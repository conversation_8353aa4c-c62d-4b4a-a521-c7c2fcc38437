"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/pipeline/create");
const authentication_1 = require("../../../middleware/authentication");
const view_1 = require("../../controllers/pipeline/view");
const update_1 = require("../../controllers/pipeline/update");
const createStage_1 = require("../../controllers/pipeline/createStage");
const delete_1 = require("../../controllers/pipeline/delete");
const updateOrder_1 = require("../../controllers/pipeline/updateOrder");
const router = (0, express_1.Router)();
router.post("/", 
// authenticate,
create_1.createPipeline);
router.get("/", 
// authenticate, // Temporarily disabled for debugging
view_1.viewPipeline);
router.get("/workTypes", 
// authenticate,
view_1.viewPipelineWorkType);
router.get("/workType/:workType", 
// authenticate,
view_1.viewPipelineWorkType);
router.get("/name/:name", 
// authenticate,
view_1.viewPipelineByName);
router.get("/:id", 
// authenticate, // Temporarily disabled for debugging
view_1.viewPipelineById);
router.put("/:id", authentication_1.authenticate, update_1.updatePipeline);
router.patch("/:id/orders", 
// authenticate,
updateOrder_1.updatePipelineStageOrder);
router.post("/:id/stages", 
// authenticate,
createStage_1.addPipelineStages);
router.delete("/:id", authentication_1.authenticate, delete_1.deletePipeline);
exports.default = router;
//# sourceMappingURL=pipelineRoutes.js.map