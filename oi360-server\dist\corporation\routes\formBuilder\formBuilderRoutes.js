"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const createForm_1 = require("../../controllers/formBuilder/createForm");
const createFormFields_1 = require("../../controllers/formBuilder/createFormFields");
const getForm_1 = require("../../controllers/formBuilder/getForm");
const getAllForms_1 = require("../../controllers/formBuilder/getAllForms");
const updateForm_1 = require("../../controllers/formBuilder/updateForm");
const updateFormField_1 = require("../../controllers/formBuilder/updateFormField");
const deleteForm_1 = require("../../controllers/formBuilder/deleteForm");
const deleteFormField_1 = require("../../controllers/formBuilder/deleteFormField");
const router = (0, express_1.Router)();
router.post("/create-form", authentication_1.authenticate, createForm_1.createForm);
router.post("/create-form-field/:formId", authentication_1.authenticate, createFormFields_1.createFormField);
router.get("/get-form/:formId", authentication_1.authenticate, getForm_1.getFormWithFields);
router.get("/get-all-forms", authentication_1.authenticate, getAllForms_1.getAllFormsWithFields);
router.put("/update-form/:formId", authentication_1.authenticate, updateForm_1.updateForm);
router.put("/update-form-field/:fieldId", authentication_1.authenticate, updateFormField_1.updateFormField);
router.delete("/delete-form/:formId", authentication_1.authenticate, deleteForm_1.deleteForm);
router.delete("/delete-form-field/:fieldId", authentication_1.authenticate, deleteFormField_1.deleteFormField);
exports.default = router;
//# sourceMappingURL=formBuilderRoutes.js.map