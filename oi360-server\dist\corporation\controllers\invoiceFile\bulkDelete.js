"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkDeleteInvoiceFiles = void 0;
const helpers_1 = require("../../../utils/helpers");
const bulkDeleteInvoiceFiles = async (req, res) => {
    try {
        const { fileIds, deletedBy } = req.body;
        if (!Array.isArray(fileIds) || fileIds.length === 0 || !deletedBy) {
            return res.status(400).json({
                success: false,
                message: "fileIds and deletedBy are required",
            });
        }
        // Soft delete: set deletedAt and deletedBy for all invoiceFiles in fileIds
        const result = await prisma.invoiceFile.updateMany({
            where: {
                id: { in: fileIds },
                deletedAt: null,
            },
            data: {
                deletedAt: new Date(),
                deletedBy,
            },
        });
        return res.status(200).json({
            success: true,
            message: `Successfully deleted ${result.count} invoice file(s)`,
            data: result,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.bulkDeleteInvoiceFiles = bulkDeleteInvoiceFiles;
//# sourceMappingURL=bulkDelete.js.map