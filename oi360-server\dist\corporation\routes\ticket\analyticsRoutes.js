"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
// Import analytics controllers
const overview_1 = require("../../controllers/ticket/analytics/overview");
const distribution_1 = require("../../controllers/ticket/analytics/distribution");
const closureRate_1 = require("../../controllers/ticket/analytics/closureRate");
const timeAnalysis_1 = require("../../controllers/ticket/analytics/timeAnalysis");
const insight_1 = require("../../controllers/ticket/analytics/insight");
const router = (0, express_1.Router)();
router.get("/overview", 
// authenticate,
overview_1.getTicketsAnalyticsOverview);
router.get("/distribution-by-stage", 
// authenticate,
distribution_1.getTicketDistributionByStage);
router.get("/closure-rate-by-stage", 
// authenticate, 
closureRate_1.getClosureRateByStage);
router.get("/average-time-by-stage", 
// authenticate,
timeAnalysis_1.getAverageTimeByStage);
// Insights APIs
router.get("/insights/closure-rate", 
// authenticate,
insight_1.getClosureRateInsights);
router.get("/insights/resolution-time", 
// authenticate,
insight_1.getResolutionTimeInsights);
exports.default = router;
//# sourceMappingURL=analyticsRoutes.js.map