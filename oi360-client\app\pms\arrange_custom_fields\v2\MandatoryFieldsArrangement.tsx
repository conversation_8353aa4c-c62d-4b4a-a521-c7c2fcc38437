"use client";
import React, { useState, useEffect, useCallback } from "react";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  useDraggable,
  useDroppable,
  CollisionDetection,
  pointerWithin,
  rectIntersection,
} from "@dnd-kit/core";
import { TouchSensor } from "@dnd-kit/core";
import axios from "axios";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";
import { MandatoryField } from "./page";
import MandatoryFieldsPreview from "./MandatoryFieldsPreview";

interface MandatoryFieldsArrangementProps {}

const TableDraggableItem = ({
  id,
  displayName,
  span = 1,
  onSpanChange,
  onRemove,
}: {
  id: string;
  displayName: string;
  span?: number;
  onSpanChange?: (fieldName: string, newSpan: number) => void;
  onRemove?: (fieldName: string) => void;
}) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id,
      data: {
        type: "mandatory-field",
        id,
        displayName,
        span,
      },
    });

  const style: React.CSSProperties = {
    transform: transform
      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
      : undefined,
    zIndex: isDragging ? 1000 : 1,
    opacity: isDragging ? 0.7 : 1,
  };

  const handleSpanChange = (newSpan: number) => {
    if (onSpanChange) {
      onSpanChange(id, newSpan);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        w-full h-full select-none transition-all duration-200 group
        ${isDragging ? "scale-105" : ""}
      `}
    >
      <div
        className={`
          w-full h-full p-2 rounded-lg border-2 flex flex-col items-center justify-center
          min-h-[60px] text-center relative
          ${
            isDragging
              ? "border-blue-400 bg-blue-50 shadow-lg"
              : "border-gray-300 bg-white hover:border-gray-400 hover:shadow-md"
          }
        `}
        style={{ backgroundColor: "#e5e7eb" }}
      >
        {onRemove && (
          <button
            type="button"
            className="absolute top-1 left-1 text-gray-400 hover:text-red-500 z-10"
            style={{ background: "transparent", border: "none", padding: 0, cursor: "pointer" }}
            onClick={e => {
              e.stopPropagation();
              onRemove(id);
            }}
            title="Remove field"
          >
            <svg width="14" height="14" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M10 8.586l4.95-4.95a1 1 0 111.414 1.414L11.414 10l4.95 4.95a1 1 0 01-1.414 1.414L10 11.414l-4.95 4.95a1 1 0 01-1.414-1.414L8.586 10l-4.95-4.95A1 1 0 115.05 3.636L10 8.586z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        )}
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing w-full h-full flex flex-col items-center justify-center"
        >
          <span className="text-xs font-bold text-gray-900 mb-1 truncate max-w-full">
            {displayName}
          </span>
          <span className="text-xs text-gray-600">Mandatory</span>
          {span > 1 && (
            <span className="text-xs text-blue-600 font-medium">
              Spans {span} cells
            </span>
          )}
        </div>

        {!isDragging && (
          <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="flex items-center gap-1 bg-white rounded-md shadow-sm border border-gray-200 p-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (span > 1) handleSpanChange(span - 1);
                }}
                disabled={span <= 1}
                className="w-4 h-4 flex items-center justify-center text-xs bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed rounded"
                title="Decrease span"
              >
                -
              </button>
              <span className="text-xs font-medium text-gray-700 min-w-[8px] text-center">
                {span}
              </span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (span < 4) handleSpanChange(span + 1);
                }}
                disabled={span >= 4}
                className="w-4 h-4 flex items-center justify-center text-xs bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed rounded"
                title="Increase span"
              >
                +
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const TableCell = ({
  id,
  children,
  isOver,
  span = 1,
}: {
  id: string;
  children?: React.ReactNode;
  isOver: boolean;
  span?: number;
}) => {
  const { setNodeRef } = useDroppable({
    id,
    data: {
      type: "table-cell",
    },
  });

  const hasContent = !!children;

  return (
    <div
      ref={setNodeRef}
      className={`
        w-full h-[80px] border-2 border-dashed transition-all duration-200
        flex items-center justify-center p-1 relative
        ${
          isOver && hasContent
            ? "border-blue-400 bg-blue-50"
            : isOver
            ? "border-green-400 bg-green-50"
            : "border-gray-300 bg-gray-50"
        }
        ${!children ? "bg-gray-100" : ""}
      `}
      style={{
        gridColumn: span > 1 ? `span ${span}` : undefined,
      }}
    >
      {children || (
        <div className="text-gray-400 text-xs text-center">Drop here</div>
      )}

      {isOver && hasContent && (
        <div className="absolute top-1 right-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded text-center leading-none">
          Insert
        </div>
      )}
    </div>
  );
};

export const MandatoryFieldsArrangement: React.FC<
  MandatoryFieldsArrangementProps
> = () => {
  // Remove handler for mandatory fields
  const handleRemoveMandatoryField = (fieldName: string) => {
    const updatedFields = reorderedMandatoryFields.filter(f => f.name !== fieldName);
    setReorderedMandatoryFields(updatedFields);
    rebuildGridWithSpans(updatedFields);
    setHasMandatoryReordered(true);
  };
  const [reorderedMandatoryFields, setReorderedMandatoryFields] = useState<
    MandatoryField[]
  >([]);
  const [originalMandatoryFields, setOriginalMandatoryFields] = useState<
    MandatoryField[]
  >([]);
  const [hasMandatoryReordered, setHasMandatoryReordered] = useState(false);
  const [activeDragItem, setActiveDragItem] = useState<MandatoryField | null>(
    null
  );
  const [dragOverZone, setDragOverZone] = useState<string | null>(null);
  const [tableGrid, setTableGrid] = useState<(MandatoryField | null)[][]>([]);
  const [gridDimensions, setGridDimensions] = useState({ rows: 3, cols: 4 });
  const [showMinColumnsMessage, setShowMinColumnsMessage] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);

  const MAX_COLUMNS = 10;
  const MIN_COLUMNS = 3;

  const handleAddColumn = () => {
    setGridDimensions((dim) => {
      if (dim.cols >= MAX_COLUMNS) return dim;
      const newDims = { ...dim, cols: dim.cols + 1 };
      rebuildGridWithSpans(reorderedMandatoryFields, false, newDims.cols);
      return newDims;
    });
  };
  const handleRemoveColumn = () => {
    if (gridDimensions.cols > MIN_COLUMNS) {
      setGridDimensions((dim) => {
        const newDims = { ...dim, cols: dim.cols - 1 };
        rebuildGridWithSpans(reorderedMandatoryFields, false, newDims.cols);
        return newDims;
      });
    } else {
      setShowMinColumnsMessage(true);
      setTimeout(() => setShowMinColumnsMessage(false), 2000);
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const mandatoryCollisionDetection: CollisionDetection = (args) => {
    const { active, droppableContainers } = args;
    const activeData = active.data.current;

    if (activeData?.type !== "mandatory-field") {
      return [];
    }

    const validDroppables = Array.from(droppableContainers.values()).filter(
      (container) => {
        const containerData = container.data.current;
        return containerData?.type === "table-cell";
      }
    );

    const pointerCollisions = pointerWithin({
      ...args,
      droppableContainers: validDroppables,
    });

    if (pointerCollisions.length > 0) {
      return pointerCollisions;
    }

    const rectCollisions = rectIntersection({
      ...args,
      droppableContainers: validDroppables,
    });

    return rectCollisions;
  };

  useEffect(() => {
    const fetchMandatoryFields = async () => {
      try {
        const res = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/mandatory-fields`
        );
        const mandFields = res.data.mandatoryFields || [];
        const mandatoryFieldObjects: MandatoryField[] = mandFields.map(
          (field: string) => ({
            name: field.toLowerCase().replace(/\s+/g, "_"),
            displayName: field,
            span: 1,
          })
        );
        setReorderedMandatoryFields(mandatoryFieldObjects);
        setOriginalMandatoryFields(mandatoryFieldObjects);
        rebuildGridWithSpans(mandatoryFieldObjects, true);
      } catch (err) {
        console.error("Failed to fetch mandatory fields:", err);
      }
    };

    if (reorderedMandatoryFields.length === 0) {
      fetchMandatoryFields();
    } else {
      rebuildGridWithSpans(reorderedMandatoryFields);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const activeData = active.data.current;

    if (activeData?.type === "mandatory-field") {
      const field = reorderedMandatoryFields.find((f) => f.name === active.id);
      setActiveDragItem(field || null);
    }
  };

  const handleDragOver = (event: any) => {
    const { over } = event;
    setDragOverZone(over?.id || null);
  };

  const handleMandatoryDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveDragItem(null);
    setDragOverZone(null);

    if (over && over.data.current?.type === "table-cell") {
      const activeData = active.data.current;
      const overData = over.data.current;

      if (
        activeData?.type === "mandatory-field" &&
        overData?.type === "table-cell"
      ) {
        const cellId = over.id.toString();
        const [rowStr, colStr] = cellId.split("-");
        const row = parseInt(rowStr);
        const col = parseInt(colStr);

        if (
          row >= 0 &&
          row < gridDimensions.rows &&
          col >= 0 &&
          col < gridDimensions.cols
        ) {
          const draggedField = reorderedMandatoryFields.find(
            (f) => f.name === active.id
          );
          if (draggedField) {
            setTableGrid((prevGrid) => {
              const newGrid = prevGrid.map((row) => [...row]);

              let fromRow = -1,
                fromCol = -1;
              for (let r = 0; r < gridDimensions.rows; r++) {
                for (let c = 0; c < gridDimensions.cols; c++) {
                  if (newGrid[r][c]?.name === draggedField.name) {
                    fromRow = r;
                    fromCol = c;
                    break;
                  }
                }
                if (fromRow !== -1) break;
              }

              if (fromRow === row && fromCol === col) return newGrid;

              if (fromRow !== -1 && fromCol !== -1) {
                const originalField = newGrid[fromRow][fromCol];
                if (originalField) {
                  const fieldSpan = originalField.span || 1;
                  for (
                    let spanCol = fromCol;
                    spanCol < fromCol + fieldSpan &&
                    spanCol < gridDimensions.cols;
                    spanCol++
                  ) {
                    newGrid[fromRow][spanCol] = null;
                  }
                }
              }

              const targetCell = newGrid[row][col];

              if (targetCell === null || isSpanPlaceholder(targetCell)) {
                newGrid[row][col] = draggedField;

                const fieldSpan = draggedField.span || 1;
                for (
                  let spanCol = col + 1;
                  spanCol < col + fieldSpan && spanCol < gridDimensions.cols;
                  spanCol++
                ) {
                  newGrid[row][spanCol] = {
                    ...draggedField,
                    name: `${draggedField.name}_span_${spanCol}`,
                  } as MandatoryField;
                }
              } else {
                const fieldsToDisplace: {
                  field: MandatoryField;
                  newRow: number;
                  newCol: number;
                }[] = [];

                let currentRow = row;
                let currentCol = col;

                while (currentRow < gridDimensions.rows) {
                  while (currentCol < gridDimensions.cols) {
                    const cellField = newGrid[currentRow][currentCol];
                    if (cellField && !isSpanPlaceholder(cellField)) {
                      const fieldSpan = cellField.span || 1;
                      for (
                        let spanCol = currentCol;
                        spanCol < currentCol + fieldSpan &&
                        spanCol < gridDimensions.cols;
                        spanCol++
                      ) {
                        newGrid[currentRow][spanCol] = null;
                      }

                      let nextRow = currentRow;
                      let nextCol = currentCol + fieldSpan;

                      if (nextCol >= gridDimensions.cols) {
                        nextRow++;
                        nextCol = 0;
                      }
                      while (nextRow >= newGrid.length) {
                        newGrid.push(Array(gridDimensions.cols).fill(null));
                      }

                      fieldsToDisplace.push({
                        field: cellField,
                        newRow: nextRow,
                        newCol: nextCol,
                      });
                      currentCol = nextCol;
                      currentRow = nextRow;
                    } else {
                      currentCol++;
                    }
                  }
                  currentRow++;
                  currentCol = 0;
                }

                newGrid[row][col] = draggedField;
                const fieldSpan = draggedField.span || 1;
                for (
                  let spanCol = col + 1;
                  spanCol < col + fieldSpan && spanCol < gridDimensions.cols;
                  spanCol++
                ) {
                  newGrid[row][spanCol] = {
                    ...draggedField,
                    name: `${draggedField.name}_span_${spanCol}`,
                  } as MandatoryField;
                }

                for (const { field, newRow, newCol } of fieldsToDisplace) {
                  newGrid[newRow][newCol] = field;
                  const displacedFieldSpan = field.span || 1;
                  for (
                    let spanCol = newCol + 1;
                    spanCol < newCol + displacedFieldSpan &&
                    spanCol < gridDimensions.cols;
                    spanCol++
                  ) {
                    newGrid[newRow][spanCol] = {
                      ...field,
                      name: `${field.name}_span_${spanCol}`,
                    } as MandatoryField;
                  }
                }
              }

              if (newGrid.length > gridDimensions.rows) {
                setGridDimensions((prev) => ({
                  ...prev,
                  rows: newGrid.length,
                }));
              }

              return newGrid;
            });
            setHasMandatoryReordered(true);
          }
        }
      }
    } else {
      console.log("Mandatory field dragged outside valid drop area - ignoring");
    }
  };

  const handleMandatoryReset = () => {
    const resetFields = originalMandatoryFields.map((field) => ({
      ...field,
      span: 1,
    }));

    setReorderedMandatoryFields(resetFields);
    setHasMandatoryReordered(false);
    rebuildGridWithSpans(resetFields, true);
  };

  const handleSpanChange = (fieldName: string, newSpan: number) => {
    setReorderedMandatoryFields((prev) =>
      prev.map((field) =>
        field.name === fieldName ? { ...field, span: newSpan } : field
      )
    );

    const updatedFields = reorderedMandatoryFields.map((field) =>
      field.name === fieldName ? { ...field, span: newSpan } : field
    );
    rebuildGridWithSpans(updatedFields);
    setHasMandatoryReordered(true);
  };

  const rebuildGridWithSpans = useCallback(
    (fields: MandatoryField[], isReset = false, overrideCols?: number) => {
      const COLS = overrideCols || gridDimensions.cols;
      const grid: (MandatoryField | null)[][] = [];

      let sortedFields: MandatoryField[];

      if (isReset) {
        sortedFields = [...fields].sort((a, b) =>
          a.displayName.localeCompare(b.displayName)
        );
      } else {
        const fieldPositions = new Map<string, { row: number; col: number }>();
        tableGrid.forEach((row, rowIndex) => {
          row.forEach((cell, colIndex) => {
            if (cell && !cell.name.includes("_span_")) {
              fieldPositions.set(cell.name, { row: rowIndex, col: colIndex });
            }
          });
        });

        sortedFields = [...fields].sort((a, b) => {
          const posA = fieldPositions.get(a.name) || { row: 0, col: 0 };
          const posB = fieldPositions.get(b.name) || { row: 0, col: 0 };
          if (posA.row !== posB.row) {
            return posA.row - posB.row;
          }
          return posA.col - posB.col;
        });
      }

      let currentRow = 0;
      let currentCol = 0;
      for (const field of sortedFields) {
        const fieldSpan = Math.min(field.span || 1, COLS);
        let placed = false;
        while (!placed) {
          // Ensure grid has the current row
          while (grid.length <= currentRow) {
            grid.push(Array(COLS).fill(null));
          }
          // Find first available col in currentRow
          currentCol = 0;
          while (currentCol <= COLS - fieldSpan) {
            let canPlace = true;
            for (
              let spanCol = currentCol;
              spanCol < currentCol + fieldSpan;
              spanCol++
            ) {
              if (grid[currentRow][spanCol] !== null) {
                canPlace = false;
                break;
              }
            }
            if (canPlace) {
              // Place the field
              grid[currentRow][currentCol] = { ...field, span: fieldSpan };
              // Place span placeholders
              for (
                let spanCol = currentCol + 1;
                spanCol < currentCol + fieldSpan;
                spanCol++
              ) {
                grid[currentRow][spanCol] = {
                  ...field,
                  name: `${field.name}_span_${spanCol}`,
                } as MandatoryField;
              }
              placed = true;
              break;
            }
            currentCol++;
          }
          if (!placed) {
            // Could not place in this row, move to next row
            currentRow++;
          }
        }
      }
      setGridDimensions((dim) => ({ ...dim, rows: Math.max(grid.length, 1) }));
      setTableGrid(grid);
    },
    [tableGrid, gridDimensions.cols]
  );

  const isSpanPlaceholder = (cell: MandatoryField | null): boolean => {
    return cell?.name?.includes("_span_") || false;
  };

  return (
    <div className="space-y-4">
      <div className="text-center relative">
        <h2 className="text-xl font-bold text-gray-900 mb-2">
          Mandatory Fields
        </h2>
        <p className="text-sm text-gray-600">
          Drag fields to empty cells or insert between existing ones (same for
          all clients)
        </p>
        <div className="flex justify-center gap-2 mt-2">
          <button
            onClick={handleAddColumn}
            className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded font-medium text-sm"
            type="button"
            disabled={gridDimensions.cols >= MAX_COLUMNS}
          >
            + Add Column
          </button>
          <button
            onClick={handleRemoveColumn}
            className="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded font-medium text-sm"
            type="button"
          >
            – Remove Column
          </button>
          <span className="ml-4 text-gray-500 text-sm">
            Columns: {gridDimensions.cols}
          </span>
        </div>
      </div>
      {gridDimensions.cols >= MAX_COLUMNS && (
        <div className="flex justify-center mt-1">
          <span className="text-red-500 text-xs">
            Maximum {MAX_COLUMNS} columns allowed.
          </span>
        </div>
      )}
      {showMinColumnsMessage && (
        <div className="flex justify-center mt-1">
          <span className="text-red-500 text-xs">
            Minimum {MIN_COLUMNS} columns required.
          </span>
        </div>
      )}

      {tableGrid.length > 0 ? (
        <DndContext
          sensors={sensors}
          collisionDetection={mandatoryCollisionDetection}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleMandatoryDragEnd}
        >
          <div
            id="mandatory-fields-table-container"
            className="bg-white rounded-2xl border-2 border-gray-200 p-4 shadow-lg"
            style={{
              position: "relative",
              overflowX: "auto",
              overflowY: "hidden",
              maxWidth: "100%",
              paddingRight: 24,
              minWidth: 350,
            }}
          >
            <div
              className="grid gap-2"
              style={{
                gridTemplateColumns: `repeat(${gridDimensions.cols}, 1fr)`,
                position: "relative",
                minWidth:
                  gridDimensions.cols > 4
                    ? `${gridDimensions.cols * 220}px`
                    : undefined,
                paddingRight: "1.5rem",
              }}
            >
              {tableGrid.map((row, rowIndex) =>
                row.map((cell, colIndex) => {
                  if (isSpanPlaceholder(cell)) {
                    return null;
                  }

                  return (
                    <TableCell
                      key={`${rowIndex}-${colIndex}`}
                      id={`${rowIndex}-${colIndex}`}
                      isOver={dragOverZone === `${rowIndex}-${colIndex}`}
                      span={cell?.span || 1}
                    >
                      {cell && !isSpanPlaceholder(cell) && (
                        <TableDraggableItem
                          id={cell.name}
                          displayName={cell.displayName}
                          span={cell.span || 1}
                          onSpanChange={handleSpanChange}
                          onRemove={handleRemoveMandatoryField}
                        />
                      )}
                    </TableCell>
                  );
                })
              )}
            </div>
          </div>

          <DragOverlay>
            {activeDragItem ? (
              <div className="transform rotate-3 scale-110">
                <TableDraggableItem
                  id={activeDragItem.name}
                  displayName={activeDragItem.displayName}
                  span={activeDragItem.span || 1}
                  onSpanChange={handleSpanChange}
                />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      ) : (
        <div className="bg-white rounded-2xl border-2 border-gray-200 p-4 shadow-lg">
          <div className="text-center py-12">
            <div className="animate-spin w-8 h-8 border-4 border-gray-300 border-t-blue-500 rounded-full mx-auto mb-4"></div>
            <p className="text-gray-500 text-sm">Loading mandatory fields...</p>
          </div>
        </div>
      )}

  {hasMandatoryReordered && (
    <div className="flex justify-center gap-2">
      <button
        onClick={handleMandatoryReset}
        className="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200 hover:scale-105"
      >
        Reset Mandatory Fields
      </button>
      <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
        <DialogTrigger asChild>
          <button
            className="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200 hover:scale-105"
            type="button"
          >
            Preview
          </button>
        </DialogTrigger>
        <DialogContent
          className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[90vw] sm:max-w-6xl max-h-[90vh] overflow-hidden
          data-[state=open]:animate-none data-[state=closed]:animate-none"
        >
          <button
            type="button"
            className="absolute right-4 top-4 rounded-sm opacity-70 transition hover:opacity-100 focus:outline-none"
            aria-label="Close"
            onClick={() => setPreviewOpen(false)}
          >
            <svg
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 8.586l4.95-4.95a1 1 0 111.414 1.414L11.414 10l4.95 4.95a1 1 0 01-1.414 1.414L10 11.414l-4.95 4.95a1 1 0 01-1.414-1.414L8.586 10l-4.95-4.95A1 1 0 015.05 3.636L10 8.586z"
                clipRule="evenodd"
              />
            </svg>
          </button>
          <DialogTitle>Preview</DialogTitle>
          <div className="mt-2 overflow-auto" style={{ maxHeight: "calc(90vh - 4.5rem)" }}>
            <MandatoryFieldsPreview tableGrid={tableGrid} />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )}
    </div>
  );
};