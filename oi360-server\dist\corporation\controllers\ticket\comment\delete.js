"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteComment = void 0;
const prismaClient_1 = __importDefault(require("../../../../utils/prismaClient"));
const helpers_1 = require("../../../../utils/helpers");
const deleteComment = async (req, res) => {
    try {
        const { id: commentId } = req.params;
        const { deletedBy } = req.body;
        if (!commentId) {
            return res.status(400).json({
                success: false,
                message: "Comment ID is required",
            });
        }
        // Only use deletedBy from req.body or fallback to 'system'
        let username = deletedBy || "system";
        const comment = await prismaClient_1.default.comment.update({
            where: { id: commentId },
            data: {
                deletedAt: new Date(),
                deletedBy: username,
            },
        });
        return res.status(200).json({
            success: true,
            message: "Comment deleted successfully",
            data: comment,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.deleteComment = deleteComment;
//# sourceMappingURL=delete.js.map