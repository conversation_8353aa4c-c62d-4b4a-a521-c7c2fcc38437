"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createuserClients = void 0;
const createuserClients = async (req, res) => {
    const { userId, clientIds } = req.body;
    if (!userId || !Array.isArray(clientIds)) {
        return res.status(400).json({ success: false, error: "Invalid input" });
    }
    try {
        const mappings = clientIds.map((clientId) => ({
            userId: Number(userId),
            clientId: Number(clientId),
        }));
        await prisma.userClients.createMany({
            data: mappings,
            skipDuplicates: true,
        });
        return res.status(200).json({ success: true, message: "Clients assigned to user successfully" });
    }
    catch (error) {
        console.error("Error assigning clients:", error);
        return res.status(500).json({ success: false, error: "An error occurred while creating the user clients" });
    }
};
exports.createuserClients = createuserClients;
//# sourceMappingURL=create.js.map