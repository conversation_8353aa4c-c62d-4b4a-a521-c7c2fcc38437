"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkBulkUniqueInvoiceFiles = exports.findDuplicateInvoiceFiles = void 0;
const helpers_1 = require("../../../utils/helpers");
const findDuplicateInvoiceFiles = async (entries) => {
    if (!entries.length)
        return [];
    return prisma.invoiceFile.findMany({
        where: {
            deletedAt: null,
            OR: entries.map((entry) => ({
                carrierId: entry.carrier,
                date: new Date(entry.date),
                fileName: entry.fileName.trim(),
                noOfPages: entry.noOfPages,
            })),
        },
        select: {
            carrierId: true,
            fileName: true,
            noOfPages: true,
            date: true,
        },
    });
};
exports.findDuplicateInvoiceFiles = findDuplicateInvoiceFiles;
const checkBulkUniqueInvoiceFiles = async (req, res) => {
    try {
        const { entries } = req.query;
        let parsedEntries;
        try {
            parsedEntries = typeof entries === "string" ? JSON.parse(entries) : [];
        }
        catch {
            return res
                .status(400)
                .json({ message: "Invalid entries format. Must be valid JSON." });
        }
        if (!Array.isArray(parsedEntries) || parsedEntries.length === 0) {
            return res
                .status(400)
                .json({ message: "Entries must be a non-empty array." });
        }
        // Validate entries
        const invalid = parsedEntries.filter((entry) => !entry.carrier ||
            !entry.date ||
            !entry.fileName ||
            typeof entry.noOfPages !== "number" ||
            entry.noOfPages <= 0);
        if (invalid.length > 0) {
            return res.status(400).json({
                message: "Each entry must include carrier, date, fileName, and a positive noOfPages.",
                invalidCount: invalid.length,
            });
        }
        const duplicates = await (0, exports.findDuplicateInvoiceFiles)(parsedEntries.map((entry) => ({
            carrier: Number(entry.carrier),
            date: new Date(entry.date),
            fileName: entry.fileName.trim(),
            noOfPages: Number(entry.noOfPages),
        })));
        if (duplicates.length > 0) {
            const conflictList = duplicates.map((file) => `File "${file.fileName}" with ${file.noOfPages} pages on ${file.date.toISOString().split("T")[0]} (Carrier ID: ${file.carrierId})`);
            return res.status(409).json({
                message: "Duplicate invoice files found.",
                duplicates: conflictList,
            });
        }
        return res.status(200).json({
            message: "All entries are unique. You may proceed.",
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.checkBulkUniqueInvoiceFiles = checkBulkUniqueInvoiceFiles;
//# sourceMappingURL=checkUniqueFiles.js.map