"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTicketDistributionByStage = void 0;
const helpers_1 = require("../../../../utils/helpers");
const getTicketDistributionByStage = async (req, res) => {
    try {
        const parseLocalDate = (s) => {
            const [y, m, d] = s.split("-").map((v) => parseInt(v, 10));
            return new Date(y, (m || 1) - 1, d || 1);
        };
        const toStartOfDay = (d) => {
            const x = new Date(d);
            x.setHours(0, 0, 0, 0);
            return x;
        };
        const toEndOfDay = (d) => {
            const x = new Date(d);
            x.setHours(23, 59, 59, 999);
            return x;
        };
        const { dateFrom, dateTo, tags, assignedTo, stageId, priority, dueDateFrom, dueDateTo, } = req.query;
        const ticketWhereClause = {
            deletedAt: null,
        };
        if ((dateFrom || dateTo) && !(dueDateFrom || dueDateTo)) {
            ticketWhereClause.createdAt = {};
            if (dateFrom) {
                ticketWhereClause.createdAt.gte = toStartOfDay(parseLocalDate(dateFrom));
            }
            if (dateTo) {
                ticketWhereClause.createdAt.lte = toEndOfDay(parseLocalDate(dateTo));
            }
        }
        if (tags) {
            const tagArray = tags.split(',').map((tag) => tag.trim());
            ticketWhereClause.tags = {
                hasSome: tagArray,
            };
        }
        if (priority) {
            ticketWhereClause.priority = priority;
        }
        const stageWhereClause = {};
        if (assignedTo) {
            stageWhereClause.assignedTo = assignedTo;
        }
        if (stageId) {
            stageWhereClause.pipelineStageId = stageId;
        }
        const tickets = await prisma.ticket.findMany({
            where: {
                ...ticketWhereClause,
                ...(Object.keys(stageWhereClause).length > 0 && {
                    stages: {
                        some: stageWhereClause,
                    },
                }),
            },
            include: {
                stages: {
                    include: {
                        pipelineStage: true,
                    },
                    orderBy: {
                        createdAt: 'asc',
                    },
                },
                pipeline: {
                    include: {
                        stages: {
                            orderBy: { order: 'asc' },
                        },
                    },
                },
            },
        });
        let filteredTickets = tickets;
        if (dueDateFrom || dueDateTo) {
            const fromDate = dueDateFrom ? toStartOfDay(parseLocalDate(dueDateFrom)) : null;
            const toDate = dueDateTo ? toEndOfDay(parseLocalDate(dueDateTo)) : null;
            filteredTickets = tickets.filter((ticket) => {
                if (!ticket.currentStageId || !ticket.stages?.length)
                    return false;
                const currentStage = ticket.stages.find((s) => String(s.pipelineStageId) === String(ticket.currentStageId));
                if (!currentStage)
                    return false;
                const dueAt = currentStage.dueAt ? new Date(currentStage.dueAt) : null;
                if (!dueAt)
                    return false;
                if (fromDate && dueAt < fromDate)
                    return false;
                if (toDate && dueAt > toDate)
                    return false;
                return true;
            });
        }
        const pipelineIds = [...new Set(filteredTickets.map(ticket => ticket.pipelineId).filter(Boolean))];
        const allStages = await prisma.pipelineStage.findMany({
            where: {
                pipelineId: {
                    in: pipelineIds,
                },
                deletedAt: null,
            },
            orderBy: [
                { pipelineId: 'asc' },
                { order: 'asc' },
            ],
        });
        const stageDistribution = new Map();
        allStages.forEach(stage => {
            stageDistribution.set(String(stage.id), {
                stageId: stage.id,
                stageName: stage.name || 'Unnamed Stage',
                stageOrder: stage.order,
                pipelineId: stage.pipelineId || '',
                ticketCount: 0,
            });
        });
        filteredTickets.forEach(ticket => {
            const key = ticket.currentStageId != null ? String(ticket.currentStageId) : null;
            if (key && stageDistribution.has(key)) {
                const stageData = stageDistribution.get(key);
                stageData.ticketCount++;
            }
        });
        const totalTickets = filteredTickets.length;
        const distributionArray = Array.from(stageDistribution.values()).map(stage => ({
            stageId: stage.stageId,
            stageName: stage.stageName,
            stageOrder: stage.stageOrder,
            ticketCount: stage.ticketCount,
            percentage: totalTickets > 0 ? Math.round((stage.ticketCount / totalTickets) * 10000) / 100 : 0,
        }));
        distributionArray.sort((a, b) => a.stageOrder - b.stageOrder);
        return res.status(200).json({
            success: true,
            data: distributionArray,
        });
    }
    catch (error) {
        console.error("Error in getTicketDistributionByStage:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getTicketDistributionByStage = getTicketDistributionByStage;
//# sourceMappingURL=distribution.js.map