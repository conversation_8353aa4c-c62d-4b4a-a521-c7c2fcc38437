"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteFormField = void 0;
const client_1 = require("@prisma/client");
const helpers_1 = require("../../../utils/helpers");
const prisma = new client_1.PrismaClient();
const deleteFormField = async (req, res) => {
    try {
        const { fieldId } = req.params;
        const deletedBy = (req.body && req.body.deletedBy) ?? req.user?.id;
        if (!fieldId) {
            return res.status(400).json({
                success: false,
                message: "Field ID is required",
            });
        }
        const field = await prisma.formSchema.findUnique({
            where: { id: fieldId },
        });
        if (!field || field.deletedAt) {
            return res.status(404).json({
                success: false,
                message: "Field not found or already deleted",
            });
        }
        await prisma.formSchema.update({
            where: { id: fieldId },
            data: {
                deletedAt: new Date(),
                deletedBy: deletedBy,
            },
        });
        return res.status(200).json({
            success: true,
            message: "Field deleted successfully",
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error, "Failed to delete field");
    }
};
exports.deleteFormField = deleteFormField;
//# sourceMappingURL=deleteFormField.js.map