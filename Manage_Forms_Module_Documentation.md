# Manage Forms Module - Technical Documentation

## Overview

The Manage Forms Module is a comprehensive form builder and management system that allows administrators to create, edit, and manage dynamic forms with various field types. The module provides a drag-and-drop form builder interface, real-time preview capabilities, and complete CRUD operations for forms and form fields.

## Architecture Overview

### Frontend Architecture (React/Next.js)

#### Component Structure
```
oi360-client/app/pms/manage_forms/
├── page.tsx                           # Main forms management page
├── api/
│   └── formBuilder.ts                 # API integration layer
├── components/
│   ├── AllFormsSection.tsx            # Forms listing and management
│   ├── CreateFormDialog.tsx           # Form creation dialog
│   └── CreateFormSection.tsx          # Form creation section
└── create-form/
    ├── page.tsx                       # Form builder page
    ├── FormBuilder.tsx                # Main form builder component
    ├── types.ts                       # TypeScript type definitions
    └── components/
        ├── FormBuilderHeader.tsx      # Form builder header
        ├── FieldSidebar.tsx           # Draggable field components
        ├── FormPreview.tsx            # Real-time form preview
        └── FieldProperties.tsx        # Field configuration panel
```

#### Key Technologies
- **React 18** with Next.js App Router
- **HTML5 Drag and Drop API** for field placement
- **React Select** for dropdown components
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **Sonner** for toast notifications
- **TypeScript** for type safety

#### State Management
- **Local State**: React useState hooks for component-level state
- **Form Builder State**: Complex state management for form structure
- **Field Management**: Dynamic field addition, removal, and reordering
- **Preview Mode**: Toggle between builder and preview modes
- **Permission Management**: Role-based access control integration

#### Drag-and-Drop Implementation
- **Field Sidebar**: Draggable field type components
- **Form Canvas**: Drop zone for field placement
- **Field Reordering**: Drag-and-drop within form preview
- **Visual Feedback**: Hover states and drop indicators

### Backend Architecture (Node.js/Express/Prisma)

#### API Endpoints
```
Base URL: /api/form-builder/

Form Management:
├── POST   /create-form                    # Create new form
├── GET    /get-form/:formId               # Fetch specific form with fields
├── GET    /get-all-forms                  # Fetch all forms with fields
├── PUT    /update-form/:formId            # Update form metadata
└── DELETE /delete-form/:formId            # Soft delete form

Form Field Management:
├── POST   /create-form-field/:formId      # Add field to form
├── PUT    /update-form-field/:fieldId     # Update field properties
└── DELETE /delete-form-field/:fieldId     # Soft delete field
```

#### Controller Structure
```
oi360-server/src/corporation/controllers/formBuilder/
├── createForm.ts                      # Form creation logic
├── getForm.ts                         # Single form retrieval
├── getAllForms.ts                     # All forms retrieval
├── updateForm.ts                      # Form update operations
├── deleteForm.ts                      # Form deletion (soft delete)
├── createFormFields.ts                # Field creation logic
├── updateFormField.ts                 # Field update operations
└── deleteFormField.ts                 # Field deletion (soft delete)
```

#### Business Logic

##### Form Management
- **Form Creation**: Validates form name and description
- **Duplicate Prevention**: Checks for existing forms with same details
- **Soft Deletion**: Marks forms as deleted instead of hard deletion
- **Nested Field Creation**: Creates form with initial fields in single transaction

##### Form Field Management
- **Field Type Validation**: Ensures valid FormFieldType enum values
- **Order Management**: Maintains field ordering within forms
- **Options Handling**: Stores field options as JSON for select/radio/checkbox fields
- **Settings Storage**: Flexible JSON storage for field-specific settings

##### Data Integrity
- **Transaction Safety**: Uses Prisma transactions for atomic operations
- **Cascade Deletion**: Soft deletes all fields when form is deleted
- **Validation**: Comprehensive input validation and sanitization
- **Error Handling**: Structured error responses with proper HTTP codes

### Database Schema

#### Core Models

##### Form Model
```prisma
model Form {
  id          String       @id @default(uuid())
  name        String       @map("name")
  description String?      @map("description")
  createdAt   DateTime     @default(now()) @map("created_at")
  createdBy   Int?         @map("created_by")
  updatedAt   DateTime?    @updatedAt @map("updated_at")
  updatedBy   Int?         @map("updated_by")
  deletedAt   DateTime?    @map("deleted_at")
  deletedBy   Int?         @map("deleted_by")
  fields      FormSchema[]
}
```

##### FormSchema Model (Form Fields)
```prisma
model FormSchema {
  id          String        @id @default(uuid())
  formId      String        @map("form_id")
  label       String        @map("label")
  type        FormFieldType @map("type")
  placeholder String?       @map("placeholder")
  required    Boolean       @map("required")    @default(false)
  order       Int           @map("order")       @default(0)
  options     Json?         @map("options")
  settings    Json?         @map("settings")
  createdAt   DateTime      @map("created_at")  @default(now())
  createdBy   Int?          @map("created_by")
  updatedAt   DateTime?     @map("updated_at")  @updatedAt
  updatedBy   Int?          @map("updated_by")
  deletedAt   DateTime?     @map("deleted_at")
  deletedBy   Int?          @map("deleted_by")
  
  form Form @relation(fields: [formId], references: [id], onDelete: Cascade)
}
```

##### FormFieldType Enum
```prisma
enum FormFieldType {
  TEXT
  TEXTAREA
  NUMBER
  EMAIL
  DROPDOWN
  RADIO
  CHECKBOX
  DATE
  TIME
  FILE
}
```

#### Relationships
- **One-to-Many**: Form → FormSchema (fields)
- **Foreign Key**: FormSchema.formId → Form.id
- **Cascade Delete**: When form is deleted, all fields are soft deleted

#### Data Flow
1. **Form Creation**: Form metadata stored in Form table
2. **Field Addition**: Fields stored in FormSchema table with formId reference
3. **Field Ordering**: Maintained via order column in FormSchema
4. **Options Storage**: Complex field options stored as JSON
5. **Soft Deletion**: deletedAt timestamp instead of hard deletion

## Feature Specifications

### Form Builder Features
- **Visual Form Builder**: Drag-and-drop interface for form creation
- **Real-time Preview**: Live preview of form as it's being built
- **Field Types**: Support for 10+ field types (text, email, number, date, etc.)
- **Field Configuration**: Customizable properties for each field type
- **Form Theming**: Basic theme customization options
- **Responsive Design**: Mobile-friendly form builder interface

### Form Management Features
- **Form Listing**: Grid and list view of all forms
- **Search and Filter**: Search by form name, creator, or status
- **Sorting Options**: Sort by date, name, responses, or creator
- **Form Actions**: Edit, delete, duplicate, and view forms
- **Permission Control**: Role-based access to form operations
- **Bulk Operations**: Select and perform actions on multiple forms

### Field Type Specifications

#### Basic Input Fields
- **Text**: Single-line text input with placeholder support
- **Email**: Email validation with built-in format checking
- **Number**: Numeric input with min/max validation
- **Textarea**: Multi-line text input for longer content
- **Date**: Date picker with format validation
- **Time**: Time picker for time selection

#### Selection Fields
- **Dropdown**: Single selection from predefined options
- **Radio**: Single selection with radio button interface
- **Checkbox**: Multiple selections with checkbox interface

#### Advanced Fields
- **File Upload**: File attachment with type restrictions
- **Phone**: Phone number input with format validation

### API Integration Patterns

#### Form Creation
```typescript
const createForm = async (formData: {
  name: string;
  description?: string;
  fields?: FormField[];
}) => {
  return await formSubmit('/api/form-builder/create-form', 'POST', formData);
};
```

#### Field Management
```typescript
const addField = async (formId: string, fieldData: FormField) => {
  return await formSubmit(`/api/form-builder/create-form-field/${formId}`, 'POST', fieldData);
};

const updateField = async (fieldId: string, updates: Partial<FormField>) => {
  return await formSubmit(`/api/form-builder/update-form-field/${fieldId}`, 'PUT', updates);
};
```