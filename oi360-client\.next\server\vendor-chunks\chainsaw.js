/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/chainsaw";
exports.ids = ["vendor-chunks/chainsaw"];
exports.modules = {

/***/ "(ssr)/./node_modules/chainsaw/index.js":
/*!****************************************!*\
  !*** ./node_modules/chainsaw/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Traverse = __webpack_require__(/*! traverse */ \"(ssr)/./node_modules/traverse/index.js\");\nvar EventEmitter = (__webpack_require__(/*! events */ \"events\").EventEmitter);\n\nmodule.exports = Chainsaw;\nfunction Chainsaw (builder) {\n    var saw = Chainsaw.saw(builder, {});\n    var r = builder.call(saw.handlers, saw);\n    if (r !== undefined) saw.handlers = r;\n    saw.record();\n    return saw.chain();\n};\n\nChainsaw.light = function ChainsawLight (builder) {\n    var saw = Chainsaw.saw(builder, {});\n    var r = builder.call(saw.handlers, saw);\n    if (r !== undefined) saw.handlers = r;\n    return saw.chain();\n};\n\nChainsaw.saw = function (builder, handlers) {\n    var saw = new EventEmitter;\n    saw.handlers = handlers;\n    saw.actions = [];\n\n    saw.chain = function () {\n        var ch = Traverse(saw.handlers).map(function (node) {\n            if (this.isRoot) return node;\n            var ps = this.path;\n\n            if (typeof node === 'function') {\n                this.update(function () {\n                    saw.actions.push({\n                        path : ps,\n                        args : [].slice.call(arguments)\n                    });\n                    return ch;\n                });\n            }\n        });\n\n        process.nextTick(function () {\n            saw.emit('begin');\n            saw.next();\n        });\n\n        return ch;\n    };\n\n    saw.pop = function () {\n        return saw.actions.shift();\n    };\n\n    saw.next = function () {\n        var action = saw.pop();\n\n        if (!action) {\n            saw.emit('end');\n        }\n        else if (!action.trap) {\n            var node = saw.handlers;\n            action.path.forEach(function (key) { node = node[key] });\n            node.apply(saw.handlers, action.args);\n        }\n    };\n\n    saw.nest = function (cb) {\n        var args = [].slice.call(arguments, 1);\n        var autonext = true;\n\n        if (typeof cb === 'boolean') {\n            var autonext = cb;\n            cb = args.shift();\n        }\n\n        var s = Chainsaw.saw(builder, {});\n        var r = builder.call(s.handlers, s);\n\n        if (r !== undefined) s.handlers = r;\n\n        // If we are recording...\n        if (\"undefined\" !== typeof saw.step) {\n            // ... our children should, too\n            s.record();\n        }\n\n        cb.apply(s.chain(), args);\n        if (autonext !== false) s.on('end', saw.next);\n    };\n\n    saw.record = function () {\n        upgradeChainsaw(saw);\n    };\n\n    ['trap', 'down', 'jump'].forEach(function (method) {\n        saw[method] = function () {\n            throw new Error(\"To use the trap, down and jump features, please \"+\n                            \"call record() first to start recording actions.\");\n        };\n    });\n\n    return saw;\n};\n\nfunction upgradeChainsaw(saw) {\n    saw.step = 0;\n\n    // override pop\n    saw.pop = function () {\n        return saw.actions[saw.step++];\n    };\n\n    saw.trap = function (name, cb) {\n        var ps = Array.isArray(name) ? name : [name];\n        saw.actions.push({\n            path : ps,\n            step : saw.step,\n            cb : cb,\n            trap : true\n        });\n    };\n\n    saw.down = function (name) {\n        var ps = (Array.isArray(name) ? name : [name]).join('/');\n        var i = saw.actions.slice(saw.step).map(function (x) {\n            if (x.trap && x.step <= saw.step) return false;\n            return x.path.join('/') == ps;\n        }).indexOf(true);\n\n        if (i >= 0) saw.step += i;\n        else saw.step = saw.actions.length;\n\n        var act = saw.actions[saw.step - 1];\n        if (act && act.trap) {\n            // It's a trap!\n            saw.step = act.step;\n            act.cb();\n        }\n        else saw.next();\n    };\n\n    saw.jump = function (step) {\n        saw.step = step;\n        saw.next();\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chainsaw/index.js\n");

/***/ })

};
;