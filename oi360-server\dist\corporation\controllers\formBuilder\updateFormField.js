"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateFormField = void 0;
const client_1 = require("@prisma/client");
const helpers_1 = require("../../../utils/helpers");
const prisma = new client_1.PrismaClient();
const updateFormField = async (req, res) => {
    try {
        const { fieldId } = req.params;
        const { label, type, placeholder, required, order, options, settings, updatedBy } = req.body;
        if (!fieldId) {
            return res.status(400).json({
                success: false,
                message: "Field ID is required",
            });
        }
        const fieldExists = await prisma.formSchema.findFirst({
            where: {
                id: fieldId,
                deletedAt: null,
            },
        });
        if (!fieldExists) {
            return res.status(404).json({
                success: false,
                message: `Field with ID ${fieldId} not found`,
            });
        }
        const updatedField = await prisma.formSchema.update({
            where: { id: fieldId },
            data: {
                ...(label && { label }),
                ...(type && { type: type }),
                placeholder: placeholder ?? null,
                required: required ?? false,
                order: order ?? 0,
                options: options ?? null,
                settings: settings ?? null,
                ...(updatedBy && { updatedBy: Number(updatedBy) }),
            },
        });
        return res.status(200).json({
            success: true,
            message: "Field updated successfully",
            data: updatedField,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error, "Failed to update form field");
    }
};
exports.updateFormField = updateFormField;
//# sourceMappingURL=updateFormField.js.map