"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-isomorphic-layout-effect";
exports.ids = ["vendor-chunks/use-isomorphic-layout-effect"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nvar isClient = typeof document !== 'undefined';\n\nvar noop = function noop() {};\nvar index = isClient ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : noop;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWlzb21vcnBoaWMtbGF5b3V0LWVmZmVjdC9kaXN0L3VzZS1pc29tb3JwaGljLWxheW91dC1lZmZlY3QuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3Qzs7QUFFeEM7O0FBRUE7QUFDQSx1QkFBdUIsa0RBQWU7O0FBRVYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQvLi9ub2RlX21vZHVsZXMvdXNlLWlzb21vcnBoaWMtbGF5b3V0LWVmZmVjdC9kaXN0L3VzZS1pc29tb3JwaGljLWxheW91dC1lZmZlY3QuZXNtLmpzPzkzNGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG52YXIgaXNDbGllbnQgPSB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnO1xuXG52YXIgbm9vcCA9IGZ1bmN0aW9uIG5vb3AoKSB7fTtcbnZhciBpbmRleCA9IGlzQ2xpZW50ID8gdXNlTGF5b3V0RWZmZWN0IDogbm9vcDtcblxuZXhwb3J0IHsgaW5kZXggYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\n");

/***/ })

};
;