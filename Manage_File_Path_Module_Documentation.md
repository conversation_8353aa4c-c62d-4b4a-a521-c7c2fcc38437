# Manage File Path Module - Technical Documentation

## Overview

The Manage File Path Module is a specialized configuration system that allows administrators to create and manage custom FTP file path templates for clients. The module provides a visual path builder interface, dynamic field substitution, and real-time path preview capabilities for organizing client-specific file structures.

## Architecture Overview

### Frontend Architecture (React/Next.js)

#### Component Structure
```
oi360-client/app/pms/manage_file_path/
├── page.tsx                           # Main server component with data fetching
├── FilepathForm.tsx                   # File path creation/editing form
├── PathVisualizer.tsx                 # Real-time path preview component
├── ViewFilepath.tsx                   # Data table for viewing file paths
├── AddFilepath.tsx                    # Add button component
├── UpdateFilepath.tsx                 # Update dialog component
├── Column.tsx                         # Table column definitions
├── add/
│   └── page.tsx                       # Add file path page
└── edit/
    └── [id]/
        └── page.tsx                   # Edit file path page
```

#### Key Technologies
- **React 18** with Next.js App Router
- **React Hook Form** with Zod validation
- **React Select** for dropdown components
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **React Hot Toast** for notifications
- **TypeScript** for type safety

#### State Management
- **Local State**: React useState hooks for component-level state
- **Form State**: React Hook Form for form management
- **Path Building**: Dynamic segment management for path construction
- **Client Selection**: Client dropdown state management
- **Preview State**: Real-time path preview updates

#### Path Building Implementation
- **Form-Based Interface**: Path built using dropdown selections and text inputs
- **Segment Management**: Add/remove path segments through form controls
- **Dynamic Field Substitution**: Predefined fields with runtime replacement
- **Static Text Support**: Custom static text segments via input fields
- **File Extension Handling**: Optional file extension management
- **Real-time Preview**: Live path generation and validation

### Backend Architecture (Node.js/Express/Prisma)

#### API Endpoints
```
Base URL: /api/

File Path Management:
├── POST   /custom-filepath              # Create new file path configuration
├── GET    /custom-filepath              # Fetch all file path configurations
├── GET    /custom-filepath/:clientId    # Fetch file path by client ID
├── PUT    /custom-filepath              # Update file path configuration
└── DELETE /custom-filepath              # Delete file path configuration

Utility Functions:
└── generateFilePath()                   # Generate actual file paths from templates
```

#### Controller Structure
```
oi360-server/src/corporation/controllers/customFilepath/
├── create.ts                          # File path creation logic
├── view.ts                            # File path retrieval operations
├── update.ts                          # File path update operations
└── delete.ts                          # File path deletion operations

oi360-server/src/utils/
└── generateFilePath.ts                # Path generation utility
```

#### Business Logic

##### File Path Configuration
- **Client-Specific Paths**: One file path configuration per client
- **Template System**: Dynamic field substitution in path templates
- **Field Validation**: Ensures valid field names and path structure
- **Unique Constraints**: Prevents duplicate configurations per client

##### Path Generation
- **Dynamic Substitution**: Runtime replacement of template fields
- **Date Formatting**: Automatic date and time field formatting
- **Client Data Integration**: Integration with client and carrier data
- **File Name Processing**: Automatic file name formatting and extension handling

##### Field System
- **Predefined Fields**: Standard fields like CLIENT, CARRIER, MONTH, YEAR
- **Date Fields**: RECEIVE DATE, CURRENT_DATE, CURRENT_MONTH, CURRENT_YEAR
- **File Fields**: FTP FILE NAME, FILENAME
- **Custom Fields**: Support for additional client-specific fields

### Database Schema

#### Core Models

##### ClientFTPFilePathConfig Model
```prisma
model ClientFTPFilePathConfig {
  clientId  Int      @id
  filePath  String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  client Client @relation(fields: [clientId], references: [id])

  @@map("client_ftp_file_path_config")
}
```

##### Client Model (Related)
```prisma
model Client {
  id                      Int      @id @default(autoincrement())
  client_name             String   @db.VarChar(255)
  associate_id            Int?
  // ... other client fields
  
  ClientFTPFilePathConfig ClientFTPFilePathConfig[]
  // ... other relationships
}
```

#### Relationships
- **One-to-One**: Client → ClientFTPFilePathConfig
- **Foreign Key**: ClientFTPFilePathConfig.clientId → Client.id

#### Data Flow
1. **Configuration Creation**: File path template stored for specific client
2. **Path Generation**: Template processed with runtime data substitution
3. **Field Replacement**: Dynamic fields replaced with actual values
4. **Path Validation**: Generated paths validated for correctness
5. **File Organization**: Actual files organized using generated paths

## Feature Specifications

### Path Builder Features
- **Segment-Based Path Construction**: Form-based interface for building file paths
- **Dynamic Field Selection**: Dropdown selection of predefined fields
- **Static Text Input**: Custom static text segments
- **Real-time Preview**: Live preview of generated path
- **File Extension Support**: Optional file extension configuration
- **Path Validation**: Client-side validation of path structure

### Field System Features
- **Predefined Fields**: Standard business fields (CLIENT, CARRIER, etc.)
- **Date Fields**: Automatic date formatting and substitution
- **File Fields**: File name and extension handling
- **Runtime Substitution**: Dynamic field replacement during path generation
- **Case Handling**: Automatic uppercase conversion for consistency

### Management Features
- **Client-Specific Configuration**: One configuration per client
- **CRUD Operations**: Complete create, read, update, delete functionality
- **Data Table View**: Sortable and filterable table of configurations
- **Inline Editing**: Quick edit functionality for existing paths
- **Bulk Operations**: Future support for bulk configuration management

### Available Fields

#### Business Fields
- **CLIENT**: Client name
- **ASSOCIATE**: Associate name
- **CARRIER**: Carrier name

#### Date Fields
- **MONTH**: Current or received month
- **YEAR**: Current or received year
- **CURRENT_DATE**: Current date (YYYY-MM-DD)
- **CURRENT_MONTH**: Current month name
- **CURRENT_YEAR**: Current year
- **RECEIVE DATE**: Document received date

#### File Fields
- **FTP FILE NAME**: Original FTP file name
- **FILENAME**: Processed file name
- **DATE**: Formatted date string

## API Integration Patterns

#### File Path Operations
```typescript
// Create file path configuration
const createFilePath = async (configData: {
  clientId: number;
  filePath: string;
}) => {
  return await fetch('/api/custom-filepath', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(configData)
  });
};

// Update file path configuration
const updateFilePath = async (updateData: {
  clientId: number;
  filePath: string;
  newFilePath: string;
}) => {
  return await fetch('/api/custom-filepath', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(updateData)
  });
};
```

#### Path Generation
```typescript
// Generate actual file path from template
const generateFilePath = (
  pathConfig: ClientFTPFilePathConfig,
  client: Client,
  carrier: Carrier,
  row: TrackSheetData,
  timeZone: string = "America/New_York",
  defaultExtension: string = ".pdf"
): string => {
  // Dynamic field substitution logic
  // Returns formatted file path
};
```

## Security & Permissions

### Access Control
- **Permission Required**: "manage-file-paths" for configuration management
- **Client Scoping**: Configurations scoped to specific clients
- **Role-Based Access**: Different permissions for different operations
- **Corporation Scoping**: Configurations scoped to user's corporation

### Data Validation
- **Path Structure Validation**: Ensures valid path format
- **Field Validation**: Validates field names and syntax
- **Client Validation**: Ensures valid client selection
- **Input Sanitization**: Comprehensive input validation and sanitization

## Performance Considerations

### Frontend Optimizations
- **Component Memoization**: React.memo for expensive components
- **Debounced Input**: Prevents excessive re-renders during typing
- **Lazy Loading**: Dynamic imports for heavy components
- **Form Optimization**: Efficient form state management

### Backend Optimizations
- **Database Indexing**: Proper indexes on clientId for fast lookups
- **Query Optimization**: Selective field loading with Prisma
- **Caching Strategy**: Potential for Redis caching of configurations
- **Path Generation Optimization**: Efficient string replacement algorithms

## Error Handling

### Frontend Error Handling
- **Toast Notifications**: User-friendly error messages via React Hot Toast
- **Form Validation**: Client-side validation with Zod schemas
- **Loading States**: Visual feedback during operations
- **Graceful Degradation**: Fallback UI for failed operations

### Backend Error Handling
- **Structured Responses**: Consistent error response format
- **HTTP Status Codes**: Appropriate status codes for different scenarios
- **Input Validation**: Comprehensive request validation
- **Database Error Handling**: Proper error catching and logging

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with Jest/React Testing Library
- **Integration Tests**: Path builder workflow testing
- **E2E Tests**: Complete file path configuration flows
- **Validation Tests**: Form validation and error handling

### Backend Testing
- **Unit Tests**: Controller and utility function testing
- **Integration Tests**: Database operation testing
- **API Tests**: Endpoint testing with various scenarios
- **Path Generation Tests**: Template processing and field substitution

## Deployment & Monitoring

### Deployment Pipeline
- **Environment Variables**: API endpoints and configuration
- **Build Process**: Next.js optimization and bundling
- **Database Migrations**: Prisma migration management
- **Health Checks**: API endpoint monitoring

### Monitoring & Logging
- **Error Tracking**: Frontend and backend error logging
- **Performance Monitoring**: API response times and path generation performance
- **Usage Analytics**: Configuration creation and usage tracking
- **Database Monitoring**: Query performance and connection health

## Future Enhancements

### Planned Features
- **Path Templates**: Pre-built path templates for common use cases
- **Advanced Field Types**: Custom field types and validation
- **Bulk Configuration**: Multi-client configuration management
- **Path Testing**: Test path generation with sample data
- **Import/Export**: Configuration backup and restore
- **Path Analytics**: Usage analytics and optimization recommendations

### Technical Improvements
- **Real-time Validation**: Live path validation during construction
- **Advanced Field System**: Custom field definitions and validation
- **Path Optimization**: Automatic path optimization suggestions
- **Integration APIs**: Webhook support for external system integration
- **Mobile Support**: Mobile-friendly path builder interface

## Integration Points

### File Processing System
- **Track Sheets**: Integration with track sheet file processing
- **FTP System**: Integration with FTP file management
- **Document Management**: Integration with document organization system

### Client Management
- **Client Data**: Integration with client information system
- **Carrier Data**: Integration with carrier management system
- **Associate Data**: Integration with associate management system

### Utility Functions
- **Date Processing**: Integration with date formatting utilities
- **File Processing**: Integration with file name processing utilities
- **Path Validation**: Integration with path validation systems
