"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePipelineStageOrder = void 0;
const helpers_1 = require("../../../utils/helpers");
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const updatePipelineStageOrder = async (req, res) => {
    const { id: pipelineId } = req.params;
    const stageOrderMap = req.body; // expects: { stage_id_1: order_1, stage_id_2: order_2 }
    const { updatedBy } = req.body;
    if (typeof stageOrderMap !== "object" ||
        stageOrderMap === null ||
        Object.keys(stageOrderMap).length === 0) {
        return res.status(400).json({
            success: false,
            message: "Request body must be a non-empty object of stage IDs to order numbers.",
        });
    }
    const stageIdsInRequest = Object.keys(stageOrderMap);
    for (const stageId of stageIdsInRequest) {
        if (typeof stageOrderMap[stageId] !== "number") {
            return res.status(400).json({
                success: false,
                message: `Invalid order value for stage ${stageId}. Order must be a number.`,
            });
        }
    }
    // Use frontend username or fallback to 'system'
    const username = updatedBy || "system";
    try {
        // Fetch all stages for the pipeline
        const stages = await prismaClient_1.default.pipelineStage.findMany({
            where: { pipelineId },
            select: { id: true },
        });
        const stageIds = stages.map((s) => s.id);
        // Validate all provided IDs belong to this pipeline
        const allValid = stageIdsInRequest.every((id) => stageIds.includes(id));
        if (!allValid) {
            return res.status(400).json({
                success: false,
                message: "One or more stage IDs are invalid for this pipeline",
            });
        }
        // 1. Set each stage's order to a unique negative value
        for (let i = 0; i < stageIdsInRequest.length; i++) {
            await prismaClient_1.default.pipelineStage.update({
                where: { id: stageIdsInRequest[i] },
                data: {
                    order: -1 * (i + 1),
                    updatedBy: username,
                },
            });
        }
        // 2. Set the correct order for each stage
        for (const stageId of stageIdsInRequest) {
            await prismaClient_1.default.pipelineStage.update({
                where: { id: stageId },
                data: {
                    order: stageOrderMap[stageId],
                    updatedBy: username,
                },
            });
        }
        return res.status(200).json({
            success: true,
            message: "Pipeline stage order updated successfully",
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.updatePipelineStageOrder = updatePipelineStageOrder;
//# sourceMappingURL=updateOrder.js.map