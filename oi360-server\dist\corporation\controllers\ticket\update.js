"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkUpdateTickets = exports.ticketUpdate = void 0;
const helpers_1 = require("../../../utils/helpers");
async function findCurrentTicketStage(ticketId) {
    const ticket = await prisma.ticket.findUnique({
        where: { id: ticketId },
        select: { id: true, currentStageId: true },
    });
    if (!ticket.currentStageId) {
        return null;
    }
    const query = `select * from ticket_stages where ticket_id = '${ticketId}' and pipeline_stage_id = '${ticket.currentStageId}'`;
    const result = await prisma.$queryRawUnsafe(query);
    if (result && result[0]) {
    }
    else {
    }
    return result[0];
}
async function isStateChange(ticketId, ticketStageId) {
    const ticket = await prisma.ticket.findUnique({
        where: { id: ticketId },
        select: { id: true, currentStageId: true },
    });
    return !!(ticket.currentStageId && ticket.currentStageId !== ticketStageId);
}
async function createTicketStageChangeLog(ticketId, ticketStageId, userId) {
    const ticket = await prisma.ticket.findUnique({
        where: { id: ticketId },
        select: { id: true, currentStageId: true },
    });
    return await prisma.ticketStageChangeLog.create({
        data: {
            ticketId,
            fromStage: ticket.currentStageId,
            toStage: ticketStageId,
            createdBy: userId,
        },
    });
}
async function handleStageTimeTracking(ticketId, fromStageId, toStageId, userId, assignedTo) {
    const now = new Date();
    if (fromStageId) {
        const activeTracking = await prisma.ticketStageTimeTracking.findFirst({
            where: {
                ticketId,
                stageId: fromStageId,
                endTime: null,
            },
        });
        if (activeTracking) {
            const startTime = new Date(activeTracking.startTime);
            const duration = Math.floor((now.getTime() - startTime.getTime()) / 60000);
            await prisma.ticketStageTimeTracking.update({
                where: { id: activeTracking.id },
                data: {
                    endTime: now,
                    updatedBy: userId,
                    duration,
                },
            });
        }
    }
    await prisma.ticketStageTimeTracking.create({
        data: {
            ticketId,
            stageId: toStageId,
            startTime: now,
            assignedTo: assignedTo || null,
            createdBy: userId,
        },
    });
}
async function handleTicketUpdate({ ticketId, ticketStageId, otherFields, userId, }) {
    let ticketStageChangeLog = null;
    let timeTrackingUpdated = false;
    if (await isStateChange(ticketId, ticketStageId)) {
        const ticket = await prisma.ticket.findUnique({
            where: { id: ticketId },
            select: { currentStageId: true },
        });
        await handleStageTimeTracking(ticketId, ticket?.currentStageId || null, ticketStageId, userId, otherFields.assignedTo);
        timeTrackingUpdated = true;
        const log = await createTicketStageChangeLog(ticketId, ticketStageId, userId);
        ticketStageChangeLog = {
            id: log.id,
            fromStage: log.fromStage,
            toStage: log.toStage,
            createdBy: log.createdBy,
            createdAt: log.createdAt,
        };
    }
    const ticketStageFields = {};
    if (typeof otherFields.assignedTo !== 'undefined') {
        ticketStageFields.assignedTo = otherFields.assignedTo;
        delete otherFields.assignedTo;
    }
    if (typeof otherFields.dueAt !== 'undefined') {
        ticketStageFields.dueAt = otherFields.dueAt;
        delete otherFields.dueAt;
    }
    if (Object.keys(ticketStageFields).length > 0) {
        const currentStage = await findCurrentTicketStage(ticketId);
        if (currentStage) {
            try {
                await prisma.ticketStage.update({
                    where: { id: currentStage.id },
                    data: {
                        ...ticketStageFields,
                        updatedBy: userId,
                    },
                });
            }
            catch (err) {
                console.error('Failed to update ticketStage:', err);
                throw new Error('Failed to update ticket stage: ' + (err?.message || err));
            }
        }
    }
    await prisma.ticket.update({
        where: { id: ticketId },
        data: {
            ...otherFields,
            currentStageId: ticketStageId,
            updatedBy: userId,
        },
    });
    const updatedFields = { ...otherFields, updatedBy: userId };
    return {
        status: "success",
        ticketId,
        updatedFields,
        ticketStageChangeLog,
        timeTrackingUpdated,
    };
}
const ticketUpdate = async (req, res) => {
    const { ticketId, ticketStageId, createdBy, updatedBy, ...otherFields } = req.body;
    const userId = createdBy || updatedBy || "system";
    try {
        const result = await handleTicketUpdate({
            ticketId,
            ticketStageId,
            otherFields,
            userId,
        });
        return res.status(200).json({
            success: true,
            updatedFields: result.updatedFields,
            ticketStageChangeLog: result.ticketStageChangeLog,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.ticketUpdate = ticketUpdate;
const bulkUpdateTickets = async (req, res) => {
    const updates = req.body.tickets;
    const results = [];
    for (const update of updates) {
        const { ticketId, ticketStageId, createdBy, updatedBy, ...otherFields } = update;
        try {
            const result = await handleTicketUpdate({
                ticketId,
                ticketStageId,
                otherFields,
                userId: createdBy || updatedBy || "system",
            });
            results.push(result);
        }
        catch (error) {
            results.push({
                status: "failed",
                ticketId,
                error: error.message,
                ticketStageChangeLog: null,
            });
        }
    }
    const allSucceeded = results.every((r) => r.status === "success");
    const allNotFound = results.every((r) => r.status === "not_found");
    return res.status(allNotFound ? 404 : allSucceeded ? 201 : 207).json({
        message: allNotFound
            ? "No tickets found."
            : allSucceeded
                ? "All tickets updated successfully."
                : "Some tickets could not be updated.",
        results,
    });
};
exports.bulkUpdateTickets = bulkUpdateTickets;
//# sourceMappingURL=update.js.map