// import prisma from "../models/prismaClient";
// import bcrypt from "bcryptjs";
// import {  UserType } from '@prisma/client'; 
// interface CreateUserData {
//   username: string;
//   email: string;
//   password: string;
//   user_type: UserType;
//   corporation_id: number | null;
// }
// export const createUser = async (data: CreateUserData) => {
//   const hashedPassword = await bcrypt.hash(data.password, 10);
//   return await prisma.user.create({
//     data: {
//       username: data.username,
//       email: data.email,
//       password: hashedPassword,
//       user_type: data.user_type, 
//       corporation_id: data.corporation_id, 
//     },
//   });
// };
// export const getAllUsers = async () => {
//   return await prisma.user.findMany({
//     include: {
//       corporation: true,
//       WorkReport: true,
//     },
//   });
// };
// export const updateUser = async (id: number, data: any) => {
//   // const hashedPassword = await bcrypt.hash(data.password, 10)
//   return await prisma.user.update({
//     where: { user_id: id },
//     data: {
//       username: data.username,
//       email: data.email,
//       user_type: data.user_type,
//     },
//   });
// };
// export const deleteUser = async (id: number) => {
//   return await prisma.user.delete({
//     where: { user_id: id },
//   });
// };
//# sourceMappingURL=userService.js.map