/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zip-stream";
exports.ids = ["vendor-chunks/zip-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/zip-stream/index.js":
/*!******************************************!*\
  !*** ./node_modules/zip-stream/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * ZipStream\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-zip-stream/blob/master/LICENSE}\n * @copyright (c) 2014 Chris Talkington, contributors.\n */\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar ZipArchiveOutputStream = (__webpack_require__(/*! compress-commons */ \"(ssr)/./node_modules/compress-commons/lib/compress-commons.js\").ZipArchiveOutputStream);\nvar ZipArchiveEntry = (__webpack_require__(/*! compress-commons */ \"(ssr)/./node_modules/compress-commons/lib/compress-commons.js\").ZipArchiveEntry);\n\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/zip-stream/node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @extends external:ZipArchiveOutputStream\n * @param {Object} [options]\n * @param {String} [options.comment] Sets the zip archive comment.\n * @param {Boolean} [options.forceLocalTime=false] Forces the archive to contain local file times instead of UTC.\n * @param {Boolean} [options.forceZip64=false] Forces the archive to contain ZIP64 headers.\n * @param {Boolean} [options.store=false] Sets the compression method to STORE.\n * @param {Object} [options.zlib] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n * to control compression.\n */\nvar ZipStream = module.exports = function(options) {\n  if (!(this instanceof ZipStream)) {\n    return new ZipStream(options);\n  }\n\n  options = this.options = options || {};\n  options.zlib = options.zlib || {};\n\n  ZipArchiveOutputStream.call(this, options);\n\n  if (typeof options.level === 'number' && options.level >= 0) {\n    options.zlib.level = options.level;\n    delete options.level;\n  }\n\n  if (!options.forceZip64 && typeof options.zlib.level === 'number' && options.zlib.level === 0) {\n    options.store = true;\n  }\n\n  options.namePrependSlash = options.namePrependSlash || false;\n\n  if (options.comment && options.comment.length > 0) {\n    this.setComment(options.comment);\n  }\n};\n\ninherits(ZipStream, ZipArchiveOutputStream);\n\n/**\n * Normalizes entry data with fallbacks for key properties.\n *\n * @private\n * @param  {Object} data\n * @return {Object}\n */\nZipStream.prototype._normalizeFileData = function(data) {\n  data = util.defaults(data, {\n    type: 'file',\n    name: null,\n    namePrependSlash: this.options.namePrependSlash,\n    linkname: null,\n    date: null,\n    mode: null,\n    store: this.options.store,\n    comment: ''\n  });\n\n  var isDir = data.type === 'directory';\n  var isSymlink = data.type === 'symlink';\n\n  if (data.name) {\n    data.name = util.sanitizePath(data.name);\n\n    if (!isSymlink && data.name.slice(-1) === '/') {\n      isDir = true;\n      data.type = 'directory';\n    } else if (isDir) {\n      data.name += '/';\n    }\n  }\n\n  if (isDir || isSymlink) {\n    data.store = true;\n  }\n\n  data.date = util.dateify(data.date);\n\n  return data;\n};\n\n/**\n * Appends an entry given an input source (text string, buffer, or stream).\n *\n * @param  {(Buffer|Stream|String)} source The input source.\n * @param  {Object} data\n * @param  {String} data.name Sets the entry name including internal path.\n * @param  {String} [data.comment] Sets the entry comment.\n * @param  {(String|Date)} [data.date=NOW()] Sets the entry date.\n * @param  {Number} [data.mode=D:0755/F:0644] Sets the entry permissions.\n * @param  {Boolean} [data.store=options.store] Sets the compression method to STORE.\n * @param  {String} [data.type=file] Sets the entry type. Defaults to `directory`\n * if name ends with trailing slash.\n * @param  {Function} callback\n * @return this\n */\nZipStream.prototype.entry = function(source, data, callback) {\n  if (typeof callback !== 'function') {\n    callback = this._emitErrorCallback.bind(this);\n  }\n\n  data = this._normalizeFileData(data);\n\n  if (data.type !== 'file' && data.type !== 'directory' && data.type !== 'symlink') {\n    callback(new Error(data.type + ' entries not currently supported'));\n    return;\n  }\n\n  if (typeof data.name !== 'string' || data.name.length === 0) {\n    callback(new Error('entry name must be a non-empty string value'));\n    return;\n  }\n\n  if (data.type === 'symlink' && typeof data.linkname !== 'string') {\n    callback(new Error('entry linkname must be a non-empty string value when type equals symlink'));\n    return;\n  }\n\n  var entry = new ZipArchiveEntry(data.name);\n  entry.setTime(data.date, this.options.forceLocalTime);\n\n  if (data.namePrependSlash) {\n    entry.setName(data.name, true);\n  }\n\n  if (data.store) {\n    entry.setMethod(0);\n  }\n\n  if (data.comment.length > 0) {\n    entry.setComment(data.comment);\n  }\n\n  if (data.type === 'symlink' && typeof data.mode !== 'number') {\n    data.mode = 40960; // 0120000\n  }\n\n  if (typeof data.mode === 'number') {\n    if (data.type === 'symlink') {\n      data.mode |= 40960;\n    }\n\n    entry.setUnixMode(data.mode);\n  }\n\n  if (data.type === 'symlink' && typeof data.linkname === 'string') {\n    source = Buffer.from(data.linkname);\n  }\n\n  return ZipArchiveOutputStream.prototype.entry.call(this, entry, source, callback);\n};\n\n/**\n * Finalizes the instance and prevents further appending to the archive\n * structure (queue will continue til drained).\n *\n * @return void\n */\nZipStream.prototype.finalize = function() {\n  this.finish();\n};\n\n/**\n * Returns the current number of bytes written to this stream.\n * @function ZipStream#getBytesWritten\n * @returns {Number}\n */\n\n/**\n * Compress Commons ZipArchiveOutputStream\n * @external ZipArchiveOutputStream\n * @see {@link https://github.com/archiverjs/node-compress-commons}\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zip-stream/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/zip-stream/node_modules/archiver-utils/file.js":
/*!*********************************************************************!*\
  !*** ./node_modules/zip-stream/node_modules/archiver-utils/file.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * archiver-utils\n *\n * Copyright (c) 2012-2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-archiver/blob/master/LICENSE-MIT\n */\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\n\nvar flatten = __webpack_require__(/*! lodash.flatten */ \"(ssr)/./node_modules/lodash.flatten/index.js\");\nvar difference = __webpack_require__(/*! lodash.difference */ \"(ssr)/./node_modules/lodash.difference/index.js\");\nvar union = __webpack_require__(/*! lodash.union */ \"(ssr)/./node_modules/lodash.union/index.js\");\nvar isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(ssr)/./node_modules/lodash.isplainobject/index.js\");\n\nvar glob = __webpack_require__(/*! glob */ \"(ssr)/./node_modules/glob/glob.js\");\n\nvar file = module.exports = {};\n\nvar pathSeparatorRe = /[\\/\\\\]/g;\n\n// Process specified wildcard glob patterns or filenames against a\n// callback, excluding and uniquing files in the result set.\nvar processPatterns = function(patterns, fn) {\n  // Filepaths to return.\n  var result = [];\n  // Iterate over flattened patterns array.\n  flatten(patterns).forEach(function(pattern) {\n    // If the first character is ! it should be omitted\n    var exclusion = pattern.indexOf('!') === 0;\n    // If the pattern is an exclusion, remove the !\n    if (exclusion) { pattern = pattern.slice(1); }\n    // Find all matching files for this pattern.\n    var matches = fn(pattern);\n    if (exclusion) {\n      // If an exclusion, remove matching files.\n      result = difference(result, matches);\n    } else {\n      // Otherwise add matching files.\n      result = union(result, matches);\n    }\n  });\n  return result;\n};\n\n// True if the file path exists.\nfile.exists = function() {\n  var filepath = path.join.apply(path, arguments);\n  return fs.existsSync(filepath);\n};\n\n// Return an array of all file paths that match the given wildcard patterns.\nfile.expand = function(...args) {\n  // If the first argument is an options object, save those options to pass\n  // into the File.prototype.glob.sync method.\n  var options = isPlainObject(args[0]) ? args.shift() : {};\n  // Use the first argument if it's an Array, otherwise convert the arguments\n  // object to an array and use that.\n  var patterns = Array.isArray(args[0]) ? args[0] : args;\n  // Return empty set if there are no patterns or filepaths.\n  if (patterns.length === 0) { return []; }\n  // Return all matching filepaths.\n  var matches = processPatterns(patterns, function(pattern) {\n    // Find all matching files for this pattern.\n    return glob.sync(pattern, options);\n  });\n  // Filter result set?\n  if (options.filter) {\n    matches = matches.filter(function(filepath) {\n      filepath = path.join(options.cwd || '', filepath);\n      try {\n        if (typeof options.filter === 'function') {\n          return options.filter(filepath);\n        } else {\n          // If the file is of the right type and exists, this should work.\n          return fs.statSync(filepath)[options.filter]();\n        }\n      } catch(e) {\n        // Otherwise, it's probably not the right type.\n        return false;\n      }\n    });\n  }\n  return matches;\n};\n\n// Build a multi task \"files\" object dynamically.\nfile.expandMapping = function(patterns, destBase, options) {\n  options = Object.assign({\n    rename: function(destBase, destPath) {\n      return path.join(destBase || '', destPath);\n    }\n  }, options);\n  var files = [];\n  var fileByDest = {};\n  // Find all files matching pattern, using passed-in options.\n  file.expand(options, patterns).forEach(function(src) {\n    var destPath = src;\n    // Flatten?\n    if (options.flatten) {\n      destPath = path.basename(destPath);\n    }\n    // Change the extension?\n    if (options.ext) {\n      destPath = destPath.replace(/(\\.[^\\/]*)?$/, options.ext);\n    }\n    // Generate destination filename.\n    var dest = options.rename(destBase, destPath, options);\n    // Prepend cwd to src path if necessary.\n    if (options.cwd) { src = path.join(options.cwd, src); }\n    // Normalize filepaths to be unix-style.\n    dest = dest.replace(pathSeparatorRe, '/');\n    src = src.replace(pathSeparatorRe, '/');\n    // Map correct src path to dest path.\n    if (fileByDest[dest]) {\n      // If dest already exists, push this src onto that dest's src array.\n      fileByDest[dest].src.push(src);\n    } else {\n      // Otherwise create a new src-dest file mapping object.\n      files.push({\n        src: [src],\n        dest: dest,\n      });\n      // And store a reference for later use.\n      fileByDest[dest] = files[files.length - 1];\n    }\n  });\n  return files;\n};\n\n// reusing bits of grunt's multi-task source normalization\nfile.normalizeFilesArray = function(data) {\n  var files = [];\n\n  data.forEach(function(obj) {\n    var prop;\n    if ('src' in obj || 'dest' in obj) {\n      files.push(obj);\n    }\n  });\n\n  if (files.length === 0) {\n    return [];\n  }\n\n  files = _(files).chain().forEach(function(obj) {\n    if (!('src' in obj) || !obj.src) { return; }\n    // Normalize .src properties to flattened array.\n    if (Array.isArray(obj.src)) {\n      obj.src = flatten(obj.src);\n    } else {\n      obj.src = [obj.src];\n    }\n  }).map(function(obj) {\n    // Build options object, removing unwanted properties.\n    var expandOptions = Object.assign({}, obj);\n    delete expandOptions.src;\n    delete expandOptions.dest;\n\n    // Expand file mappings.\n    if (obj.expand) {\n      return file.expandMapping(obj.src, obj.dest, expandOptions).map(function(mapObj) {\n        // Copy obj properties to result.\n        var result = Object.assign({}, obj);\n        // Make a clone of the orig obj available.\n        result.orig = Object.assign({}, obj);\n        // Set .src and .dest, processing both as templates.\n        result.src = mapObj.src;\n        result.dest = mapObj.dest;\n        // Remove unwanted properties.\n        ['expand', 'cwd', 'flatten', 'rename', 'ext'].forEach(function(prop) {\n          delete result[prop];\n        });\n        return result;\n      });\n    }\n\n    // Copy obj properties to result, adding an .orig property.\n    var result = Object.assign({}, obj);\n    // Make a clone of the orig obj available.\n    result.orig = Object.assign({}, obj);\n\n    if ('src' in result) {\n      // Expose an expand-on-demand getter method as .src.\n      Object.defineProperty(result, 'src', {\n        enumerable: true,\n        get: function fn() {\n          var src;\n          if (!('result' in fn)) {\n            src = obj.src;\n            // If src is an array, flatten it. Otherwise, make it into an array.\n            src = Array.isArray(src) ? flatten(src) : [src];\n            // Expand src files, memoizing result.\n            fn.result = file.expand(expandOptions, src);\n          }\n          return fn.result;\n        }\n      });\n    }\n\n    if ('dest' in result) {\n      result.dest = obj.dest;\n    }\n\n    return result;\n  }).flatten().value();\n\n  return files;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvemlwLXN0cmVhbS9ub2RlX21vZHVsZXMvYXJjaGl2ZXItdXRpbHMvZmlsZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsbUJBQU8sQ0FBQyxvRUFBYTtBQUM5QixXQUFXLG1CQUFPLENBQUMsa0JBQU07O0FBRXpCLGNBQWMsbUJBQU8sQ0FBQyxvRUFBZ0I7QUFDdEMsaUJBQWlCLG1CQUFPLENBQUMsMEVBQW1CO0FBQzVDLFlBQVksbUJBQU8sQ0FBQyxnRUFBYztBQUNsQyxvQkFBb0IsbUJBQU8sQ0FBQyxnRkFBc0I7O0FBRWxELFdBQVcsbUJBQU8sQ0FBQywrQ0FBTTs7QUFFekI7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLHdDQUF3QztBQUN4QztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBLGtDQUFrQzs7QUFFbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NsaWVudC8uL25vZGVfbW9kdWxlcy96aXAtc3RyZWFtL25vZGVfbW9kdWxlcy9hcmNoaXZlci11dGlscy9maWxlLmpzPzM0NGQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBhcmNoaXZlci11dGlsc1xuICpcbiAqIENvcHlyaWdodCAoYykgMjAxMi0yMDE0IENocmlzIFRhbGtpbmd0b24sIGNvbnRyaWJ1dG9ycy5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9hcmNoaXZlcmpzL25vZGUtYXJjaGl2ZXIvYmxvYi9tYXN0ZXIvTElDRU5TRS1NSVRcbiAqL1xudmFyIGZzID0gcmVxdWlyZSgnZ3JhY2VmdWwtZnMnKTtcbnZhciBwYXRoID0gcmVxdWlyZSgncGF0aCcpO1xuXG52YXIgZmxhdHRlbiA9IHJlcXVpcmUoJ2xvZGFzaC5mbGF0dGVuJyk7XG52YXIgZGlmZmVyZW5jZSA9IHJlcXVpcmUoJ2xvZGFzaC5kaWZmZXJlbmNlJyk7XG52YXIgdW5pb24gPSByZXF1aXJlKCdsb2Rhc2gudW5pb24nKTtcbnZhciBpc1BsYWluT2JqZWN0ID0gcmVxdWlyZSgnbG9kYXNoLmlzcGxhaW5vYmplY3QnKTtcblxudmFyIGdsb2IgPSByZXF1aXJlKCdnbG9iJyk7XG5cbnZhciBmaWxlID0gbW9kdWxlLmV4cG9ydHMgPSB7fTtcblxudmFyIHBhdGhTZXBhcmF0b3JSZSA9IC9bXFwvXFxcXF0vZztcblxuLy8gUHJvY2VzcyBzcGVjaWZpZWQgd2lsZGNhcmQgZ2xvYiBwYXR0ZXJucyBvciBmaWxlbmFtZXMgYWdhaW5zdCBhXG4vLyBjYWxsYmFjaywgZXhjbHVkaW5nIGFuZCB1bmlxdWluZyBmaWxlcyBpbiB0aGUgcmVzdWx0IHNldC5cbnZhciBwcm9jZXNzUGF0dGVybnMgPSBmdW5jdGlvbihwYXR0ZXJucywgZm4pIHtcbiAgLy8gRmlsZXBhdGhzIHRvIHJldHVybi5cbiAgdmFyIHJlc3VsdCA9IFtdO1xuICAvLyBJdGVyYXRlIG92ZXIgZmxhdHRlbmVkIHBhdHRlcm5zIGFycmF5LlxuICBmbGF0dGVuKHBhdHRlcm5zKS5mb3JFYWNoKGZ1bmN0aW9uKHBhdHRlcm4pIHtcbiAgICAvLyBJZiB0aGUgZmlyc3QgY2hhcmFjdGVyIGlzICEgaXQgc2hvdWxkIGJlIG9taXR0ZWRcbiAgICB2YXIgZXhjbHVzaW9uID0gcGF0dGVybi5pbmRleE9mKCchJykgPT09IDA7XG4gICAgLy8gSWYgdGhlIHBhdHRlcm4gaXMgYW4gZXhjbHVzaW9uLCByZW1vdmUgdGhlICFcbiAgICBpZiAoZXhjbHVzaW9uKSB7IHBhdHRlcm4gPSBwYXR0ZXJuLnNsaWNlKDEpOyB9XG4gICAgLy8gRmluZCBhbGwgbWF0Y2hpbmcgZmlsZXMgZm9yIHRoaXMgcGF0dGVybi5cbiAgICB2YXIgbWF0Y2hlcyA9IGZuKHBhdHRlcm4pO1xuICAgIGlmIChleGNsdXNpb24pIHtcbiAgICAgIC8vIElmIGFuIGV4Y2x1c2lvbiwgcmVtb3ZlIG1hdGNoaW5nIGZpbGVzLlxuICAgICAgcmVzdWx0ID0gZGlmZmVyZW5jZShyZXN1bHQsIG1hdGNoZXMpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBPdGhlcndpc2UgYWRkIG1hdGNoaW5nIGZpbGVzLlxuICAgICAgcmVzdWx0ID0gdW5pb24ocmVzdWx0LCBtYXRjaGVzKTtcbiAgICB9XG4gIH0pO1xuICByZXR1cm4gcmVzdWx0O1xufTtcblxuLy8gVHJ1ZSBpZiB0aGUgZmlsZSBwYXRoIGV4aXN0cy5cbmZpbGUuZXhpc3RzID0gZnVuY3Rpb24oKSB7XG4gIHZhciBmaWxlcGF0aCA9IHBhdGguam9pbi5hcHBseShwYXRoLCBhcmd1bWVudHMpO1xuICByZXR1cm4gZnMuZXhpc3RzU3luYyhmaWxlcGF0aCk7XG59O1xuXG4vLyBSZXR1cm4gYW4gYXJyYXkgb2YgYWxsIGZpbGUgcGF0aHMgdGhhdCBtYXRjaCB0aGUgZ2l2ZW4gd2lsZGNhcmQgcGF0dGVybnMuXG5maWxlLmV4cGFuZCA9IGZ1bmN0aW9uKC4uLmFyZ3MpIHtcbiAgLy8gSWYgdGhlIGZpcnN0IGFyZ3VtZW50IGlzIGFuIG9wdGlvbnMgb2JqZWN0LCBzYXZlIHRob3NlIG9wdGlvbnMgdG8gcGFzc1xuICAvLyBpbnRvIHRoZSBGaWxlLnByb3RvdHlwZS5nbG9iLnN5bmMgbWV0aG9kLlxuICB2YXIgb3B0aW9ucyA9IGlzUGxhaW5PYmplY3QoYXJnc1swXSkgPyBhcmdzLnNoaWZ0KCkgOiB7fTtcbiAgLy8gVXNlIHRoZSBmaXJzdCBhcmd1bWVudCBpZiBpdCdzIGFuIEFycmF5LCBvdGhlcndpc2UgY29udmVydCB0aGUgYXJndW1lbnRzXG4gIC8vIG9iamVjdCB0byBhbiBhcnJheSBhbmQgdXNlIHRoYXQuXG4gIHZhciBwYXR0ZXJucyA9IEFycmF5LmlzQXJyYXkoYXJnc1swXSkgPyBhcmdzWzBdIDogYXJncztcbiAgLy8gUmV0dXJuIGVtcHR5IHNldCBpZiB0aGVyZSBhcmUgbm8gcGF0dGVybnMgb3IgZmlsZXBhdGhzLlxuICBpZiAocGF0dGVybnMubGVuZ3RoID09PSAwKSB7IHJldHVybiBbXTsgfVxuICAvLyBSZXR1cm4gYWxsIG1hdGNoaW5nIGZpbGVwYXRocy5cbiAgdmFyIG1hdGNoZXMgPSBwcm9jZXNzUGF0dGVybnMocGF0dGVybnMsIGZ1bmN0aW9uKHBhdHRlcm4pIHtcbiAgICAvLyBGaW5kIGFsbCBtYXRjaGluZyBmaWxlcyBmb3IgdGhpcyBwYXR0ZXJuLlxuICAgIHJldHVybiBnbG9iLnN5bmMocGF0dGVybiwgb3B0aW9ucyk7XG4gIH0pO1xuICAvLyBGaWx0ZXIgcmVzdWx0IHNldD9cbiAgaWYgKG9wdGlvbnMuZmlsdGVyKSB7XG4gICAgbWF0Y2hlcyA9IG1hdGNoZXMuZmlsdGVyKGZ1bmN0aW9uKGZpbGVwYXRoKSB7XG4gICAgICBmaWxlcGF0aCA9IHBhdGguam9pbihvcHRpb25zLmN3ZCB8fCAnJywgZmlsZXBhdGgpO1xuICAgICAgdHJ5IHtcbiAgICAgICAgaWYgKHR5cGVvZiBvcHRpb25zLmZpbHRlciA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgIHJldHVybiBvcHRpb25zLmZpbHRlcihmaWxlcGF0aCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gSWYgdGhlIGZpbGUgaXMgb2YgdGhlIHJpZ2h0IHR5cGUgYW5kIGV4aXN0cywgdGhpcyBzaG91bGQgd29yay5cbiAgICAgICAgICByZXR1cm4gZnMuc3RhdFN5bmMoZmlsZXBhdGgpW29wdGlvbnMuZmlsdGVyXSgpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoKGUpIHtcbiAgICAgICAgLy8gT3RoZXJ3aXNlLCBpdCdzIHByb2JhYmx5IG5vdCB0aGUgcmlnaHQgdHlwZS5cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgIH0pO1xuICB9XG4gIHJldHVybiBtYXRjaGVzO1xufTtcblxuLy8gQnVpbGQgYSBtdWx0aSB0YXNrIFwiZmlsZXNcIiBvYmplY3QgZHluYW1pY2FsbHkuXG5maWxlLmV4cGFuZE1hcHBpbmcgPSBmdW5jdGlvbihwYXR0ZXJucywgZGVzdEJhc2UsIG9wdGlvbnMpIHtcbiAgb3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oe1xuICAgIHJlbmFtZTogZnVuY3Rpb24oZGVzdEJhc2UsIGRlc3RQYXRoKSB7XG4gICAgICByZXR1cm4gcGF0aC5qb2luKGRlc3RCYXNlIHx8ICcnLCBkZXN0UGF0aCk7XG4gICAgfVxuICB9LCBvcHRpb25zKTtcbiAgdmFyIGZpbGVzID0gW107XG4gIHZhciBmaWxlQnlEZXN0ID0ge307XG4gIC8vIEZpbmQgYWxsIGZpbGVzIG1hdGNoaW5nIHBhdHRlcm4sIHVzaW5nIHBhc3NlZC1pbiBvcHRpb25zLlxuICBmaWxlLmV4cGFuZChvcHRpb25zLCBwYXR0ZXJucykuZm9yRWFjaChmdW5jdGlvbihzcmMpIHtcbiAgICB2YXIgZGVzdFBhdGggPSBzcmM7XG4gICAgLy8gRmxhdHRlbj9cbiAgICBpZiAob3B0aW9ucy5mbGF0dGVuKSB7XG4gICAgICBkZXN0UGF0aCA9IHBhdGguYmFzZW5hbWUoZGVzdFBhdGgpO1xuICAgIH1cbiAgICAvLyBDaGFuZ2UgdGhlIGV4dGVuc2lvbj9cbiAgICBpZiAob3B0aW9ucy5leHQpIHtcbiAgICAgIGRlc3RQYXRoID0gZGVzdFBhdGgucmVwbGFjZSgvKFxcLlteXFwvXSopPyQvLCBvcHRpb25zLmV4dCk7XG4gICAgfVxuICAgIC8vIEdlbmVyYXRlIGRlc3RpbmF0aW9uIGZpbGVuYW1lLlxuICAgIHZhciBkZXN0ID0gb3B0aW9ucy5yZW5hbWUoZGVzdEJhc2UsIGRlc3RQYXRoLCBvcHRpb25zKTtcbiAgICAvLyBQcmVwZW5kIGN3ZCB0byBzcmMgcGF0aCBpZiBuZWNlc3NhcnkuXG4gICAgaWYgKG9wdGlvbnMuY3dkKSB7IHNyYyA9IHBhdGguam9pbihvcHRpb25zLmN3ZCwgc3JjKTsgfVxuICAgIC8vIE5vcm1hbGl6ZSBmaWxlcGF0aHMgdG8gYmUgdW5peC1zdHlsZS5cbiAgICBkZXN0ID0gZGVzdC5yZXBsYWNlKHBhdGhTZXBhcmF0b3JSZSwgJy8nKTtcbiAgICBzcmMgPSBzcmMucmVwbGFjZShwYXRoU2VwYXJhdG9yUmUsICcvJyk7XG4gICAgLy8gTWFwIGNvcnJlY3Qgc3JjIHBhdGggdG8gZGVzdCBwYXRoLlxuICAgIGlmIChmaWxlQnlEZXN0W2Rlc3RdKSB7XG4gICAgICAvLyBJZiBkZXN0IGFscmVhZHkgZXhpc3RzLCBwdXNoIHRoaXMgc3JjIG9udG8gdGhhdCBkZXN0J3Mgc3JjIGFycmF5LlxuICAgICAgZmlsZUJ5RGVzdFtkZXN0XS5zcmMucHVzaChzcmMpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBPdGhlcndpc2UgY3JlYXRlIGEgbmV3IHNyYy1kZXN0IGZpbGUgbWFwcGluZyBvYmplY3QuXG4gICAgICBmaWxlcy5wdXNoKHtcbiAgICAgICAgc3JjOiBbc3JjXSxcbiAgICAgICAgZGVzdDogZGVzdCxcbiAgICAgIH0pO1xuICAgICAgLy8gQW5kIHN0b3JlIGEgcmVmZXJlbmNlIGZvciBsYXRlciB1c2UuXG4gICAgICBmaWxlQnlEZXN0W2Rlc3RdID0gZmlsZXNbZmlsZXMubGVuZ3RoIC0gMV07XG4gICAgfVxuICB9KTtcbiAgcmV0dXJuIGZpbGVzO1xufTtcblxuLy8gcmV1c2luZyBiaXRzIG9mIGdydW50J3MgbXVsdGktdGFzayBzb3VyY2Ugbm9ybWFsaXphdGlvblxuZmlsZS5ub3JtYWxpemVGaWxlc0FycmF5ID0gZnVuY3Rpb24oZGF0YSkge1xuICB2YXIgZmlsZXMgPSBbXTtcblxuICBkYXRhLmZvckVhY2goZnVuY3Rpb24ob2JqKSB7XG4gICAgdmFyIHByb3A7XG4gICAgaWYgKCdzcmMnIGluIG9iaiB8fCAnZGVzdCcgaW4gb2JqKSB7XG4gICAgICBmaWxlcy5wdXNoKG9iaik7XG4gICAgfVxuICB9KTtcblxuICBpZiAoZmlsZXMubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIFtdO1xuICB9XG5cbiAgZmlsZXMgPSBfKGZpbGVzKS5jaGFpbigpLmZvckVhY2goZnVuY3Rpb24ob2JqKSB7XG4gICAgaWYgKCEoJ3NyYycgaW4gb2JqKSB8fCAhb2JqLnNyYykgeyByZXR1cm47IH1cbiAgICAvLyBOb3JtYWxpemUgLnNyYyBwcm9wZXJ0aWVzIHRvIGZsYXR0ZW5lZCBhcnJheS5cbiAgICBpZiAoQXJyYXkuaXNBcnJheShvYmouc3JjKSkge1xuICAgICAgb2JqLnNyYyA9IGZsYXR0ZW4ob2JqLnNyYyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG9iai5zcmMgPSBbb2JqLnNyY107XG4gICAgfVxuICB9KS5tYXAoZnVuY3Rpb24ob2JqKSB7XG4gICAgLy8gQnVpbGQgb3B0aW9ucyBvYmplY3QsIHJlbW92aW5nIHVud2FudGVkIHByb3BlcnRpZXMuXG4gICAgdmFyIGV4cGFuZE9wdGlvbnMgPSBPYmplY3QuYXNzaWduKHt9LCBvYmopO1xuICAgIGRlbGV0ZSBleHBhbmRPcHRpb25zLnNyYztcbiAgICBkZWxldGUgZXhwYW5kT3B0aW9ucy5kZXN0O1xuXG4gICAgLy8gRXhwYW5kIGZpbGUgbWFwcGluZ3MuXG4gICAgaWYgKG9iai5leHBhbmQpIHtcbiAgICAgIHJldHVybiBmaWxlLmV4cGFuZE1hcHBpbmcob2JqLnNyYywgb2JqLmRlc3QsIGV4cGFuZE9wdGlvbnMpLm1hcChmdW5jdGlvbihtYXBPYmopIHtcbiAgICAgICAgLy8gQ29weSBvYmogcHJvcGVydGllcyB0byByZXN1bHQuXG4gICAgICAgIHZhciByZXN1bHQgPSBPYmplY3QuYXNzaWduKHt9LCBvYmopO1xuICAgICAgICAvLyBNYWtlIGEgY2xvbmUgb2YgdGhlIG9yaWcgb2JqIGF2YWlsYWJsZS5cbiAgICAgICAgcmVzdWx0Lm9yaWcgPSBPYmplY3QuYXNzaWduKHt9LCBvYmopO1xuICAgICAgICAvLyBTZXQgLnNyYyBhbmQgLmRlc3QsIHByb2Nlc3NpbmcgYm90aCBhcyB0ZW1wbGF0ZXMuXG4gICAgICAgIHJlc3VsdC5zcmMgPSBtYXBPYmouc3JjO1xuICAgICAgICByZXN1bHQuZGVzdCA9IG1hcE9iai5kZXN0O1xuICAgICAgICAvLyBSZW1vdmUgdW53YW50ZWQgcHJvcGVydGllcy5cbiAgICAgICAgWydleHBhbmQnLCAnY3dkJywgJ2ZsYXR0ZW4nLCAncmVuYW1lJywgJ2V4dCddLmZvckVhY2goZnVuY3Rpb24ocHJvcCkge1xuICAgICAgICAgIGRlbGV0ZSByZXN1bHRbcHJvcF07XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gQ29weSBvYmogcHJvcGVydGllcyB0byByZXN1bHQsIGFkZGluZyBhbiAub3JpZyBwcm9wZXJ0eS5cbiAgICB2YXIgcmVzdWx0ID0gT2JqZWN0LmFzc2lnbih7fSwgb2JqKTtcbiAgICAvLyBNYWtlIGEgY2xvbmUgb2YgdGhlIG9yaWcgb2JqIGF2YWlsYWJsZS5cbiAgICByZXN1bHQub3JpZyA9IE9iamVjdC5hc3NpZ24oe30sIG9iaik7XG5cbiAgICBpZiAoJ3NyYycgaW4gcmVzdWx0KSB7XG4gICAgICAvLyBFeHBvc2UgYW4gZXhwYW5kLW9uLWRlbWFuZCBnZXR0ZXIgbWV0aG9kIGFzIC5zcmMuXG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocmVzdWx0LCAnc3JjJywge1xuICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICBnZXQ6IGZ1bmN0aW9uIGZuKCkge1xuICAgICAgICAgIHZhciBzcmM7XG4gICAgICAgICAgaWYgKCEoJ3Jlc3VsdCcgaW4gZm4pKSB7XG4gICAgICAgICAgICBzcmMgPSBvYmouc3JjO1xuICAgICAgICAgICAgLy8gSWYgc3JjIGlzIGFuIGFycmF5LCBmbGF0dGVuIGl0LiBPdGhlcndpc2UsIG1ha2UgaXQgaW50byBhbiBhcnJheS5cbiAgICAgICAgICAgIHNyYyA9IEFycmF5LmlzQXJyYXkoc3JjKSA/IGZsYXR0ZW4oc3JjKSA6IFtzcmNdO1xuICAgICAgICAgICAgLy8gRXhwYW5kIHNyYyBmaWxlcywgbWVtb2l6aW5nIHJlc3VsdC5cbiAgICAgICAgICAgIGZuLnJlc3VsdCA9IGZpbGUuZXhwYW5kKGV4cGFuZE9wdGlvbnMsIHNyYyk7XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiBmbi5yZXN1bHQ7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGlmICgnZGVzdCcgaW4gcmVzdWx0KSB7XG4gICAgICByZXN1bHQuZGVzdCA9IG9iai5kZXN0O1xuICAgIH1cblxuICAgIHJldHVybiByZXN1bHQ7XG4gIH0pLmZsYXR0ZW4oKS52YWx1ZSgpO1xuXG4gIHJldHVybiBmaWxlcztcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zip-stream/node_modules/archiver-utils/file.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/zip-stream/node_modules/archiver-utils/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/zip-stream/node_modules/archiver-utils/index.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * archiver-utils\n *\n * Copyright (c) 2015 Chris Talkington.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/archiver-utils/blob/master/LICENSE\n */\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar lazystream = __webpack_require__(/*! lazystream */ \"(ssr)/./node_modules/lazystream/lib/lazystream.js\");\nvar normalizePath = __webpack_require__(/*! normalize-path */ \"(ssr)/./node_modules/normalize-path/index.js\");\nvar defaults = __webpack_require__(/*! lodash.defaults */ \"(ssr)/./node_modules/lodash.defaults/index.js\");\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\nvar PassThrough = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").PassThrough);\n\nvar utils = module.exports = {};\nutils.file = __webpack_require__(/*! ./file.js */ \"(ssr)/./node_modules/zip-stream/node_modules/archiver-utils/file.js\");\n\nutils.collectStream = function(source, callback) {\n  var collection = [];\n  var size = 0;\n\n  source.on('error', callback);\n\n  source.on('data', function(chunk) {\n    collection.push(chunk);\n    size += chunk.length;\n  });\n\n  source.on('end', function() {\n    var buf = Buffer.alloc(size);\n    var offset = 0;\n\n    collection.forEach(function(data) {\n      data.copy(buf, offset);\n      offset += data.length;\n    });\n\n    callback(null, buf);\n  });\n};\n\nutils.dateify = function(dateish) {\n  dateish = dateish || new Date();\n\n  if (dateish instanceof Date) {\n    dateish = dateish;\n  } else if (typeof dateish === 'string') {\n    dateish = new Date(dateish);\n  } else {\n    dateish = new Date();\n  }\n\n  return dateish;\n};\n\n// this is slightly different from lodash version\nutils.defaults = function(object, source, guard) {\n  var args = arguments;\n  args[0] = args[0] || {};\n\n  return defaults(...args);\n};\n\nutils.isStream = function(source) {\n  return source instanceof Stream;\n};\n\nutils.lazyReadStream = function(filepath) {\n  return new lazystream.Readable(function() {\n    return fs.createReadStream(filepath);\n  });\n};\n\nutils.normalizeInputSource = function(source) {\n  if (source === null) {\n    return Buffer.alloc(0);\n  } else if (typeof source === 'string') {\n    return Buffer.from(source);\n  } else if (utils.isStream(source)) {\n    // Always pipe through a PassThrough stream to guarantee pausing the stream if it's already flowing,\n    // since it will only be processed in a (distant) future iteration of the event loop, and will lose\n    // data if already flowing now.\n    return source.pipe(new PassThrough());\n  }\n\n  return source;\n};\n\nutils.sanitizePath = function(filepath) {\n  return normalizePath(filepath, false).replace(/^\\w+:/, '').replace(/^(\\.\\.\\/|\\/)+/, '');\n};\n\nutils.trailingSlashIt = function(str) {\n  return str.slice(-1) !== '/' ? str + '/' : str;\n};\n\nutils.unixifyPath = function(filepath) {\n  return normalizePath(filepath, false).replace(/^\\w+:/, '');\n};\n\nutils.walkdir = function(dirpath, base, callback) {\n  var results = [];\n\n  if (typeof base === 'function') {\n    callback = base;\n    base = dirpath;\n  }\n\n  fs.readdir(dirpath, function(err, list) {\n    var i = 0;\n    var file;\n    var filepath;\n\n    if (err) {\n      return callback(err);\n    }\n\n    (function next() {\n      file = list[i++];\n\n      if (!file) {\n        return callback(null, results);\n      }\n\n      filepath = path.join(dirpath, file);\n\n      fs.stat(filepath, function(err, stats) {\n        results.push({\n          path: filepath,\n          relative: path.relative(base, filepath).replace(/\\\\/g, '/'),\n          stats: stats\n        });\n\n        if (stats && stats.isDirectory()) {\n          utils.walkdir(filepath, base, function(err, res) {\n            res.forEach(function(dirEntry) {\n              results.push(dirEntry);\n            });\n            next();\n          });\n        } else {\n          next();\n        }\n      });\n    })();\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zip-stream/node_modules/archiver-utils/index.js\n");

/***/ })

};
;