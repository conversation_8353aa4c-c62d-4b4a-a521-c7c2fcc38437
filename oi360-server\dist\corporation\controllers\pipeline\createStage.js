"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addPipelineStages = void 0;
const helpers_1 = require("../../../utils/helpers");
const RESERVED_DONE_ORDER = 100;
const addPipelineStages = async (req, res) => {
    const { id: pipelineId } = req.params;
    const stages = req.body;
    if (!Array.isArray(stages)) {
        return res
            .status(400)
            .json({ success: false, message: "Body must be an array of stages" });
    }
    try {
        // Get the max current order for this specific pipeline
        const maxOrder = await prisma.pipelineStage.aggregate({
            where: { pipelineId, order: { lt: RESERVED_DONE_ORDER } },
            _max: { order: true },
        });
        let nextOrder = (maxOrder._max.order ?? -1) + 1;
        const existingStagesByName = await prisma.pipelineStage.findMany({
            where: {
                pipelineId,
                name: { in: stages.map((s) => s.name) },
            },
        });
        const existingNamesSet = new Set(existingStagesByName.map((s) => s.name.toLowerCase()));
        const stagesToCreate = [];
        for (const stage of stages) {
            const stageName = stage.name?.trim();
            const isDoneStage = stageName?.toLowerCase() === "done";
            if (!isDoneStage && nextOrder >= RESERVED_DONE_ORDER) {
                throw new Error("Cannot assign order >= 100 to non-'Done' stages");
            }
            if (existingNamesSet.has(stageName.toLowerCase())) {
                const existing = existingStagesByName.find((s) => s.name.toLowerCase() === stage.name.toLowerCase());
                await prisma.pipelineStage.update({
                    where: { id: existing.id },
                    data: { order: isDoneStage ? RESERVED_DONE_ORDER : existing.order + 1 },
                });
            }
            else {
                stagesToCreate.push({
                    ...stage,
                    pipelineId,
                    order: isDoneStage ? RESERVED_DONE_ORDER : nextOrder,
                    createdBy: stage.createdBy,
                });
                if (!isDoneStage) {
                    nextOrder += 1;
                }
            }
        }
        if (stagesToCreate.length > 0) {
            await prisma.pipelineStage.createMany({ data: stagesToCreate });
        }
        return res
            .status(200)
            .json({ success: true, message: "Stages processed successfully", stagesToCreate });
    }
    catch (error) {
        console.error("Error in addPipelineStages:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.addPipelineStages = addPipelineStages;
//# sourceMappingURL=createStage.js.map