"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewTracksheetsById = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewTracksheetsById = async (req, res) => {
    try {
        const { id } = req.params;
        const data = await prisma.invoiceFile.findUnique({
            where: {
                id: id,
            },
            include: {
                TrackSheets: {
                    include: {
                        TrackSheetCustomFieldMapping: true,
                    },
                },
            },
        });
        if (!data) {
            return res.status(404).json({ message: "Invoice file not found" });
        }
        return res.status(200).json({ data: data.TrackSheets });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewTracksheetsById = viewTracksheetsById;
//# sourceMappingURL=viewTracksheetsById.js.map