"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createBulkInvoiceFiles = exports.createInvoiceFile = void 0;
const operation_1 = require("../../../utils/operation");
const checkUniqueFiles_1 = require("./checkUniqueFiles");
const createInvoiceFile = async (req, res) => {
    try {
        const { carrier, date, fileName, noOfPages, assignedTo, createdBy } = req.body;
        // Validation
        if (!carrier || !date || !fileName || !noOfPages) {
            return res.status(400).json({
                success: false,
                message: "Carrier, Date, file name, and number of pages are required",
            });
        }
        if (typeof noOfPages !== "number" || noOfPages <= 0) {
            return res.status(400).json({
                success: false,
                message: "Number of pages must be a positive number",
            });
        }
        // Prepare fields with default assigned_to = created_by if not provided
        const fields = {
            carrierId: Number(carrier),
            date: new Date(date),
            fileName: fileName.trim(),
            noOfPages: Number(noOfPages),
            createdBy: createdBy,
        };
        await (0, operation_1.createItem)({
            model: "invoiceFile",
            fieldName: "id",
            fields: fields,
            res: res,
            req: req,
            successMessage: "Invoice file entry has been created successfully",
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
};
exports.createInvoiceFile = createInvoiceFile;
const createBulkInvoiceFiles = async (req, res) => {
    try {
        const entries = req.body;
        if (!Array.isArray(entries) || entries.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Request body must be a non-empty array of invoice entries.",
            });
        }
        const formattedData = [];
        const inputErrors = [];
        for (const entry of entries) {
            const { carrier, date, fileName, noOfPages, createdBy } = entry;
            if (!carrier ||
                !date ||
                !fileName ||
                typeof noOfPages !== "number" ||
                noOfPages <= 0) {
                inputErrors.push("Each entry must include carrier, date, fileName, noOfPages");
                continue;
            }
            formattedData.push({
                carrierId: Number(carrier),
                date: new Date(date),
                fileName: fileName.trim(),
                noOfPages: Number(noOfPages),
                createdBy: createdBy ? Number(createdBy) : 0,
            });
        }
        if (inputErrors.length > 0) {
            return res.status(400).json({
                success: false,
                message: "Invalid entries found.",
                errors: inputErrors,
            });
        }
        const duplicates = await (0, checkUniqueFiles_1.findDuplicateInvoiceFiles)(formattedData.map((entry) => ({
            carrier: entry.carrierId,
            date: entry.date,
            fileName: entry.fileName,
            noOfPages: entry.noOfPages,
        })));
        if (duplicates.length > 0) {
            const conflictList = duplicates.map((file) => `File "${file.fileName}" with ${file.noOfPages} pages on ${file.date.toISOString().split("T")[0]} (Carrier ID: ${file.carrierId})`);
            return res.status(409).json({
                success: false,
                message: "Duplicate file(s) already exist for the same carrier, date, and page count.",
                duplicates: conflictList,
            });
        }
        const created = await prisma.invoiceFile.createMany({
            data: formattedData,
        });
        return res.status(201).json({
            success: true,
            message: `${created.count} invoice file(s) created successfully.`,
        });
    }
    catch (error) {
        return res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
};
exports.createBulkInvoiceFiles = createBulkInvoiceFiles;
//# sourceMappingURL=create.js.map