# Manage Tickets Module - Technical Documentation

## Overview

The Manage Tickets Module is a comprehensive ticket management and workflow system that provides Kanban-style task tracking, drag-and-drop functionality, real-time collaboration, and advanced analytics. The module supports complete ticket lifecycle management with stage-based workflows, time tracking, comments, tags, and detailed reporting capabilities.

## Architecture Overview

### Frontend Architecture (React/Next.js)

#### Component Structure
```
oi360-client/app/pms/manage_tickets/
├── page.tsx                           # Main server component with data fetching
├── ManageTickets.tsx                  # Client-side ticket management logic
├── TicketContext.tsx                  # React Context for state management
├── TicketProvider.tsx                 # Context provider wrapper
├── ticket.ts                          # TypeScript type definitions
├── tickets.ts                         # Ticket utility functions
├── ExportTicket.tsx                   # Ticket export functionality
├── [id]/
│   ├── page.tsx                       # Ticket detail page
│   └── TicketDetailClient.tsx         # Client-side ticket detail logic
├── archived_tickets/
│   ├── page.tsx                       # Archived tickets page
│   └── ArchivedTicketsBoard.tsx       # Archived tickets interface
├── tickets_analytics/
│   ├── page.tsx                       # Analytics page
│   ├── TicketsAnalyticsBoard.tsx      # Analytics dashboard
│   ├── reports.ts                     # Report type definitions
│   ├── ReportsData.ts                 # Analytics data processing
│   ├── components/                    # Analytics components
│   └── services/                      # Analytics services
└── components/
    ├── kanban-column.tsx              # Kanban column component
    ├── ticket-card.tsx                # Individual ticket card
    ├── ticket-modal.tsx               # Ticket detail modal
    ├── ticket-sidebar.tsx             # Ticket detail sidebar
    ├── ticket-filters.tsx             # Filtering interface
    ├── bulk-action.tsx                # Bulk operations
    ├── comment-section.tsx            # Comments functionality
    ├── tag-manager.tsx                # Tag management
    └── activity-section.tsx           # Activity tracking
```

#### Key Technologies
- **React 18** with Next.js App Router
- **@dnd-kit** for drag-and-drop functionality
- **React Context** for global state management
- **React Select** for dropdown components
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **React Hot Toast** for notifications
- **Date-fns** for date formatting
- **TypeScript** for type safety

#### State Management
- **React Context**: Global state for tickets, users, tags, and pipelines
- **Local State**: Component-level state for UI interactions
- **Drag-and-Drop State**: Ticket movement and visual feedback
- **Filter State**: Advanced filtering and search functionality
- **Modal State**: Ticket detail views and editing
- **Permission Management**: Role-based access control

#### Drag-and-Drop Implementation
- **Kanban Board**: Multi-column drag-and-drop for ticket stages
- **Sortable Context**: Ticket reordering within columns
- **Collision Detection**: Proper drop zone handling
- **Visual Feedback**: Drag overlays and hover states
- **Touch Support**: Mobile-friendly interactions

### Backend Architecture (Node.js/Express/Prisma)

#### API Endpoints
```
Base URL: /api/

Ticket Management:
├── POST   /tickets                        # Create new tickets (bulk)
├── GET    /tickets                        # Fetch all tickets with relations
├── GET    /tickets/mine                   # Fetch current user tickets
├── GET    /tickets/:id                    # Fetch specific ticket
├── PUT    /tickets/ticket/:id             # Update ticket properties
├── PUT    /tickets/bulk                   # Bulk update tickets
├── DELETE /tickets/:id                    # Soft delete ticket
├── GET    /tickets/export                 # Export tickets to CSV/Excel
└── GET    /tickets/:id/stage-logs         # Fetch ticket stage change logs

Comments Management:
├── POST   /comments                       # Create new comment
├── GET    /comments/ticket/:ticketId      # Fetch comments by ticket
├── PUT    /comments/:id                   # Update comment
└── DELETE /comments/:id                   # Soft delete comment

Tags Management:
├── POST   /tags                           # Create new tag
├── GET    /tags                           # Fetch all tags
├── GET    /tags/ticket/:ticketId          # Fetch tags by ticket
├── PUT    /tags/:id                       # Update tag
├── DELETE /tags/:id                       # Soft delete tag
└── POST   /tags/assign                    # Assign tags to ticket

Analytics:
├── GET    /analytics/overview             # Tickets overview analytics
├── GET    /analytics/distribution-by-stage # Stage distribution analytics
├── GET    /analytics/closure-rate-by-stage # Closure rate analytics
├── GET    /analytics/average-time-by-stage # Time analysis by stage
├── GET    /analytics/closure-insights     # Closure rate insights
└── GET    /analytics/resolution-insights  # Resolution time insights
```

#### Controller Structure
```
oi360-server/src/corporation/controllers/
├── ticket/
│   ├── create.ts                      # Ticket creation logic
│   ├── view.ts                        # Ticket retrieval operations
│   ├── update.ts                      # Ticket update and stage management
│   ├── delete.ts                      # Ticket soft deletion
│   ├── exportTicketService.ts         # Ticket export functionality
│   ├── analytics/
│   │   ├── overview.ts                # Overview analytics
│   │   ├── distribution.ts            # Distribution analytics
│   │   ├── closureRate.ts             # Closure rate analytics
│   │   ├── timeAnalysis.ts            # Time analysis
│   │   └── insight.ts                 # Insights generation
│   └── tag/
│       ├── create.ts                  # Tag creation
│       ├── view.ts                    # Tag retrieval
│       ├── update.ts                  # Tag updates
│       ├── delete.ts                  # Tag deletion
│       └── assign.ts                  # Tag assignment
└── comment/
    ├── create.ts                      # Comment creation
    ├── view.ts                        # Comment retrieval
    ├── update.ts                      # Comment updates
    └── delete.ts                      # Comment deletion
```

#### Business Logic

##### Ticket Management
- **Bulk Creation**: Support for creating multiple tickets simultaneously
- **Stage Workflow**: Automatic stage progression and validation
- **Time Tracking**: Automatic time tracking for stage transitions
- **Change Logging**: Complete audit trail for ticket changes
- **Assignment Management**: User assignment with validation

##### Stage Management
- **Stage Transitions**: Automatic stage change detection and logging
- **Time Tracking**: Start/end time tracking for each stage
- **Assignment Validation**: Ensures valid user assignments
- **Due Date Management**: Due date tracking and notifications

##### Analytics Engine
- **Real-time Metrics**: Live calculation of ticket metrics
- **Stage Analytics**: Performance analysis by pipeline stage
- **Time Analysis**: Average time spent in each stage
- **Closure Rates**: Completion rate analysis
- **Insights Generation**: Automated insights and recommendations

### Database Schema

#### Core Models

##### Ticket Model
```prisma
model Ticket {
  id             String    @id @default(uuid())
  title          String?   @db.VarChar()
  description    String?   @db.Text()
  owner          String?   @db.VarChar()
  priority       String?   @db.VarChar()
  workItemId     String    @map("work_item_id")
  pipelineId     String?   @map("pipeline_id")
  currentStageId String?   @map("current_stage_id")
  tags           String[]  @db.VarChar()
  
  pipeline       Pipeline? @relation(fields: [pipelineId], references: [id], onDelete: Cascade)
  stages         TicketStage[]
  comments       Comment[]
  timeTracking   TicketStageTimeTracking[]
  
  createdAt      DateTime  @default(now()) @map("created_at")
  createdBy      String?   @map("created_by")
  updatedAt      DateTime? @updatedAt @map("updated_at")
  updatedBy      String?   @map("updated_by")
  deletedAt      DateTime? @map("deleted_at")
  deletedBy      String?   @map("deleted_by")
}
```

##### TicketStage Model
```prisma
model TicketStage {
  id              String        @id @default(uuid())
  ticketId        String?       @map("ticket_id")
  pipelineStageId String?       @map("pipeline_stage_id")
  assignedTo      String?       @map("assigned_to") @db.VarChar()
  dueAt           DateTime?     @map("due_at")
  
  ticket          Ticket?       @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  pipelineStage   PipelineStage? @relation(fields: [pipelineStageId], references: [id], onDelete: Cascade)
  
  createdAt       DateTime      @default(now()) @map("created_at")
  createdBy       String?       @map("created_by")
  updatedAt       DateTime?     @updatedAt @map("updated_at")
  updatedBy       String?       @map("updated_by")
  deletedAt       DateTime?     @map("deleted_at")
  deletedBy       String?       @map("deleted_by")
  
  @@index([assignedTo, dueAt])
}
```

##### Comment Model
```prisma
model Comment {
  id        String    @id @default(uuid())
  content   String    @db.Text()
  ticketId  String    @map("ticket_id")
  
  ticket    Ticket    @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  
  createdBy String?   @map("created_by") @db.VarChar()
  createdAt DateTime  @default(now()) @map("created_at")
  updatedBy String?   @map("updated_by") @db.VarChar()
  updatedAt DateTime? @updatedAt @map("updated_at")
  deletedBy String?   @map("deleted_by") @db.VarChar()
  deletedAt DateTime? @map("deleted_at")
}
```

##### Tag Model
```prisma
model Tag {
  id        String    @id @default(uuid())
  tagName   String    @unique @map("tag_name") @db.VarChar()
  color     String    @db.VarChar()
  
  createdBy String?   @map("created_by") @db.VarChar()
  createdAt DateTime  @default(now()) @map("created_at")
  updatedBy String?   @map("updated_by") @db.VarChar()
  updatedAt DateTime? @updatedAt @map("updated_at")
  deletedBy String?   @map("deleted_by") @db.VarChar()
  deletedAt DateTime? @map("deleted_at")
}
```

##### TicketStageTimeTracking Model
```prisma
model TicketStageTimeTracking {
  id         String        @id @default(uuid())
  ticketId   String        @map("ticket_id")
  stageId    String        @map("stage_id")
  startTime  DateTime      @map("start_time")
  endTime    DateTime?     @map("end_time")
  duration   Int?          @map("duration") // Duration in minutes
  assignedTo String?       @map("assigned_to") @db.VarChar()
  
  ticket     Ticket        @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  stage      PipelineStage @relation(fields: [stageId], references: [id], onDelete: Cascade)
  
  createdAt  DateTime      @default(now()) @map("created_at")
  createdBy  String?       @map("created_by")
  updatedAt  DateTime?     @updatedAt @map("updated_at")
  updatedBy  String?       @map("updated_by")
}
```

##### TicketStageChangeLog Model
```prisma
model TicketStageChangeLog {
  id        String    @id @default(uuid())
  ticketId  String?   @map("ticket_id")
  fromStage String?   @map("from_stage")
  toStage   String?   @map("to_stage")
  
  createdAt DateTime  @default(now()) @map("created_at")
  createdBy String?   @map("created_by")
  updatedAt DateTime? @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")
  deletedAt DateTime? @map("deleted_at")
  deletedBy String?   @map("deleted_by")
}
```

#### Relationships
- **One-to-Many**: Ticket → TicketStage, Comment, TicketStageTimeTracking
- **Many-to-One**: Ticket → Pipeline
- **Many-to-One**: TicketStage → PipelineStage
- **One-to-Many**: PipelineStage → TicketStageTimeTracking
- **Array Field**: Ticket.tags for tag associations

#### Data Flow
1. **Ticket Creation**: Creates ticket with initial stage assignment
2. **Stage Progression**: Tickets move through pipeline stages
3. **Time Tracking**: Automatic tracking of time spent in each stage
4. **Change Logging**: All stage changes logged for audit trail
5. **Comments & Tags**: Associated metadata for collaboration
6. **Analytics**: Real-time calculation of performance metrics

## Feature Specifications

### Kanban Board Features
- **Multi-Column Layout**: Dynamic columns based on pipeline stages
- **Drag-and-Drop**: Intuitive ticket movement between stages
- **Real-time Updates**: Live updates across user sessions
- **Visual Indicators**: Color-coded priorities and status
- **Bulk Selection**: Multi-ticket operations

### Ticket Management Features
- **Ticket Creation**: Single and bulk ticket creation
- **Inline Editing**: Quick edit of ticket properties
- **Priority Management**: Low, medium, high priority levels
- **Assignment Management**: User assignment with validation
- **Due Date Tracking**: Due date management and notifications
- **Tag System**: Flexible tagging for categorization

### Advanced Filtering
- **Text Search**: Search across titles, descriptions, and content
- **Stage Filtering**: Filter by current stage
- **Assignment Filtering**: Filter by assigned user
- **Priority Filtering**: Filter by priority level
- **Tag Filtering**: Filter by associated tags
- **Date Range Filtering**: Filter by creation or due dates

### Analytics & Reporting
- **Overview Dashboard**: Key metrics and KPIs
- **Stage Distribution**: Ticket distribution across stages
- **Time Analysis**: Average time spent in each stage
- **Closure Rates**: Completion rate analysis
- **Performance Insights**: Automated insights and recommendations
- **Export Functionality**: CSV/Excel export capabilities

### Collaboration Features
- **Comments System**: Threaded comments on tickets
- **Activity Tracking**: Complete audit trail
- **Real-time Notifications**: Live updates and notifications
- **User Mentions**: @mention functionality in comments
- **File Attachments**: Document and image attachments

## API Integration Patterns

#### Ticket Operations
```typescript
// Create tickets
const createTickets = async (ticketsData: TicketCreateData[]) => {
  return await formSubmit('/api/tickets', 'POST', ticketsData);
};

// Update ticket
const updateTicket = async (ticketId: string, updates: Partial<Ticket>) => {
  return await formSubmit(`/api/tickets/ticket/${ticketId}`, 'PUT', {
    ticketId,
    ...updates
  });
};

// Bulk update tickets
const bulkUpdateTickets = async (updates: BulkUpdateData) => {
  return await formSubmit('/api/tickets/bulk', 'PUT', updates);
};
```

#### Stage Management
```typescript
// Move ticket to stage
const moveTicketToStage = async (ticketId: string, stageId: string, assignedTo?: string) => {
  return await formSubmit(`/api/tickets/ticket/${ticketId}`, 'PUT', {
    ticketId,
    ticketStageId: stageId,
    assignedTo
  });
};
```

## Security & Permissions

### Access Control
- **Permission Required**: "view-tickets" for ticket viewing
- **Role-Based Access**: Different permissions for different operations
- **User Scoping**: Users can see only their tickets or all tickets based on role
- **Corporation Scoping**: Tickets scoped to user's corporation

### Data Validation
- **Input Sanitization**: All inputs validated and sanitized
- **Stage Validation**: Ensures valid stage transitions
- **Assignment Validation**: Validates user assignments
- **Permission Checks**: Operation-level permission validation

## Performance Considerations

### Frontend Optimizations
- **React Context**: Efficient global state management
- **Component Memoization**: React.memo for expensive components
- **Virtual Scrolling**: For large ticket lists (future enhancement)
- **Debounced Search**: Prevents excessive API calls
- **Lazy Loading**: Dynamic imports for heavy components

### Backend Optimizations
- **Database Indexing**: Proper indexes on foreign keys and search fields
- **Query Optimization**: Selective field loading with Prisma includes
- **Bulk Operations**: Efficient bulk update operations
- **Caching Strategy**: Redis caching for frequently accessed data

## Error Handling

### Frontend Error Handling
- **Toast Notifications**: User-friendly error messages
- **Loading States**: Visual feedback during operations
- **Form Validation**: Client-side validation before submission
- **Graceful Degradation**: Fallback UI for failed operations

### Backend Error Handling
- **Structured Responses**: Consistent error response format
- **HTTP Status Codes**: Appropriate status codes for different scenarios
- **Input Validation**: Comprehensive request validation
- **Database Error Handling**: Proper error catching and logging

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with Jest/React Testing Library
- **Integration Tests**: Kanban board workflow testing
- **E2E Tests**: Complete ticket management flows
- **Accessibility Tests**: Screen reader and keyboard navigation

### Backend Testing
- **Unit Tests**: Controller and service testing
- **Integration Tests**: Database operation testing
- **API Tests**: Endpoint testing with various scenarios
- **Performance Tests**: Load testing for ticket operations

## Future Enhancements

### Planned Features
- **Real-time Collaboration**: WebSocket support for live updates
- **Advanced Notifications**: Email and push notifications
- **Custom Fields**: Configurable ticket fields
- **Automation Rules**: Automated ticket actions and transitions
- **Integration APIs**: Third-party system integrations
- **Mobile App**: Native mobile ticket management

### Technical Improvements
- **Performance Optimization**: Advanced caching and optimization
- **Scalability**: Microservices architecture
- **Advanced Analytics**: Machine learning insights
- **Workflow Automation**: Custom workflow rules and triggers
