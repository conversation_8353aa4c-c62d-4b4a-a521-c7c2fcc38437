"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPipeline = void 0;
const helpers_1 = require("../../../utils/helpers");
const client_1 = require("@prisma/client");
function isValidWorktype(value) {
    return Object.values(client_1.Worktype).includes(value);
}
const RESERVED_DONE_ORDER = 100;
const createPipeline = async (req, res) => {
    try {
        const { name, description, workType, createdBy, isActive } = req.body;
        // Validate workType
        if (!isValidWorktype(workType)) {
            return res.status(400).json({
                success: false,
                message: `Invalid workType value. Allowed values: ${Object.values(client_1.Worktype).join(", ")}`,
            });
        }
        const fields = {
            name,
            description,
            workType,
            isActive,
            createdBy,
            updatedBy: createdBy,
            stages: {
                create: {
                    name: "Done",
                    description: "Default Done stage",
                    order: RESERVED_DONE_ORDER,
                    createdBy,
                },
            },
        };
        const newPipeline = await prisma.pipeline.create({
            data: fields,
            include: {
                stages: true, // Include the stages in the response
            },
        });
        return res.status(201).json({
            success: true,
            message: "Pipeline created successfully.",
            data: newPipeline,
        });
    }
    catch (error) {
        (0, helpers_1.handleError)(res, error);
    }
};
exports.createPipeline = createPipeline;
//# sourceMappingURL=create.js.map