"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getClosureRateByStage = void 0;
const helpers_1 = require("../../../../utils/helpers");
const getClosureRateByStage = async (req, res) => {
    try {
        const parseLocalDate = (s) => {
            const [y, m, d] = s.split("-").map((v) => parseInt(v, 10));
            return new Date(y, (m || 1) - 1, d || 1);
        };
        const toStartOfDay = (d) => {
            const x = new Date(d);
            x.setHours(0, 0, 0, 0);
            return x;
        };
        const toEndOfDay = (d) => {
            const x = new Date(d);
            x.setHours(23, 59, 59, 999);
            return x;
        };
        const { dateFrom, dateTo, tags, assignedTo, stageId, priority, dueDateFrom, dueDateTo, } = req.query;
        const ticketWhereClause = {
            deletedAt: null,
        };
        if ((dateFrom || dateTo) && !(dueDateFrom || dueDateTo)) {
            ticketWhereClause.createdAt = {};
            if (dateFrom) {
                ticketWhereClause.createdAt.gte = toStartOfDay(parseLocalDate(dateFrom));
            }
            if (dateTo) {
                ticketWhereClause.createdAt.lte = toEndOfDay(parseLocalDate(dateTo));
            }
        }
        if (tags) {
            const tagArray = tags.split(',').map((tag) => tag.trim());
            ticketWhereClause.tags = {
                hasSome: tagArray,
            };
        }
        if (priority) {
            ticketWhereClause.priority = priority;
        }
        const stageWhereClause = {};
        if (assignedTo) {
            stageWhereClause.assignedTo = assignedTo;
        }
        if (stageId) {
            stageWhereClause.pipelineStageId = stageId;
        }
        const tickets = await prisma.ticket.findMany({
            where: {
                ...ticketWhereClause,
                ...(Object.keys(stageWhereClause).length > 0 && {
                    stages: {
                        some: stageWhereClause,
                    },
                }),
            },
            include: {
                stages: {
                    include: {
                        pipelineStage: true,
                    },
                    orderBy: {
                        createdAt: 'asc',
                    },
                },
                pipeline: {
                    include: {
                        stages: {
                            orderBy: { order: 'asc' },
                        },
                    },
                },
            },
        });
        let filteredTickets = tickets;
        if (dueDateFrom || dueDateTo) {
            const fromDate = dueDateFrom ? toStartOfDay(parseLocalDate(dueDateFrom)) : null;
            const toDate = dueDateTo ? toEndOfDay(parseLocalDate(dueDateTo)) : null;
            filteredTickets = tickets.filter((ticket) => {
                if (!ticket.currentStageId || !ticket.stages?.length)
                    return false;
                const currentStage = ticket.stages.find((s) => String(s.pipelineStageId) === String(ticket.currentStageId));
                if (!currentStage)
                    return false;
                const dueAt = currentStage.dueAt ? new Date(currentStage.dueAt) : null;
                if (!dueAt)
                    return false;
                if (fromDate && dueAt < fromDate)
                    return false;
                if (toDate && dueAt > toDate)
                    return false;
                return true;
            });
        }
        const allStages = new Map();
        filteredTickets.forEach(ticket => {
            if (ticket.pipeline?.stages) {
                ticket.pipeline.stages.forEach(stage => {
                    allStages.set(stage.id, {
                        stageId: stage.id,
                        stageName: stage.name || 'Unnamed Stage',
                        stageOrder: stage.order,
                        pipelineId: stage.pipelineId || '',
                    });
                });
            }
        });
        const stageAnalysis = Array.from(allStages.values()).map(stageInfo => {
            let ticketsEntered = 0;
            let ticketsCompleted = 0;
            let totalTimeInStage = 0;
            let timeTrackingCount = 0;
            filteredTickets.forEach(ticket => {
                if (!ticket.pipeline?.stages)
                    return;
                const ticketStages = ticket.stages.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
                const stageIndex = ticket.pipeline.stages.findIndex(s => s.id === stageInfo.stageId);
                const hasEnteredStage = ticketStages.some(ts => ts.pipelineStageId === stageInfo.stageId);
                if (hasEnteredStage) {
                    ticketsEntered++;
                    const currentStageEntry = ticketStages.find(ts => ts.pipelineStageId === stageInfo.stageId);
                    const nextStageIndex = stageIndex + 1;
                    if (nextStageIndex < ticket.pipeline.stages.length) {
                        const nextStageId = ticket.pipeline.stages[nextStageIndex].id;
                        const hasMovedToNext = ticketStages.some(ts => ts.pipelineStageId === nextStageId);
                        if (hasMovedToNext) {
                            ticketsCompleted++;
                            const stageEntry = ticketStages.find(ts => ts.pipelineStageId === stageInfo.stageId);
                            const nextStageEntry = ticketStages.find(ts => ts.pipelineStageId === nextStageId);
                            if (stageEntry && nextStageEntry) {
                                const timeInStage = new Date(nextStageEntry.createdAt).getTime() - new Date(stageEntry.createdAt).getTime();
                                totalTimeInStage += timeInStage / (1000 * 60 * 60);
                                timeTrackingCount++;
                            }
                        }
                    }
                    else {
                        const currentStage = ticketStages[ticketStages.length - 1];
                        if (currentStage.pipelineStageId === stageInfo.stageId) {
                            ticketsCompleted++;
                            if (currentStageEntry) {
                                const now = new Date();
                                const timeInStage = now.getTime() - new Date(currentStageEntry.createdAt).getTime();
                                totalTimeInStage += timeInStage / (1000 * 60 * 60);
                                timeTrackingCount++;
                            }
                        }
                    }
                }
            });
            const closureRate = ticketsEntered > 0 ? (ticketsCompleted / ticketsEntered) * 100 : 0;
            const averageTimeInStage = timeTrackingCount > 0 ? totalTimeInStage / timeTrackingCount : 0;
            return {
                stageId: stageInfo.stageId,
                stageName: stageInfo.stageName,
                stageOrder: stageInfo.stageOrder,
                ticketsEntered,
                ticketsCompleted,
                closureRate: Math.round(closureRate * 100) / 100,
                averageTimeInStage: Math.round(averageTimeInStage * 100) / 100,
                timeUnit: "hours",
            };
        });
        stageAnalysis.sort((a, b) => a.stageOrder - b.stageOrder);
        return res.status(200).json({
            success: true,
            data: stageAnalysis,
        });
    }
    catch (error) {
        console.error("Error in getClosureRateByStage:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getClosureRateByStage = getClosureRateByStage;
//# sourceMappingURL=closureRate.js.map