import React from "react";

interface MandatoryField {
  name: string;
  displayName: string;
  span?: number;
}

interface MandatoryFieldsPreviewProps {
  tableGrid: (MandatoryField | null)[][];
}

const isSpanPlaceholder = (cell: MandatoryField | null): boolean => {
  return cell?.name?.includes('_span_') || false;
};

const MandatoryFieldsPreview: React.FC<MandatoryFieldsPreviewProps> = ({ tableGrid }) => {
  // Calculate the number of columns as the maximum row length
  const cols = Math.max(...tableGrid.map(row => row.length), 1);
  const minWidth = cols > 6 ? `${cols * 140}px` : undefined;
  const grid = (
    <div
      className="grid gap-1"
      style={{
        gridTemplateColumns: `repeat(${cols}, 1fr)`,
        minWidth,
      }}
    >
      {tableGrid.map((row, rowIndex) =>
        row.map((cell, colIndex) => {
          if (isSpanPlaceholder(cell)) return null;
          if (!cell) {
            return (
              <div
                key={`${rowIndex}-${colIndex}`}
                className="bg-gray-50 border border-gray-200 rounded p-1 min-h-[36px]"
              />
            );
          }
          return (
            <div
              key={`${rowIndex}-${colIndex}`}
              style={{ gridColumn: cell.span && cell.span > 1 ? `span ${cell.span}` : undefined }}
              className="flex flex-col items-start bg-gray-50 border border-gray-200 rounded p-1 min-h-[36px]"
            >
              <label className="text-[11px] font-medium text-gray-700 mb-0.5">
                {cell.displayName}
              </label>
              <input
                type="text"
                className="w-full px-1.5 py-0.5 rounded border border-gray-300 bg-gray-100 text-gray-700 text-[11px]"
                disabled
                placeholder={cell.displayName}
              />
            </div>
          );
        })
      )}
    </div>
  );

  return (
    <div className="p-1">
      {cols > 6 ? (
        <div style={{ overflowX: 'auto' }}>{grid}</div>
      ) : (
        grid
      )}
    </div>
  );
};

export default MandatoryFieldsPreview; 
