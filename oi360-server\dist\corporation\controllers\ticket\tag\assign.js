"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.assignTagsToTicket = void 0;
const prismaClient_1 = __importDefault(require("../../../../utils/prismaClient"));
const helpers_1 = require("../../../../utils/helpers");
const assignTagsToTicket = async (req, res) => {
    try {
        const { ticketId, tagIds } = req.body;
        if (!ticketId || !Array.isArray(tagIds)) {
            return res.status(400).json({
                success: false,
                message: "Ticket ID and array of tag IDs are required",
            });
        }
        // Verify that the ticket exists
        const ticket = await prismaClient_1.default.ticket.findFirst({
            where: {
                id: ticketId,
                deletedAt: null,
            },
        });
        if (!ticket) {
            return res.status(404).json({
                success: false,
                message: "Ticket not found",
            });
        }
        // Verify that all tag IDs exist
        const existingTags = await prismaClient_1.default.tag.findMany({
            where: {
                id: {
                    in: tagIds,
                },
                deletedAt: null,
            },
            select: {
                id: true,
            },
        });
        if (existingTags.length !== tagIds.length) {
            return res.status(400).json({
                success: false,
                message: "One or more tags not found",
            });
        }
        // Update the ticket with the new tag IDs
        const updatedTicket = await prismaClient_1.default.ticket.update({
            where: { id: ticketId },
            data: {
                tags: tagIds,
                updatedBy: req.body?.createdBy || "system",
            },
        });
        return res.status(200).json({
            success: true,
            message: "Tags assigned successfully",
            data: updatedTicket,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.assignTagsToTicket = assignTagsToTicket;
//# sourceMappingURL=assign.js.map