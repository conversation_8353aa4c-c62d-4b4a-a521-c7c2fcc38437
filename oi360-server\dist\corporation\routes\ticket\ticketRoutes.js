"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/ticket/create");
const authentication_1 = require("../../../middleware/authentication");
const view_1 = require("../../controllers/ticket/view");
const delete_1 = require("../../controllers/ticket/delete");
const update_1 = require("../../controllers/ticket/update");
const exportTicketService_1 = require("../../controllers/ticket/exportTicketService");
const router = (0, express_1.Router)();
router.get("/mine", authentication_1.authenticate, view_1.getCurrentUserTickets);
// More specific routes first
router.put("/ticket/:id", authentication_1.authenticate, update_1.ticketUpdate);
router.get("/:id/stage-logs", authentication_1.authenticate, view_1.getTicketStageChangeLogs);
router.put("/bulk", authentication_1.authenticate, update_1.bulkUpdateTickets);
// Generic routes last
router.post("/", authentication_1.authenticate, create_1.createTicket);
router.get("/", authentication_1.authenticate, view_1.viewTicket);
router.get("/export", authentication_1.authenticate, exportTicketService_1.exportTicketService);
router.get("/:id", authentication_1.authenticate, view_1.viewTicketById);
router.delete("/:id", authentication_1.authenticate, delete_1.deleteTicket);
exports.default = router;
//# sourceMappingURL=ticketRoutes.js.map