"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bl";
exports.ids = ["vendor-chunks/bl"];
exports.modules = {

/***/ "(ssr)/./node_modules/bl/BufferList.js":
/*!***************************************!*\
  !*** ./node_modules/bl/BufferList.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { Buffer } = __webpack_require__(/*! buffer */ \"buffer\")\nconst symbol = Symbol.for('BufferList')\n\nfunction BufferList (buf) {\n  if (!(this instanceof BufferList)) {\n    return new BufferList(buf)\n  }\n\n  BufferList._init.call(this, buf)\n}\n\nBufferList._init = function _init (buf) {\n  Object.defineProperty(this, symbol, { value: true })\n\n  this._bufs = []\n  this.length = 0\n\n  if (buf) {\n    this.append(buf)\n  }\n}\n\nBufferList.prototype._new = function _new (buf) {\n  return new BufferList(buf)\n}\n\nBufferList.prototype._offset = function _offset (offset) {\n  if (offset === 0) {\n    return [0, 0]\n  }\n\n  let tot = 0\n\n  for (let i = 0; i < this._bufs.length; i++) {\n    const _t = tot + this._bufs[i].length\n    if (offset < _t || i === this._bufs.length - 1) {\n      return [i, offset - tot]\n    }\n    tot = _t\n  }\n}\n\nBufferList.prototype._reverseOffset = function (blOffset) {\n  const bufferId = blOffset[0]\n  let offset = blOffset[1]\n\n  for (let i = 0; i < bufferId; i++) {\n    offset += this._bufs[i].length\n  }\n\n  return offset\n}\n\nBufferList.prototype.get = function get (index) {\n  if (index > this.length || index < 0) {\n    return undefined\n  }\n\n  const offset = this._offset(index)\n\n  return this._bufs[offset[0]][offset[1]]\n}\n\nBufferList.prototype.slice = function slice (start, end) {\n  if (typeof start === 'number' && start < 0) {\n    start += this.length\n  }\n\n  if (typeof end === 'number' && end < 0) {\n    end += this.length\n  }\n\n  return this.copy(null, 0, start, end)\n}\n\nBufferList.prototype.copy = function copy (dst, dstStart, srcStart, srcEnd) {\n  if (typeof srcStart !== 'number' || srcStart < 0) {\n    srcStart = 0\n  }\n\n  if (typeof srcEnd !== 'number' || srcEnd > this.length) {\n    srcEnd = this.length\n  }\n\n  if (srcStart >= this.length) {\n    return dst || Buffer.alloc(0)\n  }\n\n  if (srcEnd <= 0) {\n    return dst || Buffer.alloc(0)\n  }\n\n  const copy = !!dst\n  const off = this._offset(srcStart)\n  const len = srcEnd - srcStart\n  let bytes = len\n  let bufoff = (copy && dstStart) || 0\n  let start = off[1]\n\n  // copy/slice everything\n  if (srcStart === 0 && srcEnd === this.length) {\n    if (!copy) {\n      // slice, but full concat if multiple buffers\n      return this._bufs.length === 1\n        ? this._bufs[0]\n        : Buffer.concat(this._bufs, this.length)\n    }\n\n    // copy, need to copy individual buffers\n    for (let i = 0; i < this._bufs.length; i++) {\n      this._bufs[i].copy(dst, bufoff)\n      bufoff += this._bufs[i].length\n    }\n\n    return dst\n  }\n\n  // easy, cheap case where it's a subset of one of the buffers\n  if (bytes <= this._bufs[off[0]].length - start) {\n    return copy\n      ? this._bufs[off[0]].copy(dst, dstStart, start, start + bytes)\n      : this._bufs[off[0]].slice(start, start + bytes)\n  }\n\n  if (!copy) {\n    // a slice, we need something to copy in to\n    dst = Buffer.allocUnsafe(len)\n  }\n\n  for (let i = off[0]; i < this._bufs.length; i++) {\n    const l = this._bufs[i].length - start\n\n    if (bytes > l) {\n      this._bufs[i].copy(dst, bufoff, start)\n      bufoff += l\n    } else {\n      this._bufs[i].copy(dst, bufoff, start, start + bytes)\n      bufoff += l\n      break\n    }\n\n    bytes -= l\n\n    if (start) {\n      start = 0\n    }\n  }\n\n  // safeguard so that we don't return uninitialized memory\n  if (dst.length > bufoff) return dst.slice(0, bufoff)\n\n  return dst\n}\n\nBufferList.prototype.shallowSlice = function shallowSlice (start, end) {\n  start = start || 0\n  end = typeof end !== 'number' ? this.length : end\n\n  if (start < 0) {\n    start += this.length\n  }\n\n  if (end < 0) {\n    end += this.length\n  }\n\n  if (start === end) {\n    return this._new()\n  }\n\n  const startOffset = this._offset(start)\n  const endOffset = this._offset(end)\n  const buffers = this._bufs.slice(startOffset[0], endOffset[0] + 1)\n\n  if (endOffset[1] === 0) {\n    buffers.pop()\n  } else {\n    buffers[buffers.length - 1] = buffers[buffers.length - 1].slice(0, endOffset[1])\n  }\n\n  if (startOffset[1] !== 0) {\n    buffers[0] = buffers[0].slice(startOffset[1])\n  }\n\n  return this._new(buffers)\n}\n\nBufferList.prototype.toString = function toString (encoding, start, end) {\n  return this.slice(start, end).toString(encoding)\n}\n\nBufferList.prototype.consume = function consume (bytes) {\n  // first, normalize the argument, in accordance with how Buffer does it\n  bytes = Math.trunc(bytes)\n  // do nothing if not a positive number\n  if (Number.isNaN(bytes) || bytes <= 0) return this\n\n  while (this._bufs.length) {\n    if (bytes >= this._bufs[0].length) {\n      bytes -= this._bufs[0].length\n      this.length -= this._bufs[0].length\n      this._bufs.shift()\n    } else {\n      this._bufs[0] = this._bufs[0].slice(bytes)\n      this.length -= bytes\n      break\n    }\n  }\n\n  return this\n}\n\nBufferList.prototype.duplicate = function duplicate () {\n  const copy = this._new()\n\n  for (let i = 0; i < this._bufs.length; i++) {\n    copy.append(this._bufs[i])\n  }\n\n  return copy\n}\n\nBufferList.prototype.append = function append (buf) {\n  if (buf == null) {\n    return this\n  }\n\n  if (buf.buffer) {\n    // append a view of the underlying ArrayBuffer\n    this._appendBuffer(Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength))\n  } else if (Array.isArray(buf)) {\n    for (let i = 0; i < buf.length; i++) {\n      this.append(buf[i])\n    }\n  } else if (this._isBufferList(buf)) {\n    // unwrap argument into individual BufferLists\n    for (let i = 0; i < buf._bufs.length; i++) {\n      this.append(buf._bufs[i])\n    }\n  } else {\n    // coerce number arguments to strings, since Buffer(number) does\n    // uninitialized memory allocation\n    if (typeof buf === 'number') {\n      buf = buf.toString()\n    }\n\n    this._appendBuffer(Buffer.from(buf))\n  }\n\n  return this\n}\n\nBufferList.prototype._appendBuffer = function appendBuffer (buf) {\n  this._bufs.push(buf)\n  this.length += buf.length\n}\n\nBufferList.prototype.indexOf = function (search, offset, encoding) {\n  if (encoding === undefined && typeof offset === 'string') {\n    encoding = offset\n    offset = undefined\n  }\n\n  if (typeof search === 'function' || Array.isArray(search)) {\n    throw new TypeError('The \"value\" argument must be one of type string, Buffer, BufferList, or Uint8Array.')\n  } else if (typeof search === 'number') {\n    search = Buffer.from([search])\n  } else if (typeof search === 'string') {\n    search = Buffer.from(search, encoding)\n  } else if (this._isBufferList(search)) {\n    search = search.slice()\n  } else if (Array.isArray(search.buffer)) {\n    search = Buffer.from(search.buffer, search.byteOffset, search.byteLength)\n  } else if (!Buffer.isBuffer(search)) {\n    search = Buffer.from(search)\n  }\n\n  offset = Number(offset || 0)\n\n  if (isNaN(offset)) {\n    offset = 0\n  }\n\n  if (offset < 0) {\n    offset = this.length + offset\n  }\n\n  if (offset < 0) {\n    offset = 0\n  }\n\n  if (search.length === 0) {\n    return offset > this.length ? this.length : offset\n  }\n\n  const blOffset = this._offset(offset)\n  let blIndex = blOffset[0] // index of which internal buffer we're working on\n  let buffOffset = blOffset[1] // offset of the internal buffer we're working on\n\n  // scan over each buffer\n  for (; blIndex < this._bufs.length; blIndex++) {\n    const buff = this._bufs[blIndex]\n\n    while (buffOffset < buff.length) {\n      const availableWindow = buff.length - buffOffset\n\n      if (availableWindow >= search.length) {\n        const nativeSearchResult = buff.indexOf(search, buffOffset)\n\n        if (nativeSearchResult !== -1) {\n          return this._reverseOffset([blIndex, nativeSearchResult])\n        }\n\n        buffOffset = buff.length - search.length + 1 // end of native search window\n      } else {\n        const revOffset = this._reverseOffset([blIndex, buffOffset])\n\n        if (this._match(revOffset, search)) {\n          return revOffset\n        }\n\n        buffOffset++\n      }\n    }\n\n    buffOffset = 0\n  }\n\n  return -1\n}\n\nBufferList.prototype._match = function (offset, search) {\n  if (this.length - offset < search.length) {\n    return false\n  }\n\n  for (let searchOffset = 0; searchOffset < search.length; searchOffset++) {\n    if (this.get(offset + searchOffset) !== search[searchOffset]) {\n      return false\n    }\n  }\n  return true\n}\n\n;(function () {\n  const methods = {\n    readDoubleBE: 8,\n    readDoubleLE: 8,\n    readFloatBE: 4,\n    readFloatLE: 4,\n    readInt32BE: 4,\n    readInt32LE: 4,\n    readUInt32BE: 4,\n    readUInt32LE: 4,\n    readInt16BE: 2,\n    readInt16LE: 2,\n    readUInt16BE: 2,\n    readUInt16LE: 2,\n    readInt8: 1,\n    readUInt8: 1,\n    readIntBE: null,\n    readIntLE: null,\n    readUIntBE: null,\n    readUIntLE: null\n  }\n\n  for (const m in methods) {\n    (function (m) {\n      if (methods[m] === null) {\n        BufferList.prototype[m] = function (offset, byteLength) {\n          return this.slice(offset, offset + byteLength)[m](0, byteLength)\n        }\n      } else {\n        BufferList.prototype[m] = function (offset = 0) {\n          return this.slice(offset, offset + methods[m])[m](0)\n        }\n      }\n    }(m))\n  }\n}())\n\n// Used internally by the class and also as an indicator of this object being\n// a `BufferList`. It's not possible to use `instanceof BufferList` in a browser\n// environment because there could be multiple different copies of the\n// BufferList class and some `BufferList`s might be `BufferList`s.\nBufferList.prototype._isBufferList = function _isBufferList (b) {\n  return b instanceof BufferList || BufferList.isBufferList(b)\n}\n\nBufferList.isBufferList = function isBufferList (b) {\n  return b != null && b[symbol]\n}\n\nmodule.exports = BufferList\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmwvQnVmZmVyTGlzdC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixRQUFRLFNBQVMsRUFBRSxtQkFBTyxDQUFDLHNCQUFRO0FBQ25DOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSx3Q0FBd0MsYUFBYTs7QUFFckQ7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsa0JBQWtCLHVCQUF1QjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCLGNBQWM7QUFDaEM7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IsdUJBQXVCO0FBQzNDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx1QkFBdUIsdUJBQXVCO0FBQzlDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxrQkFBa0IsdUJBQXVCO0FBQ3pDO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0osb0JBQW9CLGdCQUFnQjtBQUNwQztBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0Esb0JBQW9CLHNCQUFzQjtBQUMxQztBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxTQUFTLDZCQUE2QjtBQUN0Qzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsUUFBUTtBQUNSOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDZCQUE2Qiw4QkFBOEI7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50Ly4vbm9kZV9tb2R1bGVzL2JsL0J1ZmZlckxpc3QuanM/NmNkYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgeyBCdWZmZXIgfSA9IHJlcXVpcmUoJ2J1ZmZlcicpXG5jb25zdCBzeW1ib2wgPSBTeW1ib2wuZm9yKCdCdWZmZXJMaXN0JylcblxuZnVuY3Rpb24gQnVmZmVyTGlzdCAoYnVmKSB7XG4gIGlmICghKHRoaXMgaW5zdGFuY2VvZiBCdWZmZXJMaXN0KSkge1xuICAgIHJldHVybiBuZXcgQnVmZmVyTGlzdChidWYpXG4gIH1cblxuICBCdWZmZXJMaXN0Ll9pbml0LmNhbGwodGhpcywgYnVmKVxufVxuXG5CdWZmZXJMaXN0Ll9pbml0ID0gZnVuY3Rpb24gX2luaXQgKGJ1Zikge1xuICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgc3ltYm9sLCB7IHZhbHVlOiB0cnVlIH0pXG5cbiAgdGhpcy5fYnVmcyA9IFtdXG4gIHRoaXMubGVuZ3RoID0gMFxuXG4gIGlmIChidWYpIHtcbiAgICB0aGlzLmFwcGVuZChidWYpXG4gIH1cbn1cblxuQnVmZmVyTGlzdC5wcm90b3R5cGUuX25ldyA9IGZ1bmN0aW9uIF9uZXcgKGJ1Zikge1xuICByZXR1cm4gbmV3IEJ1ZmZlckxpc3QoYnVmKVxufVxuXG5CdWZmZXJMaXN0LnByb3RvdHlwZS5fb2Zmc2V0ID0gZnVuY3Rpb24gX29mZnNldCAob2Zmc2V0KSB7XG4gIGlmIChvZmZzZXQgPT09IDApIHtcbiAgICByZXR1cm4gWzAsIDBdXG4gIH1cblxuICBsZXQgdG90ID0gMFxuXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5fYnVmcy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IF90ID0gdG90ICsgdGhpcy5fYnVmc1tpXS5sZW5ndGhcbiAgICBpZiAob2Zmc2V0IDwgX3QgfHwgaSA9PT0gdGhpcy5fYnVmcy5sZW5ndGggLSAxKSB7XG4gICAgICByZXR1cm4gW2ksIG9mZnNldCAtIHRvdF1cbiAgICB9XG4gICAgdG90ID0gX3RcbiAgfVxufVxuXG5CdWZmZXJMaXN0LnByb3RvdHlwZS5fcmV2ZXJzZU9mZnNldCA9IGZ1bmN0aW9uIChibE9mZnNldCkge1xuICBjb25zdCBidWZmZXJJZCA9IGJsT2Zmc2V0WzBdXG4gIGxldCBvZmZzZXQgPSBibE9mZnNldFsxXVxuXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgYnVmZmVySWQ7IGkrKykge1xuICAgIG9mZnNldCArPSB0aGlzLl9idWZzW2ldLmxlbmd0aFxuICB9XG5cbiAgcmV0dXJuIG9mZnNldFxufVxuXG5CdWZmZXJMaXN0LnByb3RvdHlwZS5nZXQgPSBmdW5jdGlvbiBnZXQgKGluZGV4KSB7XG4gIGlmIChpbmRleCA+IHRoaXMubGVuZ3RoIHx8IGluZGV4IDwgMCkge1xuICAgIHJldHVybiB1bmRlZmluZWRcbiAgfVxuXG4gIGNvbnN0IG9mZnNldCA9IHRoaXMuX29mZnNldChpbmRleClcblxuICByZXR1cm4gdGhpcy5fYnVmc1tvZmZzZXRbMF1dW29mZnNldFsxXV1cbn1cblxuQnVmZmVyTGlzdC5wcm90b3R5cGUuc2xpY2UgPSBmdW5jdGlvbiBzbGljZSAoc3RhcnQsIGVuZCkge1xuICBpZiAodHlwZW9mIHN0YXJ0ID09PSAnbnVtYmVyJyAmJiBzdGFydCA8IDApIHtcbiAgICBzdGFydCArPSB0aGlzLmxlbmd0aFxuICB9XG5cbiAgaWYgKHR5cGVvZiBlbmQgPT09ICdudW1iZXInICYmIGVuZCA8IDApIHtcbiAgICBlbmQgKz0gdGhpcy5sZW5ndGhcbiAgfVxuXG4gIHJldHVybiB0aGlzLmNvcHkobnVsbCwgMCwgc3RhcnQsIGVuZClcbn1cblxuQnVmZmVyTGlzdC5wcm90b3R5cGUuY29weSA9IGZ1bmN0aW9uIGNvcHkgKGRzdCwgZHN0U3RhcnQsIHNyY1N0YXJ0LCBzcmNFbmQpIHtcbiAgaWYgKHR5cGVvZiBzcmNTdGFydCAhPT0gJ251bWJlcicgfHwgc3JjU3RhcnQgPCAwKSB7XG4gICAgc3JjU3RhcnQgPSAwXG4gIH1cblxuICBpZiAodHlwZW9mIHNyY0VuZCAhPT0gJ251bWJlcicgfHwgc3JjRW5kID4gdGhpcy5sZW5ndGgpIHtcbiAgICBzcmNFbmQgPSB0aGlzLmxlbmd0aFxuICB9XG5cbiAgaWYgKHNyY1N0YXJ0ID49IHRoaXMubGVuZ3RoKSB7XG4gICAgcmV0dXJuIGRzdCB8fCBCdWZmZXIuYWxsb2MoMClcbiAgfVxuXG4gIGlmIChzcmNFbmQgPD0gMCkge1xuICAgIHJldHVybiBkc3QgfHwgQnVmZmVyLmFsbG9jKDApXG4gIH1cblxuICBjb25zdCBjb3B5ID0gISFkc3RcbiAgY29uc3Qgb2ZmID0gdGhpcy5fb2Zmc2V0KHNyY1N0YXJ0KVxuICBjb25zdCBsZW4gPSBzcmNFbmQgLSBzcmNTdGFydFxuICBsZXQgYnl0ZXMgPSBsZW5cbiAgbGV0IGJ1Zm9mZiA9IChjb3B5ICYmIGRzdFN0YXJ0KSB8fCAwXG4gIGxldCBzdGFydCA9IG9mZlsxXVxuXG4gIC8vIGNvcHkvc2xpY2UgZXZlcnl0aGluZ1xuICBpZiAoc3JjU3RhcnQgPT09IDAgJiYgc3JjRW5kID09PSB0aGlzLmxlbmd0aCkge1xuICAgIGlmICghY29weSkge1xuICAgICAgLy8gc2xpY2UsIGJ1dCBmdWxsIGNvbmNhdCBpZiBtdWx0aXBsZSBidWZmZXJzXG4gICAgICByZXR1cm4gdGhpcy5fYnVmcy5sZW5ndGggPT09IDFcbiAgICAgICAgPyB0aGlzLl9idWZzWzBdXG4gICAgICAgIDogQnVmZmVyLmNvbmNhdCh0aGlzLl9idWZzLCB0aGlzLmxlbmd0aClcbiAgICB9XG5cbiAgICAvLyBjb3B5LCBuZWVkIHRvIGNvcHkgaW5kaXZpZHVhbCBidWZmZXJzXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLl9idWZzLmxlbmd0aDsgaSsrKSB7XG4gICAgICB0aGlzLl9idWZzW2ldLmNvcHkoZHN0LCBidWZvZmYpXG4gICAgICBidWZvZmYgKz0gdGhpcy5fYnVmc1tpXS5sZW5ndGhcbiAgICB9XG5cbiAgICByZXR1cm4gZHN0XG4gIH1cblxuICAvLyBlYXN5LCBjaGVhcCBjYXNlIHdoZXJlIGl0J3MgYSBzdWJzZXQgb2Ygb25lIG9mIHRoZSBidWZmZXJzXG4gIGlmIChieXRlcyA8PSB0aGlzLl9idWZzW29mZlswXV0ubGVuZ3RoIC0gc3RhcnQpIHtcbiAgICByZXR1cm4gY29weVxuICAgICAgPyB0aGlzLl9idWZzW29mZlswXV0uY29weShkc3QsIGRzdFN0YXJ0LCBzdGFydCwgc3RhcnQgKyBieXRlcylcbiAgICAgIDogdGhpcy5fYnVmc1tvZmZbMF1dLnNsaWNlKHN0YXJ0LCBzdGFydCArIGJ5dGVzKVxuICB9XG5cbiAgaWYgKCFjb3B5KSB7XG4gICAgLy8gYSBzbGljZSwgd2UgbmVlZCBzb21ldGhpbmcgdG8gY29weSBpbiB0b1xuICAgIGRzdCA9IEJ1ZmZlci5hbGxvY1Vuc2FmZShsZW4pXG4gIH1cblxuICBmb3IgKGxldCBpID0gb2ZmWzBdOyBpIDwgdGhpcy5fYnVmcy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGwgPSB0aGlzLl9idWZzW2ldLmxlbmd0aCAtIHN0YXJ0XG5cbiAgICBpZiAoYnl0ZXMgPiBsKSB7XG4gICAgICB0aGlzLl9idWZzW2ldLmNvcHkoZHN0LCBidWZvZmYsIHN0YXJ0KVxuICAgICAgYnVmb2ZmICs9IGxcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5fYnVmc1tpXS5jb3B5KGRzdCwgYnVmb2ZmLCBzdGFydCwgc3RhcnQgKyBieXRlcylcbiAgICAgIGJ1Zm9mZiArPSBsXG4gICAgICBicmVha1xuICAgIH1cblxuICAgIGJ5dGVzIC09IGxcblxuICAgIGlmIChzdGFydCkge1xuICAgICAgc3RhcnQgPSAwXG4gICAgfVxuICB9XG5cbiAgLy8gc2FmZWd1YXJkIHNvIHRoYXQgd2UgZG9uJ3QgcmV0dXJuIHVuaW5pdGlhbGl6ZWQgbWVtb3J5XG4gIGlmIChkc3QubGVuZ3RoID4gYnVmb2ZmKSByZXR1cm4gZHN0LnNsaWNlKDAsIGJ1Zm9mZilcblxuICByZXR1cm4gZHN0XG59XG5cbkJ1ZmZlckxpc3QucHJvdG90eXBlLnNoYWxsb3dTbGljZSA9IGZ1bmN0aW9uIHNoYWxsb3dTbGljZSAoc3RhcnQsIGVuZCkge1xuICBzdGFydCA9IHN0YXJ0IHx8IDBcbiAgZW5kID0gdHlwZW9mIGVuZCAhPT0gJ251bWJlcicgPyB0aGlzLmxlbmd0aCA6IGVuZFxuXG4gIGlmIChzdGFydCA8IDApIHtcbiAgICBzdGFydCArPSB0aGlzLmxlbmd0aFxuICB9XG5cbiAgaWYgKGVuZCA8IDApIHtcbiAgICBlbmQgKz0gdGhpcy5sZW5ndGhcbiAgfVxuXG4gIGlmIChzdGFydCA9PT0gZW5kKSB7XG4gICAgcmV0dXJuIHRoaXMuX25ldygpXG4gIH1cblxuICBjb25zdCBzdGFydE9mZnNldCA9IHRoaXMuX29mZnNldChzdGFydClcbiAgY29uc3QgZW5kT2Zmc2V0ID0gdGhpcy5fb2Zmc2V0KGVuZClcbiAgY29uc3QgYnVmZmVycyA9IHRoaXMuX2J1ZnMuc2xpY2Uoc3RhcnRPZmZzZXRbMF0sIGVuZE9mZnNldFswXSArIDEpXG5cbiAgaWYgKGVuZE9mZnNldFsxXSA9PT0gMCkge1xuICAgIGJ1ZmZlcnMucG9wKClcbiAgfSBlbHNlIHtcbiAgICBidWZmZXJzW2J1ZmZlcnMubGVuZ3RoIC0gMV0gPSBidWZmZXJzW2J1ZmZlcnMubGVuZ3RoIC0gMV0uc2xpY2UoMCwgZW5kT2Zmc2V0WzFdKVxuICB9XG5cbiAgaWYgKHN0YXJ0T2Zmc2V0WzFdICE9PSAwKSB7XG4gICAgYnVmZmVyc1swXSA9IGJ1ZmZlcnNbMF0uc2xpY2Uoc3RhcnRPZmZzZXRbMV0pXG4gIH1cblxuICByZXR1cm4gdGhpcy5fbmV3KGJ1ZmZlcnMpXG59XG5cbkJ1ZmZlckxpc3QucHJvdG90eXBlLnRvU3RyaW5nID0gZnVuY3Rpb24gdG9TdHJpbmcgKGVuY29kaW5nLCBzdGFydCwgZW5kKSB7XG4gIHJldHVybiB0aGlzLnNsaWNlKHN0YXJ0LCBlbmQpLnRvU3RyaW5nKGVuY29kaW5nKVxufVxuXG5CdWZmZXJMaXN0LnByb3RvdHlwZS5jb25zdW1lID0gZnVuY3Rpb24gY29uc3VtZSAoYnl0ZXMpIHtcbiAgLy8gZmlyc3QsIG5vcm1hbGl6ZSB0aGUgYXJndW1lbnQsIGluIGFjY29yZGFuY2Ugd2l0aCBob3cgQnVmZmVyIGRvZXMgaXRcbiAgYnl0ZXMgPSBNYXRoLnRydW5jKGJ5dGVzKVxuICAvLyBkbyBub3RoaW5nIGlmIG5vdCBhIHBvc2l0aXZlIG51bWJlclxuICBpZiAoTnVtYmVyLmlzTmFOKGJ5dGVzKSB8fCBieXRlcyA8PSAwKSByZXR1cm4gdGhpc1xuXG4gIHdoaWxlICh0aGlzLl9idWZzLmxlbmd0aCkge1xuICAgIGlmIChieXRlcyA+PSB0aGlzLl9idWZzWzBdLmxlbmd0aCkge1xuICAgICAgYnl0ZXMgLT0gdGhpcy5fYnVmc1swXS5sZW5ndGhcbiAgICAgIHRoaXMubGVuZ3RoIC09IHRoaXMuX2J1ZnNbMF0ubGVuZ3RoXG4gICAgICB0aGlzLl9idWZzLnNoaWZ0KClcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5fYnVmc1swXSA9IHRoaXMuX2J1ZnNbMF0uc2xpY2UoYnl0ZXMpXG4gICAgICB0aGlzLmxlbmd0aCAtPSBieXRlc1xuICAgICAgYnJlYWtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdGhpc1xufVxuXG5CdWZmZXJMaXN0LnByb3RvdHlwZS5kdXBsaWNhdGUgPSBmdW5jdGlvbiBkdXBsaWNhdGUgKCkge1xuICBjb25zdCBjb3B5ID0gdGhpcy5fbmV3KClcblxuICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuX2J1ZnMubGVuZ3RoOyBpKyspIHtcbiAgICBjb3B5LmFwcGVuZCh0aGlzLl9idWZzW2ldKVxuICB9XG5cbiAgcmV0dXJuIGNvcHlcbn1cblxuQnVmZmVyTGlzdC5wcm90b3R5cGUuYXBwZW5kID0gZnVuY3Rpb24gYXBwZW5kIChidWYpIHtcbiAgaWYgKGJ1ZiA9PSBudWxsKSB7XG4gICAgcmV0dXJuIHRoaXNcbiAgfVxuXG4gIGlmIChidWYuYnVmZmVyKSB7XG4gICAgLy8gYXBwZW5kIGEgdmlldyBvZiB0aGUgdW5kZXJseWluZyBBcnJheUJ1ZmZlclxuICAgIHRoaXMuX2FwcGVuZEJ1ZmZlcihCdWZmZXIuZnJvbShidWYuYnVmZmVyLCBidWYuYnl0ZU9mZnNldCwgYnVmLmJ5dGVMZW5ndGgpKVxuICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoYnVmKSkge1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYnVmLmxlbmd0aDsgaSsrKSB7XG4gICAgICB0aGlzLmFwcGVuZChidWZbaV0pXG4gICAgfVxuICB9IGVsc2UgaWYgKHRoaXMuX2lzQnVmZmVyTGlzdChidWYpKSB7XG4gICAgLy8gdW53cmFwIGFyZ3VtZW50IGludG8gaW5kaXZpZHVhbCBCdWZmZXJMaXN0c1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYnVmLl9idWZzLmxlbmd0aDsgaSsrKSB7XG4gICAgICB0aGlzLmFwcGVuZChidWYuX2J1ZnNbaV0pXG4gICAgfVxuICB9IGVsc2Uge1xuICAgIC8vIGNvZXJjZSBudW1iZXIgYXJndW1lbnRzIHRvIHN0cmluZ3MsIHNpbmNlIEJ1ZmZlcihudW1iZXIpIGRvZXNcbiAgICAvLyB1bmluaXRpYWxpemVkIG1lbW9yeSBhbGxvY2F0aW9uXG4gICAgaWYgKHR5cGVvZiBidWYgPT09ICdudW1iZXInKSB7XG4gICAgICBidWYgPSBidWYudG9TdHJpbmcoKVxuICAgIH1cblxuICAgIHRoaXMuX2FwcGVuZEJ1ZmZlcihCdWZmZXIuZnJvbShidWYpKVxuICB9XG5cbiAgcmV0dXJuIHRoaXNcbn1cblxuQnVmZmVyTGlzdC5wcm90b3R5cGUuX2FwcGVuZEJ1ZmZlciA9IGZ1bmN0aW9uIGFwcGVuZEJ1ZmZlciAoYnVmKSB7XG4gIHRoaXMuX2J1ZnMucHVzaChidWYpXG4gIHRoaXMubGVuZ3RoICs9IGJ1Zi5sZW5ndGhcbn1cblxuQnVmZmVyTGlzdC5wcm90b3R5cGUuaW5kZXhPZiA9IGZ1bmN0aW9uIChzZWFyY2gsIG9mZnNldCwgZW5jb2RpbmcpIHtcbiAgaWYgKGVuY29kaW5nID09PSB1bmRlZmluZWQgJiYgdHlwZW9mIG9mZnNldCA9PT0gJ3N0cmluZycpIHtcbiAgICBlbmNvZGluZyA9IG9mZnNldFxuICAgIG9mZnNldCA9IHVuZGVmaW5lZFxuICB9XG5cbiAgaWYgKHR5cGVvZiBzZWFyY2ggPT09ICdmdW5jdGlvbicgfHwgQXJyYXkuaXNBcnJheShzZWFyY2gpKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcignVGhlIFwidmFsdWVcIiBhcmd1bWVudCBtdXN0IGJlIG9uZSBvZiB0eXBlIHN0cmluZywgQnVmZmVyLCBCdWZmZXJMaXN0LCBvciBVaW50OEFycmF5LicpXG4gIH0gZWxzZSBpZiAodHlwZW9mIHNlYXJjaCA9PT0gJ251bWJlcicpIHtcbiAgICBzZWFyY2ggPSBCdWZmZXIuZnJvbShbc2VhcmNoXSlcbiAgfSBlbHNlIGlmICh0eXBlb2Ygc2VhcmNoID09PSAnc3RyaW5nJykge1xuICAgIHNlYXJjaCA9IEJ1ZmZlci5mcm9tKHNlYXJjaCwgZW5jb2RpbmcpXG4gIH0gZWxzZSBpZiAodGhpcy5faXNCdWZmZXJMaXN0KHNlYXJjaCkpIHtcbiAgICBzZWFyY2ggPSBzZWFyY2guc2xpY2UoKVxuICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoc2VhcmNoLmJ1ZmZlcikpIHtcbiAgICBzZWFyY2ggPSBCdWZmZXIuZnJvbShzZWFyY2guYnVmZmVyLCBzZWFyY2guYnl0ZU9mZnNldCwgc2VhcmNoLmJ5dGVMZW5ndGgpXG4gIH0gZWxzZSBpZiAoIUJ1ZmZlci5pc0J1ZmZlcihzZWFyY2gpKSB7XG4gICAgc2VhcmNoID0gQnVmZmVyLmZyb20oc2VhcmNoKVxuICB9XG5cbiAgb2Zmc2V0ID0gTnVtYmVyKG9mZnNldCB8fCAwKVxuXG4gIGlmIChpc05hTihvZmZzZXQpKSB7XG4gICAgb2Zmc2V0ID0gMFxuICB9XG5cbiAgaWYgKG9mZnNldCA8IDApIHtcbiAgICBvZmZzZXQgPSB0aGlzLmxlbmd0aCArIG9mZnNldFxuICB9XG5cbiAgaWYgKG9mZnNldCA8IDApIHtcbiAgICBvZmZzZXQgPSAwXG4gIH1cblxuICBpZiAoc2VhcmNoLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBvZmZzZXQgPiB0aGlzLmxlbmd0aCA/IHRoaXMubGVuZ3RoIDogb2Zmc2V0XG4gIH1cblxuICBjb25zdCBibE9mZnNldCA9IHRoaXMuX29mZnNldChvZmZzZXQpXG4gIGxldCBibEluZGV4ID0gYmxPZmZzZXRbMF0gLy8gaW5kZXggb2Ygd2hpY2ggaW50ZXJuYWwgYnVmZmVyIHdlJ3JlIHdvcmtpbmcgb25cbiAgbGV0IGJ1ZmZPZmZzZXQgPSBibE9mZnNldFsxXSAvLyBvZmZzZXQgb2YgdGhlIGludGVybmFsIGJ1ZmZlciB3ZSdyZSB3b3JraW5nIG9uXG5cbiAgLy8gc2NhbiBvdmVyIGVhY2ggYnVmZmVyXG4gIGZvciAoOyBibEluZGV4IDwgdGhpcy5fYnVmcy5sZW5ndGg7IGJsSW5kZXgrKykge1xuICAgIGNvbnN0IGJ1ZmYgPSB0aGlzLl9idWZzW2JsSW5kZXhdXG5cbiAgICB3aGlsZSAoYnVmZk9mZnNldCA8IGJ1ZmYubGVuZ3RoKSB7XG4gICAgICBjb25zdCBhdmFpbGFibGVXaW5kb3cgPSBidWZmLmxlbmd0aCAtIGJ1ZmZPZmZzZXRcblxuICAgICAgaWYgKGF2YWlsYWJsZVdpbmRvdyA+PSBzZWFyY2gubGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IG5hdGl2ZVNlYXJjaFJlc3VsdCA9IGJ1ZmYuaW5kZXhPZihzZWFyY2gsIGJ1ZmZPZmZzZXQpXG5cbiAgICAgICAgaWYgKG5hdGl2ZVNlYXJjaFJlc3VsdCAhPT0gLTEpIHtcbiAgICAgICAgICByZXR1cm4gdGhpcy5fcmV2ZXJzZU9mZnNldChbYmxJbmRleCwgbmF0aXZlU2VhcmNoUmVzdWx0XSlcbiAgICAgICAgfVxuXG4gICAgICAgIGJ1ZmZPZmZzZXQgPSBidWZmLmxlbmd0aCAtIHNlYXJjaC5sZW5ndGggKyAxIC8vIGVuZCBvZiBuYXRpdmUgc2VhcmNoIHdpbmRvd1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgcmV2T2Zmc2V0ID0gdGhpcy5fcmV2ZXJzZU9mZnNldChbYmxJbmRleCwgYnVmZk9mZnNldF0pXG5cbiAgICAgICAgaWYgKHRoaXMuX21hdGNoKHJldk9mZnNldCwgc2VhcmNoKSkge1xuICAgICAgICAgIHJldHVybiByZXZPZmZzZXRcbiAgICAgICAgfVxuXG4gICAgICAgIGJ1ZmZPZmZzZXQrK1xuICAgICAgfVxuICAgIH1cblxuICAgIGJ1ZmZPZmZzZXQgPSAwXG4gIH1cblxuICByZXR1cm4gLTFcbn1cblxuQnVmZmVyTGlzdC5wcm90b3R5cGUuX21hdGNoID0gZnVuY3Rpb24gKG9mZnNldCwgc2VhcmNoKSB7XG4gIGlmICh0aGlzLmxlbmd0aCAtIG9mZnNldCA8IHNlYXJjaC5sZW5ndGgpIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIGZvciAobGV0IHNlYXJjaE9mZnNldCA9IDA7IHNlYXJjaE9mZnNldCA8IHNlYXJjaC5sZW5ndGg7IHNlYXJjaE9mZnNldCsrKSB7XG4gICAgaWYgKHRoaXMuZ2V0KG9mZnNldCArIHNlYXJjaE9mZnNldCkgIT09IHNlYXJjaFtzZWFyY2hPZmZzZXRdKSB7XG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHRydWVcbn1cblxuOyhmdW5jdGlvbiAoKSB7XG4gIGNvbnN0IG1ldGhvZHMgPSB7XG4gICAgcmVhZERvdWJsZUJFOiA4LFxuICAgIHJlYWREb3VibGVMRTogOCxcbiAgICByZWFkRmxvYXRCRTogNCxcbiAgICByZWFkRmxvYXRMRTogNCxcbiAgICByZWFkSW50MzJCRTogNCxcbiAgICByZWFkSW50MzJMRTogNCxcbiAgICByZWFkVUludDMyQkU6IDQsXG4gICAgcmVhZFVJbnQzMkxFOiA0LFxuICAgIHJlYWRJbnQxNkJFOiAyLFxuICAgIHJlYWRJbnQxNkxFOiAyLFxuICAgIHJlYWRVSW50MTZCRTogMixcbiAgICByZWFkVUludDE2TEU6IDIsXG4gICAgcmVhZEludDg6IDEsXG4gICAgcmVhZFVJbnQ4OiAxLFxuICAgIHJlYWRJbnRCRTogbnVsbCxcbiAgICByZWFkSW50TEU6IG51bGwsXG4gICAgcmVhZFVJbnRCRTogbnVsbCxcbiAgICByZWFkVUludExFOiBudWxsXG4gIH1cblxuICBmb3IgKGNvbnN0IG0gaW4gbWV0aG9kcykge1xuICAgIChmdW5jdGlvbiAobSkge1xuICAgICAgaWYgKG1ldGhvZHNbbV0gPT09IG51bGwpIHtcbiAgICAgICAgQnVmZmVyTGlzdC5wcm90b3R5cGVbbV0gPSBmdW5jdGlvbiAob2Zmc2V0LCBieXRlTGVuZ3RoKSB7XG4gICAgICAgICAgcmV0dXJuIHRoaXMuc2xpY2Uob2Zmc2V0LCBvZmZzZXQgKyBieXRlTGVuZ3RoKVttXSgwLCBieXRlTGVuZ3RoKVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBCdWZmZXJMaXN0LnByb3RvdHlwZVttXSA9IGZ1bmN0aW9uIChvZmZzZXQgPSAwKSB7XG4gICAgICAgICAgcmV0dXJuIHRoaXMuc2xpY2Uob2Zmc2V0LCBvZmZzZXQgKyBtZXRob2RzW21dKVttXSgwKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfShtKSlcbiAgfVxufSgpKVxuXG4vLyBVc2VkIGludGVybmFsbHkgYnkgdGhlIGNsYXNzIGFuZCBhbHNvIGFzIGFuIGluZGljYXRvciBvZiB0aGlzIG9iamVjdCBiZWluZ1xuLy8gYSBgQnVmZmVyTGlzdGAuIEl0J3Mgbm90IHBvc3NpYmxlIHRvIHVzZSBgaW5zdGFuY2VvZiBCdWZmZXJMaXN0YCBpbiBhIGJyb3dzZXJcbi8vIGVudmlyb25tZW50IGJlY2F1c2UgdGhlcmUgY291bGQgYmUgbXVsdGlwbGUgZGlmZmVyZW50IGNvcGllcyBvZiB0aGVcbi8vIEJ1ZmZlckxpc3QgY2xhc3MgYW5kIHNvbWUgYEJ1ZmZlckxpc3RgcyBtaWdodCBiZSBgQnVmZmVyTGlzdGBzLlxuQnVmZmVyTGlzdC5wcm90b3R5cGUuX2lzQnVmZmVyTGlzdCA9IGZ1bmN0aW9uIF9pc0J1ZmZlckxpc3QgKGIpIHtcbiAgcmV0dXJuIGIgaW5zdGFuY2VvZiBCdWZmZXJMaXN0IHx8IEJ1ZmZlckxpc3QuaXNCdWZmZXJMaXN0KGIpXG59XG5cbkJ1ZmZlckxpc3QuaXNCdWZmZXJMaXN0ID0gZnVuY3Rpb24gaXNCdWZmZXJMaXN0IChiKSB7XG4gIHJldHVybiBiICE9IG51bGwgJiYgYltzeW1ib2xdXG59XG5cbm1vZHVsZS5leHBvcnRzID0gQnVmZmVyTGlzdFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bl/BufferList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bl/bl.js":
/*!*******************************!*\
  !*** ./node_modules/bl/bl.js ***!
  \*******************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst DuplexStream = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Duplex)\nconst inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nconst BufferList = __webpack_require__(/*! ./BufferList */ \"(ssr)/./node_modules/bl/BufferList.js\")\n\nfunction BufferListStream (callback) {\n  if (!(this instanceof BufferListStream)) {\n    return new BufferListStream(callback)\n  }\n\n  if (typeof callback === 'function') {\n    this._callback = callback\n\n    const piper = function piper (err) {\n      if (this._callback) {\n        this._callback(err)\n        this._callback = null\n      }\n    }.bind(this)\n\n    this.on('pipe', function onPipe (src) {\n      src.on('error', piper)\n    })\n    this.on('unpipe', function onUnpipe (src) {\n      src.removeListener('error', piper)\n    })\n\n    callback = null\n  }\n\n  BufferList._init.call(this, callback)\n  DuplexStream.call(this)\n}\n\ninherits(BufferListStream, DuplexStream)\nObject.assign(BufferListStream.prototype, BufferList.prototype)\n\nBufferListStream.prototype._new = function _new (callback) {\n  return new BufferListStream(callback)\n}\n\nBufferListStream.prototype._write = function _write (buf, encoding, callback) {\n  this._appendBuffer(buf)\n\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nBufferListStream.prototype._read = function _read (size) {\n  if (!this.length) {\n    return this.push(null)\n  }\n\n  size = Math.min(size, this.length)\n  this.push(this.slice(0, size))\n  this.consume(size)\n}\n\nBufferListStream.prototype.end = function end (chunk) {\n  DuplexStream.prototype.end.call(this, chunk)\n\n  if (this._callback) {\n    this._callback(null, this.slice())\n    this._callback = null\n  }\n}\n\nBufferListStream.prototype._destroy = function _destroy (err, cb) {\n  this._bufs.length = 0\n  this.length = 0\n  cb(err)\n}\n\nBufferListStream.prototype._isBufferList = function _isBufferList (b) {\n  return b instanceof BufferListStream || b instanceof BufferList || BufferListStream.isBufferList(b)\n}\n\nBufferListStream.isBufferList = BufferList.isBufferList\n\nmodule.exports = BufferListStream\nmodule.exports.BufferListStream = BufferListStream\nmodule.exports.BufferList = BufferList\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bl/bl.js\n");

/***/ })

};
;