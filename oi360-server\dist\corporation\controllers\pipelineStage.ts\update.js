"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePipelineStage = void 0;
const operation_1 = require("../../../utils/operation");
const updatePipelineStage = async (req, res) => {
    const id = req.params.id;
    const { updatedBy, name, description } = req.body;
    const fields = {
        name: name,
        description: description,
        updatedBy: updatedBy,
    };
    await (0, operation_1.updateItem)({
        model: "PipelineStage",
        fieldName: "id",
        fields: fields,
        id: id,
        res: res,
        req: req,
        successMessage: "Pipeline stage updated successfully",
    });
};
exports.updatePipelineStage = updatePipelineStage;
//# sourceMappingURL=update.js.map