# Manage Pipelines Module - Technical Documentation

## Overview

The Manage Pipelines Module is a comprehensive workflow management system that allows administrators to create, configure, and manage task pipelines with customizable stages. The module provides drag-and-drop stage reordering, work type categorization, pipeline status management, and complete CRUD operations for pipelines and their stages.

## Architecture Overview

### Frontend Architecture (React/Next.js)

#### Component Structure
```
oi360-client/app/pms/manage_pipelines/
├── page.tsx                           # Main server component with data fetching
├── PipelineManager.tsx                # Client-side pipeline management logic
├── types.ts                           # TypeScript type definitions
├── CreatePipelineDialog.tsx           # Pipeline creation modal
├── PipelineList.tsx                   # Pipeline listing container
├── PipelineCard.tsx                   # Individual pipeline card component
└── FilterBar.tsx                      # Search and filtering interface
```

#### Key Technologies
- **React 18** with Next.js App Router
- **HTML5 Drag and Drop API** for stage reordering
- **React Select** for dropdown components
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **Sonner** for toast notifications
- **TypeScript** for type safety

#### State Management
- **Local State**: React useState hooks for component-level state
- **Pipeline Management**: Complex state for pipelines, stages, and work types
- **Drag-and-Drop State**: Stage reordering and visual feedback
- **Filter State**: Search, work type, status, and date filtering
- **Loading States**: Individual loading states for different operations
- **Permission Management**: Role-based access control integration

#### Drag-and-Drop Implementation
- **Stage Reordering**: HTML5 drag-and-drop for stage order management
- **Visual Feedback**: Drag indicators and hover states
- **Order Tracking**: Reordered pipelines tracking for save/reset functionality
- **Collision Detection**: Proper drop zone handling

### Backend Architecture (Node.js/Express/Prisma)

#### API Endpoints
```
Base URL: /api/

Pipeline Management:
├── POST   /pipelines                      # Create new pipeline
├── GET    /pipelines                      # Fetch all pipelines with stages
├── GET    /pipelines/:id                  # Fetch specific pipeline
├── GET    /pipelines/name/:name           # Fetch pipeline by name
├── PUT    /pipelines/:id                  # Update pipeline metadata
├── DELETE /pipelines/:id                  # Soft delete pipeline
├── GET    /pipelines/workTypes            # Fetch all work types
└── GET    /pipelines/workType/:workType   # Fetch pipelines by work type

Pipeline Stage Management:
├── POST   /pipelines/:id/stages           # Add stages to pipeline
├── PATCH  /pipelines/:id/orders           # Update stage order
├── GET    /pipeline-stages                # Fetch all pipeline stages
├── GET    /pipeline-stages/:id            # Fetch specific stage
├── PUT    /pipeline-stages/:id            # Update stage properties
└── DELETE /pipeline-stages/:id            # Soft delete stage
```

#### Controller Structure
```
oi360-server/src/corporation/controllers/
├── pipeline/
│   ├── create.ts                      # Pipeline creation logic
│   ├── view.ts                        # Pipeline retrieval operations
│   ├── update.ts                      # Pipeline update operations
│   ├── delete.ts                      # Pipeline soft deletion
│   ├── createStage.ts                 # Stage creation and management
│   └── updateOrder.ts                 # Stage order management
└── pipelineStage.ts/
    ├── view.ts                        # Stage retrieval operations
    ├── update.ts                      # Stage update operations
    └── delete.ts                      # Stage soft deletion
```

#### Business Logic

##### Pipeline Management
- **Work Type Validation**: Ensures valid Worktype enum values
- **Default Stage Creation**: Automatically creates "Done" stage with reserved order
- **Soft Deletion**: Marks pipelines as deleted with cascade to stages and tickets
- **Corporation Scoping**: Associates pipelines with specific corporations

##### Stage Management
- **Order Management**: Maintains stage ordering with reserved "Done" stage at order 100
- **Duplicate Prevention**: Handles duplicate stage names within pipelines
- **Stage Reordering**: Bulk update operations for stage order changes
- **Reserved Orders**: Prevents non-"Done" stages from using order >= 100

##### Data Integrity
- **Transaction Safety**: Uses Prisma transactions for atomic operations
- **Cascade Operations**: Soft deletes related entities when pipeline is deleted
- **Validation**: Comprehensive input validation and sanitization
- **Error Handling**: Structured error responses with proper HTTP codes

### Database Schema

#### Core Models

##### Pipeline Model
```prisma
model Pipeline {
  id            String       @id @default(uuid())
  name          String?      @db.VarChar()
  description   String?      @db.VarChar()
  workType      Worktype     @map("work_type")
  isActive      Boolean?     @default(true) @map("is_active")
  corporationId Int?         @map("corporation_id")
  
  stages        PipelineStage[]
  tickets       Ticket[]
  corporation   Corporation? @relation(fields: [corporationId], references: [corporation_id], onDelete: Cascade)
  
  createdAt     DateTime     @default(now()) @map("created_at")
  createdBy     String?      @map("created_by")
  updatedAt     DateTime?    @updatedAt @map("updated_at")
  updatedBy     String?      @map("updated_by")
  deletedAt     DateTime?    @map("deleted_at")
  deletedBy     String?      @map("deleted_by")
}
```

##### PipelineStage Model
```prisma
model PipelineStage {
  id          String    @id @default(uuid())
  pipelineId  String?   @map("pipeline_id")
  name        String?   @db.VarChar()
  description String?   @db.VarChar()
  order       Int
  
  pipeline    Pipeline? @relation(fields: [pipelineId], references: [id], onDelete: Cascade)
  ticketStages TicketStage[]
  timeTracking TicketStageTimeTracking[]
  
  createdAt   DateTime  @default(now()) @map("created_at")
  createdBy   String?   @map("created_by")
  updatedAt   DateTime? @updatedAt @map("updated_at")
  updatedBy   String?   @map("updated_by")
  deletedAt   DateTime? @map("deleted_at")
  deletedBy   String?   @map("deleted_by")
  
  @@unique([pipelineId, order])
}
```

##### Worktype Enum
```prisma
enum Worktype {
  trackSheets
  // Additional work types can be added here
}
```

##### Ticket Model (Related)
```prisma
model Ticket {
  id             String    @id @default(uuid())
  title          String?   @db.VarChar()
  description    String?   @db.Text()
  owner          String?   @db.VarChar()
  priority       String?   @db.VarChar()
  workItemId     String    @map("work_item_id")
  pipelineId     String?   @map("pipeline_id")
  currentStageId String?   @map("current_stage_id")
  
  pipeline       Pipeline? @relation(fields: [pipelineId], references: [id], onDelete: Cascade)
  stages         TicketStage[]
  comments       Comment[]
  timeTracking   TicketStageTimeTracking[]
  tags           String[]  @db.VarChar()
  
  // Audit fields...
}
```

#### Relationships
- **One-to-Many**: Pipeline → PipelineStage
- **One-to-Many**: Pipeline → Ticket
- **One-to-Many**: PipelineStage → TicketStage
- **Many-to-One**: Pipeline → Corporation
- **Unique Constraint**: (pipelineId, order) for stage ordering

#### Data Flow
1. **Pipeline Creation**: Creates pipeline with default "Done" stage
2. **Stage Management**: Stages ordered with "Done" reserved at order 100
3. **Ticket Association**: Tickets linked to pipelines and current stages
4. **Workflow Progression**: Tickets move through pipeline stages
5. **Soft Deletion**: Maintains referential integrity with timestamp deletion

## Feature Specifications

### Pipeline Management Features
- **Pipeline Creation**: Create pipelines with name, description, and work type
- **Pipeline Editing**: Inline editing of pipeline metadata
- **Status Toggle**: Enable/disable pipelines with visual indicators
- **Work Type Categorization**: Organize pipelines by work type
- **Pipeline Deletion**: Soft delete with cascade to stages and tickets

### Stage Management Features
- **Stage Creation**: Add custom stages to pipelines
- **Stage Editing**: Inline editing of stage name and description
- **Stage Reordering**: Drag-and-drop stage order management
- **Stage Deletion**: Remove stages with validation
- **Reserved Stages**: "Done" stage with special order handling

### Filtering and Search Features
- **Text Search**: Search across pipeline names, descriptions, and stages
- **Work Type Filter**: Filter pipelines by work type
- **Status Filter**: Filter by active/inactive status
- **Stage Count Filter**: Filter by number of stages
- **Date Filter**: Filter by creation date
- **Combined Filters**: Multiple filter criteria support

### UI/UX Features
- **Responsive Design**: Mobile-friendly interface
- **Visual Feedback**: Loading states and operation indicators
- **Drag-and-Drop**: Intuitive stage reordering
- **Inline Editing**: Quick edit functionality
- **Color Coding**: Work type and stage color indicators

## API Integration Patterns

#### Pipeline Operations
```typescript
// Create pipeline
const createPipeline = async (pipelineData: {
  name: string;
  description?: string;
  workType: string;
  createdBy: string;
}) => {
  return await formSubmit('/api/pipelines', 'POST', pipelineData);
};

// Update pipeline
const updatePipeline = async (pipelineId: string, updates: Partial<Pipeline>) => {
  return await formSubmit(`/api/pipelines/${pipelineId}`, 'PUT', updates);
};
```

#### Stage Operations
```typescript
// Add stage
const addStage = async (pipelineId: string, stageData: {
  name: string;
  description?: string;
  createdBy: string;
}) => {
  return await formSubmit(`/api/pipelines/${pipelineId}/stages`, 'POST', { stages: [stageData] });
};

// Update stage order
const updateStageOrder = async (pipelineId: string, stageOrders: Array<{id: string, order: number}>) => {
  return await formSubmit(`/api/pipelines/${pipelineId}/orders`, 'PATCH', { stageOrders });
};
```

## Security & Permissions

### Access Control
- **Authentication**: JWT-based authentication for most endpoints
- **Corporation Scoping**: Pipelines scoped to user's corporation
- **Role-Based Access**: Permission-based operation control
- **Soft Deletion**: Maintains audit trail and referential integrity

### Data Validation
- **Work Type Validation**: Ensures valid Worktype enum values
- **Input Sanitization**: Comprehensive input validation
- **Order Constraints**: Validates stage order constraints
- **Unique Constraints**: Prevents duplicate stage orders within pipeline

## Performance Considerations

### Frontend Optimizations
- **Server-Side Rendering**: Initial data fetching on server
- **Component Memoization**: React.memo for expensive components
- **Debounced Search**: Prevents excessive filtering operations
- **Lazy Loading**: Dynamic imports for heavy components

### Backend Optimizations
- **Database Indexing**: Proper indexes on foreign keys and search fields
- **Soft Deletion Queries**: Efficient filtering of deleted records
- **Transaction Batching**: Bulk operations wrapped in transactions
- **Query Optimization**: Selective field loading with Prisma includes

## Error Handling

### Frontend Error Handling
- **Toast Notifications**: User-friendly error messages via Sonner
- **Loading States**: Visual feedback during operations
- **Form Validation**: Client-side validation before submission
- **Graceful Degradation**: Fallback UI for failed operations

### Backend Error Handling
- **Structured Responses**: Consistent error response format
- **HTTP Status Codes**: Appropriate status codes for different scenarios
- **Input Validation**: Comprehensive request validation
- **Database Error Handling**: Proper error catching and logging

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with Jest/React Testing Library
- **Integration Tests**: Pipeline management workflow testing
- **E2E Tests**: Complete pipeline creation and management flows
- **Accessibility Tests**: Screen reader and keyboard navigation

### Backend Testing
- **Unit Tests**: Controller and service testing
- **Integration Tests**: Database operation testing
- **API Tests**: Endpoint testing with various scenarios
- **Performance Tests**: Load testing for pipeline operations

## Deployment & Monitoring

### Deployment Pipeline
- **Environment Variables**: API endpoints and configuration
- **Build Process**: Next.js optimization and bundling
- **Database Migrations**: Prisma migration management
- **Health Checks**: API endpoint monitoring

### Monitoring & Logging
- **Error Tracking**: Frontend and backend error logging
- **Performance Monitoring**: API response times and pipeline operations
- **User Analytics**: Pipeline creation and usage tracking
- **Database Monitoring**: Query performance and connection health

## Future Enhancements

### Planned Features
- **Pipeline Templates**: Pre-built pipeline templates for common workflows
- **Conditional Stages**: Dynamic stage progression based on conditions
- **Stage Automation**: Automated stage transitions and actions
- **Pipeline Analytics**: Detailed analytics on pipeline performance
- **Workflow Rules**: Custom rules for stage transitions
- **Integration APIs**: Webhook support for external system integration

### Technical Improvements
- **Real-time Updates**: WebSocket support for live pipeline updates
- **Advanced Filtering**: More sophisticated filtering and search options
- **Bulk Operations**: Multi-pipeline management capabilities
- **Performance Optimization**: Advanced caching and optimization
- **Mobile App**: Native mobile pipeline management application
