"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTicketStageChangeLogs = exports.viewTicketById = exports.getCurrentUserTickets = exports.viewTicket = exports.getWorkItem = exports.workTypeToTableMapping = void 0;
const helpers_1 = require("../../../utils/helpers");
exports.workTypeToTableMapping = {
    trackSheets: "track_sheets",
    invoiceFiles: "invoice_files",
};
const getWorkItem = async (table, id) => {
    try {
        const result = await prisma.$queryRawUnsafe(`SELECT * FROM ${table} WHERE id::text = $1`, String(id));
        return result?.[0] ?? null;
    }
    catch (error) {
        console.error(`getWorkItem error [${table}]`, error);
        return null;
    }
};
exports.getWorkItem = getWorkItem;
const viewTicket = async (req, res) => {
    try {
        const data = await prisma.ticket.findMany({
            where: {
                deletedAt: null,
                pipeline: {
                    deletedAt: null,
                },
            },
            include: {
                pipeline: { include: { stages: { orderBy: { order: "asc" } } } },
                stages: true,
                comments: {
                    where: { deletedAt: null },
                    orderBy: { createdAt: "desc" },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        const allAssignedTo = new Set();
        data.forEach((ticket) => {
            if (ticket.stages) {
                ticket.stages.forEach((stage) => {
                    if (stage.assignedTo)
                        allAssignedTo.add(stage.assignedTo);
                });
            }
        });
        const assignedToArr = Array.from(allAssignedTo);
        const users = assignedToArr.length > 0
            ? await prisma.user.findMany({
                where: {
                    OR: [
                        { username: { in: assignedToArr } },
                        {
                            id: {
                                in: assignedToArr
                                    .filter((v) => !isNaN(Number(v)))
                                    .map((v) => Number(v)),
                            },
                        },
                    ],
                },
                select: {
                    id: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                },
            })
            : [];
        const getUser = (assignedTo) => users.find((u) => u.username === assignedTo || String(u.id) === String(assignedTo));
        const enhancedData = await Promise.all(data.map(async (ticket) => {
            const table = exports.workTypeToTableMapping[ticket.pipeline.workType];
            const tags = ticket.tags.length > 0
                ? await prisma.tag.findMany({
                    where: {
                        id: {
                            in: ticket.tags,
                        },
                        deletedAt: null,
                    },
                })
                : [];
            const stages = ticket.stages
                ? ticket.stages.map((stage) => ({
                    ...stage,
                    assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
                }))
                : ticket.stages;
            const pipeline = ticket.pipeline
                ? {
                    ...ticket.pipeline,
                    stages: ticket.pipeline.stages,
                }
                : ticket.pipeline;
            const TicketTracksheet = table && ticket.workItemId
                ? await (0, exports.getWorkItem)(table, ticket.workItemId)
                : null;
            return {
                ...ticket,
                TicketTracksheet,
                tags,
                pipeline,
                stages,
            };
        }));
        return res.status(200).json({
            data: await Promise.all(enhancedData),
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewTicket = viewTicket;
const getCurrentUserTickets = async (req, res) => {
    try {
        const currentUserId = req.user_id || req.body.userId;
        const data = await prisma.ticket.findMany({
            where: {
                deletedAt: null,
                pipeline: {
                    deletedAt: null,
                },
            },
            include: {
                pipeline: { include: { stages: { orderBy: { order: "asc" } } } },
                stages: true,
                comments: {
                    where: { deletedAt: null },
                    orderBy: { createdAt: "desc" },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        const filteredData = data.filter((ticket) => {
            if (!ticket.currentStageId ||
                !ticket.stages ||
                ticket.stages.length === 0) {
                return false;
            }
            const currentStage = ticket.stages.find((stage) => stage.pipelineStageId === ticket.currentStageId);
            if (!currentStage) {
                return false;
            }
            if (String(currentStage.assignedTo) !== String(currentUserId)) {
                return false;
            }
            return true;
        });
        const allAssignedTo = new Set();
        filteredData.forEach((ticket) => {
            if (ticket.stages) {
                ticket.stages.forEach((stage) => {
                    if (stage.assignedTo)
                        allAssignedTo.add(stage.assignedTo);
                });
            }
        });
        const assignedToArr = Array.from(allAssignedTo);
        const users = assignedToArr.length > 0
            ? await prisma.user.findMany({
                where: {
                    OR: [
                        { username: { in: assignedToArr } },
                        {
                            id: {
                                in: assignedToArr
                                    .filter((v) => !isNaN(Number(v)))
                                    .map((v) => Number(v)),
                            },
                        },
                    ],
                },
                select: {
                    id: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                },
            })
            : [];
        const getUser = (assignedTo) => users.find((u) => u.username === assignedTo || String(u.id) === String(assignedTo));
        const enhancedData = await Promise.all(filteredData.map(async (ticket) => {
            const table = exports.workTypeToTableMapping[ticket.pipeline.workType];
            const tags = ticket.tags.length > 0
                ? await prisma.tag.findMany({
                    where: {
                        id: {
                            in: ticket.tags,
                        },
                        deletedAt: null,
                    },
                })
                : [];
            const stages = ticket.stages
                ? ticket.stages.map((stage) => ({
                    ...stage,
                    assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
                }))
                : ticket.stages;
            const pipeline = ticket.pipeline
                ? {
                    ...ticket.pipeline,
                    stages: ticket.pipeline.stages,
                }
                : ticket.pipeline;
            const TicketTracksheet = table && ticket.workItemId
                ? await (0, exports.getWorkItem)(table, ticket.workItemId)
                : null;
            return {
                ...ticket,
                TicketTracksheet,
                tags,
                pipeline,
                stages,
            };
        }));
        return res.status(200).json({
            data: await Promise.all(enhancedData),
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getCurrentUserTickets = getCurrentUserTickets;
const viewTicketById = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ error: "Ticket id is required" });
        }
        const data = await prisma.ticket.findUnique({
            where: { id: id },
            include: {
                pipeline: { include: { stages: true } },
                stages: true,
                comments: {
                    where: { deletedAt: null },
                    orderBy: { createdAt: "desc" },
                },
            },
        });
        if (!data) {
            return res.status(404).json({ error: "Ticket not found" });
        }
        const allAssignedTo = new Set();
        if (data.stages) {
            data.stages.forEach((stage) => {
                if (stage.assignedTo)
                    allAssignedTo.add(stage.assignedTo);
            });
        }
        const assignedToArr = Array.from(allAssignedTo);
        const users = assignedToArr.length > 0
            ? await prisma.user.findMany({
                where: {
                    OR: [
                        { username: { in: assignedToArr } },
                        {
                            id: {
                                in: assignedToArr
                                    .filter((v) => !isNaN(Number(v)))
                                    .map((v) => Number(v)),
                            },
                        },
                    ],
                },
                select: {
                    id: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                },
            })
            : [];
        const getUser = (assignedTo) => users.find((u) => u.username === assignedTo || String(u.id) === String(assignedTo));
        const tags = data.tags.length > 0
            ? await prisma.tag.findMany({
                where: {
                    id: {
                        in: data.tags,
                    },
                    deletedAt: null,
                },
            })
            : [];
        const stages = data.stages
            ? data.stages.map((stage) => ({
                ...stage,
                assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
            }))
            : data.stages;
        const pipeline = data.pipeline
            ? {
                ...data.pipeline,
                stages: data.pipeline.stages,
            }
            : data.pipeline;
        const table = exports.workTypeToTableMapping[data.pipeline.workType];
        let enhancedData = data;
        if (table && data.workItemId) {
            const workItem = table && data.workItemId
                ? await (0, exports.getWorkItem)(table, data.workItemId)
                : null;
            enhancedData = {
                ...data,
                workItem,
                tags,
                pipeline,
                stages,
            };
        }
        else {
            enhancedData = {
                ...data,
                tags: tags,
                pipeline,
                stages,
            };
        }
        return res.status(200).json({
            data: enhancedData,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewTicketById = viewTicketById;
const getTicketStageChangeLogs = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ error: "Ticket id is required" });
        }
        const logs = await prisma.ticketStageChangeLog.findMany({
            where: {
                ticketId: id,
                deletedAt: null,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        const enhancedLogs = await Promise.all(logs.map(async (log) => {
            let fromStageName = "Unknown Stage";
            let toStageName = "Unknown Stage";
            if (log.fromStage) {
                const fromPipelineStage = await prisma.pipelineStage.findUnique({
                    where: { id: log.fromStage },
                    select: { name: true },
                });
                fromStageName = fromPipelineStage?.name || "Unknown Stage";
            }
            if (log.toStage) {
                const toPipelineStage = await prisma.pipelineStage.findUnique({
                    where: { id: log.toStage },
                    select: { name: true },
                });
                toStageName = toPipelineStage?.name || "Unknown Stage";
            }
            return {
                id: log.id,
                ticketId: log.ticketId,
                fromStage: log.fromStage,
                toStage: log.toStage,
                fromStageName,
                toStageName,
                createdBy: log.createdBy,
                createdAt: log.createdAt,
                updatedAt: log.updatedAt,
                updatedBy: log.updatedBy,
            };
        }));
        return res.status(200).json({
            success: true,
            data: enhancedLogs,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getTicketStageChangeLogs = getTicketStageChangeLogs;
//# sourceMappingURL=view.js.map