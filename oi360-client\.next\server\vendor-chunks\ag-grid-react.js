"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ag-grid-react";
exports.ids = ["vendor-chunks/ag-grid-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/ag-grid-react/dist/package/index.esm.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/ag-grid-react/dist/package/index.esm.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgGridReact: () => (/* binding */ AgGridReact),\n/* harmony export */   CustomComponentContext: () => (/* binding */ CustomContext),\n/* harmony export */   getInstance: () => (/* binding */ getInstance),\n/* harmony export */   useGridCellEditor: () => (/* binding */ useGridCellEditor),\n/* harmony export */   useGridDate: () => (/* binding */ useGridDate),\n/* harmony export */   useGridFilter: () => (/* binding */ useGridFilter),\n/* harmony export */   useGridFilterDisplay: () => (/* binding */ useGridFilterDisplay),\n/* harmony export */   useGridFloatingFilter: () => (/* binding */ useGridFloatingFilter),\n/* harmony export */   useGridMenuItem: () => (/* binding */ useGridMenuItem),\n/* harmony export */   warnReactiveCustomComponents: () => (/* binding */ warnReactiveCustomComponents)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var ag_grid_community__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ag-grid-community */ \"(ssr)/./node_modules/ag-grid-community/dist/package/main.esm.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n// packages/ag-grid-react/src/agGridReact.tsx\n\n\n// packages/ag-grid-react/src/reactUi/agGridReactUi.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/cellRenderer/groupCellRenderer.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/beansContext.tsx\n\nvar BeansContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext({});\nvar RenderModeContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(\"default\");\n\n// packages/ag-grid-react/src/reactUi/jsComp.tsx\nvar showJsComp = (compDetails, context, eParent, ref) => {\n  const doNothing = !compDetails || compDetails.componentFromFramework || context.isDestroyed();\n  if (doNothing) {\n    return;\n  }\n  const promise = compDetails.newAgStackInstance();\n  let comp;\n  let compGui;\n  let destroyed = false;\n  promise.then((c) => {\n    if (destroyed) {\n      context.destroyBean(c);\n      return;\n    }\n    comp = c;\n    compGui = comp.getGui();\n    eParent.appendChild(compGui);\n    setRef(ref, comp);\n  });\n  return () => {\n    destroyed = true;\n    if (!comp) {\n      return;\n    }\n    compGui?.parentElement?.removeChild(compGui);\n    context.destroyBean(comp);\n    if (ref) {\n      setRef(ref, void 0);\n    }\n  };\n};\nvar setRef = (ref, value) => {\n  if (!ref) {\n    return;\n  }\n  if (ref instanceof Function) {\n    const refCallback = ref;\n    refCallback(value);\n  } else {\n    const refObj = ref;\n    refObj.current = value;\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/utils.tsx\n\n\nvar classesList = (...list) => {\n  const filtered = list.filter((s) => s != null && s !== \"\");\n  return filtered.join(\" \");\n};\nvar CssClasses = class _CssClasses {\n  constructor(...initialClasses) {\n    this.classesMap = {};\n    initialClasses.forEach((className) => {\n      this.classesMap[className] = true;\n    });\n  }\n  setClass(className, on) {\n    const nothingHasChanged = !!this.classesMap[className] == on;\n    if (nothingHasChanged) {\n      return this;\n    }\n    const res = new _CssClasses();\n    res.classesMap = { ...this.classesMap };\n    res.classesMap[className] = on;\n    return res;\n  }\n  toString() {\n    const res = Object.keys(this.classesMap).filter((key) => this.classesMap[key]).join(\" \");\n    return res;\n  }\n};\nvar isComponentStateless = (Component2) => {\n  const hasSymbol = () => typeof Symbol === \"function\" && Symbol.for;\n  const getMemoType = () => hasSymbol() ? Symbol.for(\"react.memo\") : 60115;\n  return typeof Component2 === \"function\" && !(Component2.prototype && Component2.prototype.isReactComponent) || typeof Component2 === \"object\" && Component2.$$typeof === getMemoType();\n};\nvar reactVersion = react__WEBPACK_IMPORTED_MODULE_0__.version?.split(\".\")[0];\nvar isReactVersion17Minus = reactVersion === \"16\" || reactVersion === \"17\";\nfunction isReact19() {\n  return reactVersion === \"19\";\n}\nvar disableFlushSync = false;\nfunction runWithoutFlushSync(func) {\n  if (!disableFlushSync) {\n    setTimeout(() => disableFlushSync = false, 0);\n  }\n  disableFlushSync = true;\n  return func();\n}\nvar agFlushSync = (useFlushSync, fn) => {\n  if (!isReactVersion17Minus && useFlushSync && !disableFlushSync) {\n    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(fn);\n  } else {\n    fn();\n  }\n};\nvar agStartTransition = (fn) => {\n  if (!isReactVersion17Minus) {\n    react__WEBPACK_IMPORTED_MODULE_0__.startTransition(fn);\n  } else {\n    fn();\n  }\n};\nfunction agUseSyncExternalStore(subscribe, getSnapshot, defaultSnapshot) {\n  if (react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(subscribe, getSnapshot);\n  } else {\n    return defaultSnapshot;\n  }\n}\nfunction getNextValueIfDifferent(prev, next, maintainOrder) {\n  if (next == null || prev == null) {\n    return next;\n  }\n  if (prev === next || next.length === 0 && prev.length === 0) {\n    return prev;\n  }\n  if (maintainOrder || prev.length === 0 && next.length > 0 || prev.length > 0 && next.length === 0) {\n    return next;\n  }\n  const oldValues = [];\n  const newValues = [];\n  const prevMap = /* @__PURE__ */ new Map();\n  const nextMap = /* @__PURE__ */ new Map();\n  for (let i = 0; i < next.length; i++) {\n    const c = next[i];\n    nextMap.set(c.instanceId, c);\n  }\n  for (let i = 0; i < prev.length; i++) {\n    const c = prev[i];\n    prevMap.set(c.instanceId, c);\n    if (nextMap.has(c.instanceId)) {\n      oldValues.push(c);\n    }\n  }\n  for (let i = 0; i < next.length; i++) {\n    const c = next[i];\n    const instanceId = c.instanceId;\n    if (!prevMap.has(instanceId)) {\n      newValues.push(c);\n    }\n  }\n  if (oldValues.length === prev.length && newValues.length === 0) {\n    return prev;\n  }\n  if (oldValues.length === 0 && newValues.length === next.length) {\n    return next;\n  }\n  if (oldValues.length === 0) {\n    return newValues;\n  }\n  if (newValues.length === 0) {\n    return oldValues;\n  }\n  return [...oldValues, ...newValues];\n}\n\n// packages/ag-grid-react/src/reactUi/cellRenderer/groupCellRenderer.tsx\nvar GroupCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const { registry, context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eCheckboxRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eExpandedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eContractedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const ctrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [innerCompDetails, setInnerCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [childCount, setChildCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [expandedCssClasses, setExpandedCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-hidden\"));\n  const [contractedCssClasses, setContractedCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-hidden\"));\n  const [checkboxCssClasses, setCheckboxCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-invisible\"));\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => {\n    return {\n      // force new instance when grid tries to refresh\n      refresh() {\n        return false;\n      }\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    return showJsComp(innerCompDetails, context, eValueRef.current);\n  }, [innerCompDetails]);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    if (!eRef) {\n      ctrlRef.current = context.destroyBean(ctrlRef.current);\n      return;\n    }\n    const compProxy = {\n      setInnerRenderer: (details, valueToDisplay) => {\n        setInnerCompDetails(details);\n        setValue(valueToDisplay);\n      },\n      setChildCount: (count) => setChildCount(count),\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setContractedDisplayed: (displayed) => setContractedCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed)),\n      setExpandedDisplayed: (displayed) => setExpandedCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed)),\n      setCheckboxVisible: (visible) => setCheckboxCssClasses((prev) => prev.setClass(\"ag-invisible\", !visible)),\n      setCheckboxSpacing: (add) => setCheckboxCssClasses((prev) => prev.setClass(\"ag-group-checkbox-spacing\", add))\n    };\n    const groupCellRendererCtrl = registry.createDynamicBean(\"groupCellRendererCtrl\", true);\n    if (groupCellRendererCtrl) {\n      ctrlRef.current = context.createBean(groupCellRendererCtrl);\n      ctrlRef.current.init(\n        compProxy,\n        eRef,\n        eCheckboxRef.current,\n        eExpandedRef.current,\n        eContractedRef.current,\n        GroupCellRenderer,\n        props\n      );\n    }\n  }, []);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-cell-wrapper ${cssClasses.toString()}`, [cssClasses]);\n  const expandedClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-group-expanded ${expandedCssClasses.toString()}`, [expandedCssClasses]);\n  const contractedClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => `ag-group-contracted ${contractedCssClasses.toString()}`,\n    [contractedCssClasses]\n  );\n  const checkboxClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-group-checkbox ${checkboxCssClasses.toString()}`, [checkboxCssClasses]);\n  const useFwRenderer = innerCompDetails?.componentFromFramework;\n  const FwRenderer = useFwRenderer ? innerCompDetails.componentClass : void 0;\n  const useValue = innerCompDetails == null && value != null;\n  const escapedValue = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._toString)(value);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"span\",\n    {\n      className,\n      ref: setRef2,\n      ...!props.colDef ? { role: ctrlRef.current?.getCellAriaRole() } : {}\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: expandedClassName, ref: eExpandedRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: contractedClassName, ref: eContractedRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: checkboxClassName, ref: eCheckboxRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: \"ag-group-value\", ref: eValueRef }, useValue ? escapedValue : useFwRenderer ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FwRenderer, { ...innerCompDetails.params }) : null),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: \"ag-group-child-count\" }, childCount)\n  );\n});\nvar groupCellRenderer_default = GroupCellRenderer;\n\n// packages/ag-grid-react/src/shared/customComp/customComponentWrapper.ts\n\n\n// packages/ag-grid-react/src/reactUi/customComp/customWrapperComp.tsx\n\n\n// packages/ag-grid-react/src/shared/customComp/customContext.ts\n\nvar CustomContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n  setMethods: () => {\n  }\n});\n\n// packages/ag-grid-react/src/reactUi/customComp/customWrapperComp.tsx\nvar CustomWrapperComp = (params) => {\n  const { initialProps, addUpdateCallback, CustomComponentClass, setMethods } = params;\n  const [{ key, ...props }, setProps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialProps);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    addUpdateCallback((newProps) => setProps(newProps));\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomContext.Provider, { value: { setMethods } }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomComponentClass, { key, ...props }));\n};\nvar customWrapperComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(CustomWrapperComp);\n\n// packages/ag-grid-react/src/shared/reactComponent.ts\n\n\n\n\n// packages/ag-grid-react/src/shared/keyGenerator.ts\nvar counter = 0;\nfunction generateNewKey() {\n  return `agPortalKey_${++counter}`;\n}\n\n// packages/ag-grid-react/src/shared/reactComponent.ts\nvar ReactComponent = class {\n  constructor(reactComponent, portalManager, componentType, suppressFallbackMethods) {\n    this.portal = null;\n    this.oldPortal = null;\n    this.reactComponent = reactComponent;\n    this.portalManager = portalManager;\n    this.componentType = componentType;\n    this.suppressFallbackMethods = !!suppressFallbackMethods;\n    this.statelessComponent = this.isStateless(this.reactComponent);\n    this.key = generateNewKey();\n    this.portalKey = generateNewKey();\n    this.instanceCreated = this.isStatelessComponent() ? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve(false) : new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveInstanceCreated = resolve;\n    });\n  }\n  getGui() {\n    return this.eParentElement;\n  }\n  /** `getGui()` returns the parent element. This returns the actual root element. */\n  getRootElement() {\n    const firstChild = this.eParentElement.firstChild;\n    return firstChild;\n  }\n  destroy() {\n    if (this.componentInstance && typeof this.componentInstance.destroy == \"function\") {\n      this.componentInstance.destroy();\n    }\n    const portal = this.portal;\n    if (portal) {\n      this.portalManager.destroyPortal(portal);\n    }\n  }\n  createParentElement(params) {\n    const componentWrappingElement = this.portalManager.getComponentWrappingElement();\n    const eParentElement = document.createElement(componentWrappingElement || \"div\");\n    eParentElement.classList.add(\"ag-react-container\");\n    params.reactContainer = eParentElement;\n    return eParentElement;\n  }\n  statelessComponentRendered() {\n    return this.eParentElement.childElementCount > 0 || this.eParentElement.childNodes.length > 0;\n  }\n  getFrameworkComponentInstance() {\n    return this.componentInstance;\n  }\n  isStatelessComponent() {\n    return this.statelessComponent;\n  }\n  getReactComponentName() {\n    return this.reactComponent.name;\n  }\n  getMemoType() {\n    return this.hasSymbol() ? Symbol.for(\"react.memo\") : 60115;\n  }\n  hasSymbol() {\n    return typeof Symbol === \"function\" && Symbol.for;\n  }\n  isStateless(Component2) {\n    return typeof Component2 === \"function\" && !(Component2.prototype && Component2.prototype.isReactComponent) || typeof Component2 === \"object\" && Component2.$$typeof === this.getMemoType();\n  }\n  hasMethod(name) {\n    const frameworkComponentInstance = this.getFrameworkComponentInstance();\n    return !!frameworkComponentInstance && frameworkComponentInstance[name] != null || this.fallbackMethodAvailable(name);\n  }\n  callMethod(name, args) {\n    const frameworkComponentInstance = this.getFrameworkComponentInstance();\n    if (this.isStatelessComponent()) {\n      return this.fallbackMethod(name, !!args && args[0] ? args[0] : {});\n    } else if (!frameworkComponentInstance) {\n      setTimeout(() => this.callMethod(name, args));\n      return;\n    }\n    const method = frameworkComponentInstance[name];\n    if (method) {\n      return method.apply(frameworkComponentInstance, args);\n    }\n    if (this.fallbackMethodAvailable(name)) {\n      return this.fallbackMethod(name, !!args && args[0] ? args[0] : {});\n    }\n  }\n  addMethod(name, callback) {\n    this[name] = callback;\n  }\n  init(params) {\n    this.eParentElement = this.createParentElement(params);\n    this.createOrUpdatePortal(params);\n    return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => this.createReactComponent(resolve));\n  }\n  createOrUpdatePortal(params) {\n    if (!this.isStatelessComponent()) {\n      this.ref = (element) => {\n        this.componentInstance = element;\n        this.resolveInstanceCreated?.(true);\n        this.resolveInstanceCreated = void 0;\n      };\n      params.ref = this.ref;\n    }\n    this.reactElement = this.createElement(this.reactComponent, { ...params, key: this.key });\n    this.portal = (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(\n      this.reactElement,\n      this.eParentElement,\n      this.portalKey\n      // fixed deltaRowModeRefreshCompRenderer\n    );\n  }\n  createElement(reactComponent, props) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(reactComponent, props);\n  }\n  createReactComponent(resolve) {\n    this.portalManager.mountReactPortal(this.portal, this, resolve);\n  }\n  rendered() {\n    return this.isStatelessComponent() && this.statelessComponentRendered() || !!(!this.isStatelessComponent() && this.getFrameworkComponentInstance());\n  }\n  /*\n   * fallback methods - these will be invoked if a corresponding instance method is not present\n   * for example if refresh is called and is not available on the component instance, then refreshComponent on this\n   * class will be invoked instead\n   *\n   * Currently only refresh is supported\n   */\n  refreshComponent(args) {\n    this.oldPortal = this.portal;\n    this.createOrUpdatePortal(args);\n    this.portalManager.updateReactPortal(this.oldPortal, this.portal);\n  }\n  fallbackMethod(name, params) {\n    const method = this[`${name}Component`];\n    if (!this.suppressFallbackMethods && !!method) {\n      return method.bind(this)(params);\n    }\n  }\n  fallbackMethodAvailable(name) {\n    if (this.suppressFallbackMethods) {\n      return false;\n    }\n    const method = this[`${name}Component`];\n    return !!method;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/customComponentWrapper.ts\nfunction addOptionalMethods(optionalMethodNames, providedMethods, component) {\n  optionalMethodNames.forEach((methodName) => {\n    const providedMethod = providedMethods[methodName];\n    if (providedMethod) {\n      component[methodName] = providedMethod;\n    }\n  });\n}\nvar CustomComponentWrapper = class extends ReactComponent {\n  constructor() {\n    super(...arguments);\n    this.awaitUpdateCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveUpdateCallback = resolve;\n    });\n    this.wrapperComponent = customWrapperComp_default;\n  }\n  init(params) {\n    this.sourceParams = params;\n    return super.init(this.getProps());\n  }\n  addMethod() {\n  }\n  getInstance() {\n    return this.instanceCreated.then(() => this.componentInstance);\n  }\n  getFrameworkComponentInstance() {\n    return this;\n  }\n  createElement(reactComponent, props) {\n    return super.createElement(this.wrapperComponent, {\n      initialProps: props,\n      CustomComponentClass: reactComponent,\n      setMethods: (methods) => this.setMethods(methods),\n      addUpdateCallback: (callback) => {\n        this.updateCallback = () => {\n          callback(this.getProps());\n          return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n            setTimeout(() => {\n              resolve();\n            });\n          });\n        };\n        this.resolveUpdateCallback();\n      }\n    });\n  }\n  setMethods(methods) {\n    this.providedMethods = methods;\n    addOptionalMethods(this.getOptionalMethods(), this.providedMethods, this);\n  }\n  getOptionalMethods() {\n    return [];\n  }\n  getProps() {\n    return {\n      ...this.sourceParams,\n      key: this.key,\n      ref: this.ref\n    };\n  }\n  refreshProps() {\n    if (this.updateCallback) {\n      return this.updateCallback();\n    }\n    return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise(\n      (resolve) => this.awaitUpdateCallback.then(() => {\n        this.updateCallback().then(() => resolve());\n      })\n    );\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/cellRendererComponentWrapper.ts\nvar CellRendererComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/dateComponentWrapper.ts\nvar DateComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.date = null;\n    this.onDateChange = (date) => this.updateDate(date);\n  }\n  getDate() {\n    return this.date;\n  }\n  setDate(date) {\n    this.date = date;\n    this.refreshProps();\n  }\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\", \"setInputPlaceholder\", \"setInputAriaLabel\", \"setDisabled\"];\n  }\n  updateDate(date) {\n    this.setDate(date);\n    this.sourceParams.onDateChanged();\n  }\n  getProps() {\n    const props = super.getProps();\n    props.date = this.date;\n    props.onDateChange = this.onDateChange;\n    delete props.onDateChanged;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/dragAndDropImageComponentWrapper.ts\nvar DragAndDropImageComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.label = \"\";\n    this.icon = null;\n    this.shake = false;\n  }\n  setIcon(iconName, shake) {\n    this.icon = iconName;\n    this.shake = shake;\n    this.refreshProps();\n  }\n  setLabel(label) {\n    this.label = label;\n    this.refreshProps();\n  }\n  getProps() {\n    const props = super.getProps();\n    const { label, icon, shake } = this;\n    props.label = label;\n    props.icon = icon;\n    props.shake = shake;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/filterComponentWrapper.ts\n\nvar FilterComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n    this.onUiChange = () => this.sourceParams.filterModifiedCallback();\n    this.expectingNewMethods = true;\n    this.hasBeenActive = false;\n    this.awaitSetMethodsCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveSetMethodsCallback = resolve;\n    });\n  }\n  isFilterActive() {\n    return this.model != null;\n  }\n  doesFilterPass(params) {\n    return this.providedMethods.doesFilterPass(params);\n  }\n  getModel() {\n    return this.model;\n  }\n  setModel(model) {\n    this.expectingNewMethods = true;\n    this.model = model;\n    this.hasBeenActive || (this.hasBeenActive = this.isFilterActive());\n    return this.refreshProps();\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n    return true;\n  }\n  afterGuiAttached(params) {\n    const providedMethods = this.providedMethods;\n    if (!providedMethods) {\n      this.awaitSetMethodsCallback.then(() => this.providedMethods?.afterGuiAttached?.(params));\n    } else {\n      providedMethods.afterGuiAttached?.(params);\n    }\n  }\n  getOptionalMethods() {\n    return [\"afterGuiDetached\", \"onNewRowsLoaded\", \"getModelAsString\", \"onAnyFilterChanged\"];\n  }\n  setMethods(methods) {\n    if (this.expectingNewMethods === false && this.hasBeenActive && this.providedMethods?.doesFilterPass !== methods?.doesFilterPass) {\n      setTimeout(() => {\n        this.sourceParams.filterChangedCallback();\n      });\n    }\n    this.expectingNewMethods = false;\n    super.setMethods(methods);\n    this.resolveSetMethodsCallback();\n    this.resolveFilterPassCallback?.();\n    this.resolveFilterPassCallback = void 0;\n  }\n  updateModel(model) {\n    this.resolveFilterPassCallback?.();\n    const awaitFilterPassCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveFilterPassCallback = resolve;\n    });\n    this.setModel(model).then(() => {\n      awaitFilterPassCallback.then(() => {\n        this.sourceParams.filterChangedCallback();\n      });\n    });\n  }\n  getProps() {\n    const props = super.getProps();\n    props.model = this.model;\n    props.onModelChange = this.onModelChange;\n    props.onUiChange = this.onUiChange;\n    delete props.filterChangedCallback;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/filterDisplayComponentWrapper.ts\n\nvar FilterDisplayComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.awaitSetMethodsCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveSetMethodsCallback = resolve;\n    });\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n    return true;\n  }\n  afterGuiAttached(params) {\n    const providedMethods = this.providedMethods;\n    if (!providedMethods) {\n      this.awaitSetMethodsCallback.then(() => this.providedMethods?.afterGuiAttached?.(params));\n    } else {\n      providedMethods.afterGuiAttached?.(params);\n    }\n  }\n  getOptionalMethods() {\n    return [\"afterGuiDetached\", \"onNewRowsLoaded\", \"onAnyFilterChanged\"];\n  }\n  setMethods(methods) {\n    super.setMethods(methods);\n    this.resolveSetMethodsCallback();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterComponentProxy.ts\n\nfunction updateFloatingFilterParent(params, model) {\n  params.parentFilterInstance((instance) => {\n    (instance.setModel(model) || ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve()).then(() => {\n      params.filterParams.filterChangedCallback();\n    });\n  });\n}\nvar FloatingFilterComponentProxy = class {\n  constructor(floatingFilterParams, refreshProps) {\n    this.floatingFilterParams = floatingFilterParams;\n    this.refreshProps = refreshProps;\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n  }\n  getProps() {\n    return {\n      ...this.floatingFilterParams,\n      model: this.model,\n      onModelChange: this.onModelChange\n    };\n  }\n  onParentModelChanged(parentModel) {\n    this.model = parentModel;\n    this.refreshProps();\n  }\n  refresh(params) {\n    this.floatingFilterParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n  updateModel(model) {\n    this.model = model;\n    this.refreshProps();\n    updateFloatingFilterParent(this.floatingFilterParams, model);\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterComponentWrapper.ts\nvar FloatingFilterComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n  }\n  onParentModelChanged(parentModel) {\n    this.model = parentModel;\n    this.refreshProps();\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n  updateModel(model) {\n    this.model = model;\n    this.refreshProps();\n    updateFloatingFilterParent(this.sourceParams, model);\n  }\n  getProps() {\n    const props = super.getProps();\n    props.model = this.model;\n    props.onModelChange = this.onModelChange;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterDisplayComponentWrapper.ts\nvar FloatingFilterDisplayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/innerHeaderComponentWrapper.ts\nvar InnerHeaderComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/loadingOverlayComponentWrapper.ts\nvar LoadingOverlayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/menuItemComponentWrapper.ts\nvar MenuItemComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.active = false;\n    this.expanded = false;\n    this.onActiveChange = (active) => this.updateActive(active);\n  }\n  setActive(active) {\n    this.awaitSetActive(active);\n  }\n  setExpanded(expanded) {\n    this.expanded = expanded;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"select\", \"configureDefaults\"];\n  }\n  awaitSetActive(active) {\n    this.active = active;\n    return this.refreshProps();\n  }\n  updateActive(active) {\n    const result = this.awaitSetActive(active);\n    if (active) {\n      result.then(() => this.sourceParams.onItemActivated());\n    }\n  }\n  getProps() {\n    const props = super.getProps();\n    props.active = this.active;\n    props.expanded = this.expanded;\n    props.onActiveChange = this.onActiveChange;\n    delete props.onItemActivated;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/noRowsOverlayComponentWrapper.ts\nvar NoRowsOverlayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/statusPanelComponentWrapper.ts\nvar StatusPanelComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/toolPanelComponentWrapper.ts\nvar ToolPanelComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.onStateChange = (state) => this.updateState(state);\n  }\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n  getState() {\n    return this.state;\n  }\n  updateState(state) {\n    this.state = state;\n    this.refreshProps();\n    this.sourceParams.onStateUpdated();\n  }\n  getProps() {\n    const props = super.getProps();\n    props.state = this.state;\n    props.onStateChange = this.onStateChange;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/util.ts\n\nfunction getInstance(wrapperComponent, callback) {\n  const promise = wrapperComponent?.getInstance?.() ?? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve(void 0);\n  promise.then((comp) => callback(comp));\n}\nfunction warnReactiveCustomComponents() {\n  (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._warn)(231);\n}\n\n// packages/ag-grid-react/src/shared/portalManager.ts\nvar MAX_COMPONENT_CREATION_TIME_IN_MS = 1e3;\nvar PortalManager = class {\n  constructor(refresher, wrappingElement, maxComponentCreationTimeMs) {\n    this.destroyed = false;\n    this.portals = [];\n    this.hasPendingPortalUpdate = false;\n    this.wrappingElement = wrappingElement ? wrappingElement : \"div\";\n    this.refresher = refresher;\n    this.maxComponentCreationTimeMs = maxComponentCreationTimeMs ? maxComponentCreationTimeMs : MAX_COMPONENT_CREATION_TIME_IN_MS;\n  }\n  getPortals() {\n    return this.portals;\n  }\n  destroy() {\n    this.destroyed = true;\n  }\n  destroyPortal(portal) {\n    this.portals = this.portals.filter((curPortal) => curPortal !== portal);\n    this.batchUpdate();\n  }\n  getComponentWrappingElement() {\n    return this.wrappingElement;\n  }\n  mountReactPortal(portal, reactComponent, resolve) {\n    this.portals = [...this.portals, portal];\n    this.waitForInstance(reactComponent, resolve);\n    this.batchUpdate();\n  }\n  updateReactPortal(oldPortal, newPortal) {\n    this.portals[this.portals.indexOf(oldPortal)] = newPortal;\n    this.batchUpdate();\n  }\n  batchUpdate() {\n    if (this.hasPendingPortalUpdate) {\n      return;\n    }\n    setTimeout(() => {\n      if (!this.destroyed) {\n        this.refresher();\n        this.hasPendingPortalUpdate = false;\n      }\n    });\n    this.hasPendingPortalUpdate = true;\n  }\n  waitForInstance(reactComponent, resolve, startTime = Date.now()) {\n    if (this.destroyed) {\n      resolve(null);\n      return;\n    }\n    if (reactComponent.rendered()) {\n      resolve(reactComponent);\n    } else {\n      if (Date.now() - startTime >= this.maxComponentCreationTimeMs && !this.hasPendingPortalUpdate) {\n        agFlushSync(true, () => this.refresher());\n        if (reactComponent.rendered()) {\n          resolve(reactComponent);\n        }\n        return;\n      }\n      window.setTimeout(() => {\n        this.waitForInstance(reactComponent, resolve, startTime);\n      });\n    }\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/gridComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/gridBodyComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/gridHeaderComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerRowContainerComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerRowComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerCellComp.tsx\n\n\nvar HeaderCellComp = ({ ctrl }) => {\n  const isAlive = ctrl.isAlive();\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const colId = isAlive ? ctrl.column.getColId() : void 0;\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eHeaderCompWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cssManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (isAlive && !cssManager.current) {\n    cssManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !ctrl.isAlive()) {\n      return;\n    }\n    const refreshSelectAllGui = () => {\n      const selectAllGui = ctrl.getSelectAllGui();\n      if (selectAllGui) {\n        eResize.current?.insertAdjacentElement(\"afterend\", selectAllGui);\n        compBean.current.addDestroyFunc(() => selectAllGui.remove());\n      }\n    };\n    const compProxy = {\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      toggleCss: (name, on) => cssManager.current.toggleCss(name, on),\n      setUserStyles: (styles) => setUserStyles(styles),\n      setAriaSort: (sort) => {\n        if (eGui.current) {\n          sort ? (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaSort)(eGui.current, sort) : (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._removeAriaSort)(eGui.current);\n        }\n      },\n      setUserCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      getUserCompInstance: () => userCompRef.current || void 0,\n      refreshSelectAllGui,\n      removeSelectAllGui: () => ctrl.getSelectAllGui()?.remove()\n    };\n    ctrl.setComp(compProxy, eRef, eResize.current, eHeaderCompWrapper.current, compBean.current);\n    refreshSelectAllGui();\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(userCompDetails, context, eHeaderCompWrapper.current, userCompRef),\n    [userCompDetails]\n  );\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ctrl.setDragSource(eGui.current);\n  }, [userCompDetails]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails?.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const reactUserComp = userCompDetails?.componentFromFramework;\n  const UserCompClass = userCompDetails?.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, style: userStyles, className: \"ag-header-cell\", \"col-id\": colId, role: \"columnheader\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eResize, className: \"ag-header-cell-resize\", role: \"presentation\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eHeaderCompWrapper, className: \"ag-header-cell-comp-wrapper\", role: \"presentation\" }, reactUserComp ? userCompStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompRef }) : null));\n};\nvar headerCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerFilterCellComp.tsx\n\n\n\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterDisplayComponentProxy.ts\nvar FloatingFilterDisplayComponentProxy = class {\n  constructor(floatingFilterParams, refreshProps) {\n    this.floatingFilterParams = floatingFilterParams;\n    this.refreshProps = refreshProps;\n  }\n  getProps() {\n    return this.floatingFilterParams;\n  }\n  refresh(params) {\n    this.floatingFilterParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/header/headerFilterCellComp.tsx\nvar HeaderFilterCellComp = ({ ctrl }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => new CssClasses(\"ag-header-cell\", \"ag-floating-filter\")\n  );\n  const [cssBodyClasses, setBodyCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [cssButtonWrapperClasses, setButtonWrapperCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => new CssClasses(\"ag-floating-filter-button\", \"ag-hidden\")\n  );\n  const [buttonWrapperAriaHidden, setButtonWrapperAriaHidden] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"false\");\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [, setRenderKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eFloatingFilterBody = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eButtonWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eButtonShowMainFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompResolve = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const userCompPromise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const userCompRef = (value) => {\n    if (value == null) {\n      return;\n    }\n    userCompResolve.current && userCompResolve.current(value);\n  };\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !ctrl.isAlive()) {\n      return;\n    }\n    userCompPromise.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      userCompResolve.current = resolve;\n    });\n    const compProxy = {\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setUserStyles: (styles) => setUserStyles(styles),\n      addOrRemoveBodyCssClass: (name, on) => setBodyCssClasses((prev) => prev.setClass(name, on)),\n      setButtonWrapperDisplayed: (displayed) => {\n        setButtonWrapperCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed));\n        setButtonWrapperAriaHidden(!displayed ? \"true\" : \"false\");\n      },\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      setCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      getFloatingFilterComp: () => userCompPromise.current ? userCompPromise.current : null,\n      setMenuIcon: (eIcon) => eButtonShowMainFilter.current?.appendChild(eIcon)\n    };\n    ctrl.setComp(compProxy, eRef, eButtonShowMainFilter.current, eFloatingFilterBody.current, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(userCompDetails, context, eFloatingFilterBody.current, userCompRef),\n    [userCompDetails]\n  );\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssClasses.toString(), [cssClasses]);\n  const bodyClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssBodyClasses.toString(), [cssBodyClasses]);\n  const buttonWrapperClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssButtonWrapperClasses.toString(), [cssButtonWrapperClasses]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails && userCompDetails.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const reactiveCustomComponents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gos.get(\"reactiveCustomComponents\"), []);\n  const enableFilterHandlers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gos.get(\"enableFilterHandlers\"), []);\n  const [floatingFilterCompProxy, setFloatingFilterCompProxy] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (userCompDetails?.componentFromFramework) {\n      if (reactiveCustomComponents) {\n        const ProxyClass = enableFilterHandlers ? FloatingFilterDisplayComponentProxy : FloatingFilterComponentProxy;\n        const compProxy = new ProxyClass(userCompDetails.params, () => setRenderKey((prev) => prev + 1));\n        userCompRef(compProxy);\n        setFloatingFilterCompProxy(compProxy);\n      } else {\n        warnReactiveCustomComponents();\n      }\n    }\n  }, [userCompDetails]);\n  const floatingFilterProps = floatingFilterCompProxy?.getProps();\n  const reactUserComp = userCompDetails?.componentFromFramework;\n  const UserCompClass = userCompDetails?.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, style: userStyles, className, role: \"gridcell\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eFloatingFilterBody, className: bodyClassName, role: \"presentation\" }, reactUserComp ? reactiveCustomComponents ? floatingFilterProps && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    CustomContext.Provider,\n    {\n      value: {\n        setMethods: (methods) => floatingFilterCompProxy.setMethods(methods)\n      }\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...floatingFilterProps })\n  ) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompStateless ? () => {\n  } : userCompRef }) : null), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: eButtonWrapper,\n      \"aria-hidden\": buttonWrapperAriaHidden,\n      className: buttonWrapperClassName,\n      role: \"presentation\"\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"button\",\n      {\n        ref: eButtonShowMainFilter,\n        type: \"button\",\n        className: \"ag-button ag-floating-filter-button-button\",\n        tabIndex: -1\n      }\n    )\n  ));\n};\nvar headerFilterCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderFilterCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerGroupCellComp.tsx\n\n\nvar HeaderGroupCellComp = ({ ctrl }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [cssResizableClasses, setResizableCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [resizableAriaHidden, setResizableAriaHidden] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"false\");\n  const [ariaExpanded, setAriaExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const colId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ctrl.column.getUniqueId(), []);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eHeaderCompWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !ctrl.isAlive()) {\n      return;\n    }\n    const compProxy = {\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setUserStyles: (styles) => setUserStyles(styles),\n      setHeaderWrapperHidden: (hidden) => {\n        const headerCompWrapper = eHeaderCompWrapper.current;\n        if (!headerCompWrapper) {\n          return;\n        }\n        if (hidden) {\n          headerCompWrapper.style.setProperty(\"display\", \"none\");\n        } else {\n          headerCompWrapper.style.removeProperty(\"display\");\n        }\n      },\n      setHeaderWrapperMaxHeight: (value) => {\n        const headerCompWrapper = eHeaderCompWrapper.current;\n        if (!headerCompWrapper) {\n          return;\n        }\n        if (value != null) {\n          headerCompWrapper.style.setProperty(\"max-height\", `${value}px`);\n        } else {\n          headerCompWrapper.style.removeProperty(\"max-height\");\n        }\n        headerCompWrapper.classList.toggle(\"ag-header-cell-comp-wrapper-limited-height\", value != null);\n      },\n      setUserCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      setResizableDisplayed: (displayed) => {\n        setResizableCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed));\n        setResizableAriaHidden(!displayed ? \"true\" : \"false\");\n      },\n      setAriaExpanded: (expanded) => setAriaExpanded(expanded),\n      getUserCompInstance: () => userCompRef.current || void 0\n    };\n    ctrl.setComp(compProxy, eRef, eResize.current, eHeaderCompWrapper.current, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => showJsComp(userCompDetails, context, eHeaderCompWrapper.current), [userCompDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (eGui.current) {\n      ctrl.setDragSource(eGui.current);\n    }\n  }, [userCompDetails]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails?.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => \"ag-header-group-cell \" + cssClasses.toString(), [cssClasses]);\n  const resizableClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => \"ag-header-cell-resize \" + cssResizableClasses.toString(),\n    [cssResizableClasses]\n  );\n  const reactUserComp = userCompDetails?.componentFromFramework;\n  const UserCompClass = userCompDetails?.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      style: userStyles,\n      className,\n      \"col-id\": colId,\n      role: \"columnheader\",\n      \"aria-expanded\": ariaExpanded\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eHeaderCompWrapper, className: \"ag-header-cell-comp-wrapper\", role: \"presentation\" }, reactUserComp ? userCompStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompRef }) : null),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eResize, \"aria-hidden\": resizableAriaHidden, className: resizableClassName })\n  );\n};\nvar headerGroupCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderGroupCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerRowComp.tsx\nvar HeaderRowComp = ({ ctrl }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { topOffset, rowHeight } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ctrl.getTopAndHeight(), []);\n  const [ariaRowIndex, setAriaRowIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(ctrl.getAriaRowIndex());\n  const className = ctrl.headerRowClass;\n  const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowHeight + \"px\");\n  const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => topOffset + \"px\");\n  const cellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [cellCtrls, setCellCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => ctrl.getUpdatedHeaderCtrls());\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setHeight: (height2) => setHeight(height2),\n      setTop: (top2) => setTop(top2),\n      setHeaderCtrls: (ctrls, forceOrder, afterScroll) => {\n        const prevCellCtrls = cellCtrlsRef.current;\n        const nextCells = getNextValueIfDifferent(prevCellCtrls, ctrls, forceOrder);\n        if (nextCells !== prevCellCtrls) {\n          cellCtrlsRef.current = nextCells;\n          agFlushSync(afterScroll, () => setCellCtrls(nextCells));\n        }\n      },\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      setRowIndex: (rowIndex) => {\n        setAriaRowIndex(rowIndex);\n      }\n    };\n    ctrl.setComp(compProxy, compBean.current, false);\n  }, []);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height,\n      top\n    }),\n    [height, top]\n  );\n  const createCellJsx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((cellCtrl) => {\n    switch (ctrl.type) {\n      case \"group\":\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerGroupCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n      case \"filter\":\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerFilterCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n      default:\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n    }\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, role: \"row\", style, \"aria-rowindex\": ariaRowIndex }, cellCtrls.map(createCellJsx));\n};\nvar headerRowComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRowComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerRowContainerComp.tsx\nvar HeaderRowContainerComp = ({ pinned }) => {\n  const [displayed, setDisplayed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [headerRowCtrls, setHeaderRowCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eCenterContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const headerRowCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const pinnedLeft = pinned === \"left\";\n  const pinnedRight = pinned === \"right\";\n  const centre = !pinnedLeft && !pinnedRight;\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    headerRowCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.HeaderRowContainerCtrl(pinned)) : context.destroyBean(headerRowCtrlRef.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setDisplayed,\n      setCtrls: (ctrls) => setHeaderRowCtrls(ctrls),\n      // centre only\n      setCenterWidth: (width) => {\n        if (eCenterContainer.current) {\n          eCenterContainer.current.style.width = width;\n        }\n      },\n      setViewportScrollLeft: (left) => {\n        if (eGui.current) {\n          eGui.current.scrollLeft = left;\n        }\n      },\n      // pinned only\n      setPinnedContainerWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n          eGui.current.style.minWidth = width;\n          eGui.current.style.maxWidth = width;\n        }\n      }\n    };\n    headerRowCtrlRef.current.setComp(compProxy, eGui.current);\n  }, []);\n  const className = !displayed ? \"ag-hidden\" : \"\";\n  const insertRowsJsx = () => headerRowCtrls.map((ctrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowComp_default, { ctrl, key: ctrl.instanceId }));\n  return pinnedLeft ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-pinned-left-header \" + className, \"aria-hidden\": !displayed, role: \"rowgroup\" }, insertRowsJsx()) : pinnedRight ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-pinned-right-header \" + className, \"aria-hidden\": !displayed, role: \"rowgroup\" }, insertRowsJsx()) : centre ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-header-viewport \" + className, role: \"presentation\", tabIndex: -1 }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eCenterContainer, className: \"ag-header-container\", role: \"rowgroup\" }, insertRowsJsx())) : null;\n};\nvar headerRowContainerComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRowContainerComp);\n\n// packages/ag-grid-react/src/reactUi/header/gridHeaderComp.tsx\nvar GridHeaderComp = () => {\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const gridCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    gridCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridHeaderCtrl()) : context.destroyBean(gridCtrlRef.current);\n    if (!eRef)\n      return;\n    const compProxy = {\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setHeightAndMinHeight: (height2) => setHeight(height2)\n    };\n    gridCtrlRef.current.setComp(compProxy, eRef, eRef);\n  }, []);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = cssClasses.toString();\n    return \"ag-header \" + res;\n  }, [cssClasses]);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height,\n      minHeight: height\n    }),\n    [height]\n  );\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, style, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: \"left\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: null }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: \"right\" }));\n};\nvar gridHeaderComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridHeaderComp);\n\n// packages/ag-grid-react/src/reactUi/reactComment.tsx\n\nvar useReactCommentEffect = (comment, eForCommentRef) => {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const eForComment = eForCommentRef.current;\n    if (eForComment) {\n      const eParent = eForComment.parentElement;\n      if (eParent) {\n        const eComment = document.createComment(comment);\n        eParent.insertBefore(eComment, eForComment);\n        return () => {\n          eParent.removeChild(eComment);\n        };\n      }\n    }\n  }, [comment]);\n};\nvar reactComment_default = useReactCommentEffect;\n\n// packages/ag-grid-react/src/reactUi/rows/rowContainerComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/rows/rowComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/cells/cellComp.tsx\n\n\n\n// packages/ag-grid-react/src/shared/customComp/cellEditorComponentProxy.ts\n\nvar CellEditorComponentProxy = class {\n  constructor(cellEditorParams, refreshProps) {\n    this.cellEditorParams = cellEditorParams;\n    this.refreshProps = refreshProps;\n    this.instanceCreated = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveInstanceCreated = resolve;\n    });\n    this.onValueChange = (value) => this.updateValue(value);\n    this.value = cellEditorParams.value;\n  }\n  getProps() {\n    return {\n      ...this.cellEditorParams,\n      initialValue: this.cellEditorParams.value,\n      value: this.value,\n      onValueChange: this.onValueChange\n    };\n  }\n  getValue() {\n    return this.value;\n  }\n  refresh(params) {\n    this.cellEditorParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getInstance() {\n    return this.instanceCreated.then(() => this.componentInstance);\n  }\n  setRef(componentInstance) {\n    this.componentInstance = componentInstance;\n    this.resolveInstanceCreated?.();\n    this.resolveInstanceCreated = void 0;\n  }\n  getOptionalMethods() {\n    return [\n      \"isCancelBeforeStart\",\n      \"isCancelAfterEnd\",\n      \"focusIn\",\n      \"focusOut\",\n      \"afterGuiAttached\",\n      \"getValidationErrors\",\n      \"getValidationElement\"\n    ];\n  }\n  updateValue(value) {\n    this.value = value;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/cells/cellEditorComp.tsx\n\n\n// packages/ag-grid-react/src/reactUi/cells/popupEditorComp.tsx\n\n\n\n\n// packages/ag-grid-react/src/reactUi/useEffectOnce.tsx\n\nvar useEffectOnce = (effect) => {\n  const effectFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(effect);\n  const destroyFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const effectCalled = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rendered = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [, setVal] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  if (effectCalled.current) {\n    rendered.current = true;\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!effectCalled.current) {\n      destroyFn.current = effectFn.current();\n      effectCalled.current = true;\n    }\n    setVal((val) => val + 1);\n    return () => {\n      if (!rendered.current) {\n        return;\n      }\n      destroyFn.current?.();\n    };\n  }, []);\n};\n\n// packages/ag-grid-react/src/reactUi/cells/popupEditorComp.tsx\nvar PopupEditorComp = (props) => {\n  const [popupEditorWrapper, setPopupEditorWrapper] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context, popupSvc, gos, editSvc } = beans;\n  const { editDetails, cellCtrl, eParentCell } = props;\n  useEffectOnce(() => {\n    const { compDetails } = editDetails;\n    const useModelPopup = gos.get(\"stopEditingWhenCellsLoseFocus\");\n    const wrapper = context.createBean(editSvc.createPopupEditorWrapper(compDetails.params));\n    const ePopupGui = wrapper.getGui();\n    if (props.jsChildComp) {\n      const eChildGui = props.jsChildComp.getGui();\n      if (eChildGui) {\n        ePopupGui.appendChild(eChildGui);\n      }\n    }\n    const { column, rowNode } = cellCtrl;\n    const positionParams = {\n      column,\n      rowNode,\n      type: \"popupCellEditor\",\n      eventSource: eParentCell,\n      ePopup: ePopupGui,\n      position: editDetails.popupPosition,\n      keepWithinBounds: true\n    };\n    const positionCallback = popupSvc?.positionPopupByComponent.bind(popupSvc, positionParams);\n    const addPopupRes = popupSvc?.addPopup({\n      modal: useModelPopup,\n      eChild: ePopupGui,\n      closeOnEsc: true,\n      closedCallback: () => {\n        cellCtrl.onPopupEditorClosed();\n      },\n      anchorToElement: eParentCell,\n      positionCallback,\n      ariaOwns: eParentCell\n    });\n    const hideEditorPopup = addPopupRes ? addPopupRes.hideFunc : void 0;\n    setPopupEditorWrapper(wrapper);\n    props.jsChildComp?.afterGuiAttached?.();\n    return () => {\n      hideEditorPopup?.();\n      context.destroyBean(wrapper);\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    return () => {\n      if (cellCtrl.isCellFocused() && popupEditorWrapper?.getGui().contains((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getActiveDomElement)(beans))) {\n        eParentCell.focus({ preventScroll: true });\n      }\n    };\n  }, [popupEditorWrapper]);\n  return popupEditorWrapper && props.wrappedContent ? (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(props.wrappedContent, popupEditorWrapper.getGui()) : null;\n};\nvar popupEditorComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(PopupEditorComp);\n\n// packages/ag-grid-react/src/reactUi/cells/cellEditorComp.tsx\nvar jsxEditorProxy = (editDetails, CellEditorClass, setRef2) => {\n  const { compProxy } = editDetails;\n  setRef2(compProxy);\n  const props = compProxy.getProps();\n  const isStateless = isComponentStateless(CellEditorClass);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    CustomContext.Provider,\n    {\n      value: {\n        setMethods: (methods) => compProxy.setMethods(methods)\n      }\n    },\n    isStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...props }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...props, ref: (ref) => compProxy.setRef(ref) })\n  );\n};\nvar jsxEditor = (editDetails, CellEditorClass, setRef2) => {\n  const newFormat = editDetails.compProxy;\n  return newFormat ? jsxEditorProxy(editDetails, CellEditorClass, setRef2) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...editDetails.compDetails.params, ref: setRef2 });\n};\nvar jsxEditValue = (editDetails, setCellEditorRef, eGui, cellCtrl, jsEditorComp) => {\n  const compDetails = editDetails.compDetails;\n  const CellEditorClass = compDetails.componentClass;\n  const reactInlineEditor = compDetails.componentFromFramework && !editDetails.popup;\n  const reactPopupEditor = compDetails.componentFromFramework && editDetails.popup;\n  const jsPopupEditor = !compDetails.componentFromFramework && editDetails.popup;\n  return reactInlineEditor ? jsxEditor(editDetails, CellEditorClass, setCellEditorRef) : reactPopupEditor ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    popupEditorComp_default,\n    {\n      editDetails,\n      cellCtrl,\n      eParentCell: eGui,\n      wrappedContent: jsxEditor(editDetails, CellEditorClass, setCellEditorRef)\n    }\n  ) : jsPopupEditor && jsEditorComp ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(popupEditorComp_default, { editDetails, cellCtrl, eParentCell: eGui, jsChildComp: jsEditorComp }) : null;\n};\n\n// packages/ag-grid-react/src/reactUi/cells/showJsRenderer.tsx\n\nvar useJsCellRenderer = (showDetails, showTools, eCellValue, cellValueVersion, jsCellRendererRef, eGui) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const destroyCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const comp = jsCellRendererRef.current;\n    if (!comp) {\n      return;\n    }\n    const compGui = comp.getGui();\n    if (compGui && compGui.parentElement) {\n      compGui.parentElement.removeChild(compGui);\n    }\n    context.destroyBean(comp);\n    jsCellRendererRef.current = void 0;\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const showValue = showDetails != null;\n    const jsCompDetails = showDetails?.compDetails && !showDetails.compDetails.componentFromFramework;\n    const waitingForToolsSetup = showTools && eCellValue == null;\n    const showComp = showValue && jsCompDetails && !waitingForToolsSetup;\n    if (!showComp) {\n      destroyCellRenderer();\n      return;\n    }\n    const compDetails = showDetails.compDetails;\n    if (jsCellRendererRef.current) {\n      const comp = jsCellRendererRef.current;\n      const attemptRefresh = comp.refresh != null && showDetails.force == false;\n      const refreshResult = attemptRefresh ? comp.refresh(compDetails.params) : false;\n      const refreshWorked = refreshResult === true || refreshResult === void 0;\n      if (refreshWorked) {\n        return;\n      }\n      destroyCellRenderer();\n    }\n    const promise = compDetails.newAgStackInstance();\n    promise.then((comp) => {\n      if (!comp) {\n        return;\n      }\n      const compGui = comp.getGui();\n      if (!compGui) {\n        return;\n      }\n      const parent = showTools ? eCellValue : eGui.current;\n      parent.appendChild(compGui);\n      jsCellRendererRef.current = comp;\n    });\n  }, [showDetails, showTools, cellValueVersion]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return destroyCellRenderer;\n  }, []);\n};\nvar showJsRenderer_default = useJsCellRenderer;\n\n// packages/ag-grid-react/src/reactUi/cells/skeletonCellComp.tsx\n\nvar SkeletonCellRenderer = ({\n  cellCtrl,\n  parent\n}) => {\n  const jsCellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const renderDetails = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const { loadingComp } = cellCtrl.getDeferLoadingCellRenderer();\n    return loadingComp ? {\n      value: void 0,\n      compDetails: loadingComp,\n      force: false\n    } : void 0;\n  }, [cellCtrl]);\n  showJsRenderer_default(renderDetails, false, void 0, 1, jsCellRendererRef, parent);\n  if (renderDetails?.compDetails?.componentFromFramework) {\n    const CellRendererClass = renderDetails.compDetails.componentClass;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...renderDetails.compDetails.params });\n  }\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null);\n};\n\n// packages/ag-grid-react/src/reactUi/cells/cellComp.tsx\nvar CellComp = ({\n  cellCtrl,\n  printLayout,\n  editingCell\n}) => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context } = beans;\n  const {\n    column: { colIdSanitised },\n    instanceId\n  } = cellCtrl;\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [renderDetails, setRenderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => cellCtrl.isCellRenderer() ? void 0 : { compDetails: void 0, value: cellCtrl.getValueToDisplay(), force: false }\n  );\n  const [editDetails, setEditDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [renderKey, setRenderKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [includeSelection, setIncludeSelection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [includeRowDrag, setIncludeRowDrag] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [includeDndSource, setIncludeDndSource] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [jsEditorComp, setJsEditorComp] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const forceWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cellCtrl.isForceWrapper(), [cellCtrl]);\n  const cellAriaRole = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cellCtrl.getCellAriaRole(), [cellCtrl]);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const cellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const jsCellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cellEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eCellWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cellWrapperDestroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const eCellValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [cellValueVersion, setCellValueVersion] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const setCellValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eCellValue.current = ref;\n    setCellValueVersion((v) => v + 1);\n  }, []);\n  const showTools = renderDetails != null && (includeSelection || includeDndSource || includeRowDrag) && (editDetails == null || !!editDetails.popup);\n  const showCellWrapper = forceWrapper || showTools;\n  const setCellEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (cellEditor) => {\n      cellEditorRef.current = cellEditor;\n      if (cellEditor) {\n        const editingCancelledByUserComp = cellEditor.isCancelBeforeStart && cellEditor.isCancelBeforeStart();\n        setTimeout(() => {\n          if (editingCancelledByUserComp) {\n            cellCtrl.stopEditing(true);\n            cellCtrl.focusCell(true);\n          } else {\n            cellCtrl.cellEditorAttached();\n            cellCtrl.enableEditorTooltipFeature(cellEditor);\n          }\n        });\n      }\n    },\n    [cellCtrl]\n  );\n  const cssManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssManager.current) {\n    cssManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  showJsRenderer_default(renderDetails, showCellWrapper, eCellValue.current, cellValueVersion, jsCellRendererRef, eGui);\n  const lastRenderDetails = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    const oldDetails = lastRenderDetails.current;\n    const newDetails = renderDetails;\n    lastRenderDetails.current = renderDetails;\n    if (oldDetails == null || oldDetails.compDetails == null || newDetails == null || newDetails.compDetails == null) {\n      return;\n    }\n    const oldCompDetails = oldDetails.compDetails;\n    const newCompDetails = newDetails.compDetails;\n    if (oldCompDetails.componentClass != newCompDetails.componentClass) {\n      return;\n    }\n    if (cellRendererRef.current?.refresh == null) {\n      return;\n    }\n    const result = cellRendererRef.current.refresh(newCompDetails.params);\n    if (result != true) {\n      setRenderKey((prev) => prev + 1);\n    }\n  }, [renderDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    const doingJsEditor = editDetails && !editDetails.compDetails.componentFromFramework;\n    if (!doingJsEditor) {\n      return;\n    }\n    const compDetails = editDetails.compDetails;\n    const isPopup = editDetails.popup === true;\n    const cellEditorPromise = compDetails.newAgStackInstance();\n    cellEditorPromise.then((cellEditor) => {\n      if (!cellEditor) {\n        return;\n      }\n      const compGui = cellEditor.getGui();\n      setCellEditorRef(cellEditor);\n      if (!isPopup) {\n        const parentEl = (forceWrapper ? eCellWrapper : eGui).current;\n        parentEl?.appendChild(compGui);\n        cellEditor.afterGuiAttached && cellEditor.afterGuiAttached();\n      }\n      setJsEditorComp(cellEditor);\n    });\n    return () => {\n      cellEditorPromise.then((cellEditor) => {\n        const compGui = cellEditor.getGui();\n        cellCtrl.disableEditorTooltipFeature();\n        context.destroyBean(cellEditor);\n        setCellEditorRef(void 0);\n        setJsEditorComp(void 0);\n        compGui?.parentElement?.removeChild(compGui);\n      });\n    };\n  }, [editDetails]);\n  const setCellWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (eRef) => {\n      eCellWrapper.current = eRef;\n      if (!eRef) {\n        cellWrapperDestroyFuncs.current.forEach((f) => f());\n        cellWrapperDestroyFuncs.current = [];\n        return;\n      }\n      const addComp = (comp) => {\n        if (comp) {\n          const eGui2 = comp.getGui();\n          eRef.insertAdjacentElement(\"afterbegin\", eGui2);\n          cellWrapperDestroyFuncs.current.push(() => {\n            context.destroyBean(comp);\n            (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._removeFromParent)(eGui2);\n          });\n        }\n        return comp;\n      };\n      if (includeSelection) {\n        const checkboxSelectionComp = cellCtrl.createSelectionCheckbox();\n        addComp(checkboxSelectionComp);\n      }\n      if (includeDndSource) {\n        addComp(cellCtrl.createDndSource());\n      }\n      if (includeRowDrag) {\n        addComp(cellCtrl.createRowDragComp());\n      }\n    },\n    [cellCtrl, context, includeDndSource, includeRowDrag, includeSelection]\n  );\n  const init = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const spanReady = !cellCtrl.isCellSpanning() || eWrapper.current;\n    const eRef = eGui.current;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !spanReady || !cellCtrl) {\n      return;\n    }\n    const compProxy = {\n      toggleCss: (name, on) => cssManager.current.toggleCss(name, on),\n      setUserStyles: (styles) => setUserStyles(styles),\n      getFocusableElement: () => eGui.current,\n      setIncludeSelection: (include) => setIncludeSelection(include),\n      setIncludeRowDrag: (include) => setIncludeRowDrag(include),\n      setIncludeDndSource: (include) => setIncludeDndSource(include),\n      getCellEditor: () => cellEditorRef.current || null,\n      getCellRenderer: () => cellRendererRef.current ?? jsCellRendererRef.current,\n      getParentOfValue: () => eCellValue.current ?? eCellWrapper.current ?? eGui.current,\n      setRenderDetails: (compDetails, value, force) => {\n        const setDetails = () => {\n          setRenderDetails((prev) => {\n            if (prev?.compDetails !== compDetails || prev?.value !== value || prev?.force !== force) {\n              return {\n                value,\n                compDetails,\n                force\n              };\n            } else {\n              return prev;\n            }\n          });\n        };\n        if (compDetails?.params?.deferRender && !cellCtrl.rowNode.group) {\n          const { loadingComp, onReady } = cellCtrl.getDeferLoadingCellRenderer();\n          if (loadingComp) {\n            setRenderDetails({\n              value: void 0,\n              compDetails: loadingComp,\n              force: false\n            });\n            onReady.then(() => agStartTransition(setDetails));\n            return;\n          }\n        }\n        setDetails();\n      },\n      setEditDetails: (compDetails, popup, popupPosition, reactiveCustomComponents) => {\n        if (compDetails) {\n          let compProxy2 = void 0;\n          if (compDetails.componentFromFramework) {\n            if (reactiveCustomComponents) {\n              compProxy2 = new CellEditorComponentProxy(\n                compDetails.params,\n                () => setRenderKey((prev) => prev + 1)\n              );\n            } else {\n              warnReactiveCustomComponents();\n            }\n          }\n          setEditDetails({\n            compDetails,\n            popup,\n            popupPosition,\n            compProxy: compProxy2\n          });\n          if (!popup) {\n            setRenderDetails(void 0);\n          }\n        } else {\n          const recoverFocus = cellCtrl.hasBrowserFocus();\n          if (recoverFocus) {\n            compProxy.getFocusableElement().focus({ preventScroll: true });\n          }\n          setEditDetails((editDetails2) => {\n            if (editDetails2?.compProxy) {\n              cellEditorRef.current = void 0;\n            }\n            return void 0;\n          });\n        }\n      },\n      refreshEditStyles: (editing, isPopup) => {\n        if (!eGui.current) {\n          return;\n        }\n        const { current } = cssManager;\n        current.toggleCss(\"ag-cell-value\", !showCellWrapper);\n        current.toggleCss(\"ag-cell-inline-editing\", !!editing && !isPopup);\n        current.toggleCss(\"ag-cell-popup-editing\", !!editing && !!isPopup);\n        current.toggleCss(\"ag-cell-not-inline-editing\", !editing || !!isPopup);\n      }\n    };\n    const cellWrapperOrUndefined = eCellWrapper.current || void 0;\n    cellCtrl.setComp(\n      compProxy,\n      eRef,\n      eWrapper.current ?? void 0,\n      cellWrapperOrUndefined,\n      printLayout,\n      editingCell,\n      compBean.current\n    );\n  }, []);\n  const setGuiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eGui.current = ref;\n    init();\n  }, []);\n  const setWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eWrapper.current = ref;\n    init();\n  }, []);\n  const reactCellRendererStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = renderDetails?.compDetails?.componentFromFramework && isComponentStateless(renderDetails.compDetails.componentClass);\n    return !!res;\n  }, [renderDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!eGui.current) {\n      return;\n    }\n    const { current } = cssManager;\n    current.toggleCss(\"ag-cell-value\", !showCellWrapper);\n    current.toggleCss(\"ag-cell-inline-editing\", !!editDetails && !editDetails.popup);\n    current.toggleCss(\"ag-cell-popup-editing\", !!editDetails && !!editDetails.popup);\n    current.toggleCss(\"ag-cell-not-inline-editing\", !editDetails || !!editDetails.popup);\n  });\n  const valueOrCellComp = () => {\n    const { compDetails, value } = renderDetails;\n    if (!compDetails) {\n      return value?.toString?.() ?? value;\n    }\n    if (compDetails.componentFromFramework) {\n      const CellRendererClass = compDetails.componentClass;\n      return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, { fallback: /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SkeletonCellRenderer, { cellCtrl, parent: eGui }) }, reactCellRendererStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...compDetails.params, key: renderKey }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...compDetails.params, key: renderKey, ref: cellRendererRef }));\n    }\n  };\n  const showCellOrEditor = () => {\n    const showCellValue = () => {\n      if (renderDetails == null) {\n        return null;\n      }\n      return showCellWrapper ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { role: \"presentation\", id: `cell-${instanceId}`, className: \"ag-cell-value\", ref: setCellValueRef }, valueOrCellComp()) : valueOrCellComp();\n    };\n    const showEditValue = (details) => jsxEditValue(details, setCellEditorRef, eGui.current, cellCtrl, jsEditorComp);\n    if (editDetails != null) {\n      if (editDetails.popup) {\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, showCellValue(), showEditValue(editDetails));\n      }\n      return showEditValue(editDetails);\n    }\n    return showCellValue();\n  };\n  const renderCell = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setGuiRef, style: userStyles, role: cellAriaRole, \"col-id\": colIdSanitised }, showCellWrapper ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ag-cell-wrapper\", role: \"presentation\", ref: setCellWrapperRef }, showCellOrEditor()) : showCellOrEditor());\n  if (cellCtrl.isCellSpanning()) {\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setWrapperRef, className: \"ag-spanned-cell-wrapper\", role: \"presentation\" }, renderCell());\n  }\n  return renderCell();\n};\nvar cellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(CellComp);\n\n// packages/ag-grid-react/src/reactUi/rows/rowComp.tsx\nvar RowComp = ({ rowCtrl, containerType }) => {\n  const { context, gos, editSvc } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const enableUses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(RenderModeContext) === \"default\";\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const domOrderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(rowCtrl.getDomOrder());\n  const isFullWidth = rowCtrl.isFullWidth();\n  const isDisplayed = rowCtrl.rowNode.displayed;\n  const [rowIndex, setRowIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.rowNode.getRowIndexString() : null\n  );\n  const [rowId, setRowId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.rowId);\n  const [rowBusinessKey, setRowBusinessKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.businessKey);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.rowStyles);\n  const cellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [cellCtrlsFlushSync, setCellCtrlsFlushSync] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => null);\n  const [fullWidthCompDetails, setFullWidthCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.getInitialRowTop(containerType) : void 0\n  );\n  const [transform, setTransform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.getInitialTransform(containerType) : void 0\n  );\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const fullWidthCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const autoHeightSetup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [autoHeightSetupAttempt, setAutoHeightSetupAttempt] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (autoHeightSetup.current || !fullWidthCompDetails || autoHeightSetupAttempt > 10) {\n      return;\n    }\n    const eChild = eGui.current?.firstChild;\n    if (eChild) {\n      rowCtrl.setupDetailRowAutoHeight(eChild);\n      autoHeightSetup.current = true;\n    } else {\n      setAutoHeightSetupAttempt((prev) => prev + 1);\n    }\n  }, [fullWidthCompDetails, autoHeightSetupAttempt]);\n  const cssManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssManager.current) {\n    cssManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  const cellsChanged = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => {\n  });\n  const sub = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((onStoreChange) => {\n    cellsChanged.current = onStoreChange;\n    return () => {\n      cellsChanged.current = () => {\n      };\n    };\n  }, []);\n  const cellCtrlsUses = agUseSyncExternalStore(\n    sub,\n    () => {\n      return cellCtrlsRef.current;\n    },\n    []\n  );\n  const cellCtrlsMerged = enableUses ? cellCtrlsUses : cellCtrlsFlushSync;\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      rowCtrl.unsetComp(containerType);\n      return;\n    }\n    if (!rowCtrl.isAlive()) {\n      return;\n    }\n    const compProxy = {\n      // the rowTop is managed by state, instead of direct style manipulation by rowCtrl (like all the other styles)\n      // as we need to have an initial value when it's placed into he DOM for the first time, for animation to work.\n      setTop,\n      setTransform,\n      // i found using React for managing classes at the row level was to slow, as modifying classes caused a lot of\n      // React code to execute, so avoiding React for managing CSS Classes made the grid go much faster.\n      toggleCss: (name, on) => cssManager.current.toggleCss(name, on),\n      setDomOrder: (domOrder) => domOrderRef.current = domOrder,\n      setRowIndex,\n      setRowId,\n      setRowBusinessKey,\n      setUserStyles,\n      // if we don't maintain the order, then cols will be ripped out and into the dom\n      // when cols reordered, which would stop the CSS transitions from working\n      setCellCtrls: (next, useFlushSync) => {\n        const prevCellCtrls = cellCtrlsRef.current;\n        const nextCells = getNextValueIfDifferent(prevCellCtrls, next, domOrderRef.current);\n        if (nextCells !== prevCellCtrls) {\n          cellCtrlsRef.current = nextCells;\n          if (enableUses) {\n            cellsChanged.current();\n          } else {\n            agFlushSync(useFlushSync, () => setCellCtrlsFlushSync(nextCells));\n          }\n        }\n      },\n      showFullWidth: (compDetails) => setFullWidthCompDetails(compDetails),\n      getFullWidthCellRenderer: () => fullWidthCompRef.current,\n      refreshFullWidth: (getUpdatedParams) => {\n        if (canRefreshFullWidthRef.current) {\n          setFullWidthCompDetails((prevFullWidthCompDetails) => ({\n            ...prevFullWidthCompDetails,\n            params: getUpdatedParams()\n          }));\n          return true;\n        } else {\n          if (!fullWidthCompRef.current || !fullWidthCompRef.current.refresh) {\n            return false;\n          }\n          return fullWidthCompRef.current.refresh(getUpdatedParams());\n        }\n      }\n    };\n    rowCtrl.setComp(compProxy, eRef, containerType, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(fullWidthCompDetails, context, eGui.current, fullWidthCompRef),\n    [fullWidthCompDetails]\n  );\n  const rowStyles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = { top, transform };\n    Object.assign(res, userStyles);\n    return res;\n  }, [top, transform, userStyles]);\n  const showFullWidthFramework = isFullWidth && fullWidthCompDetails?.componentFromFramework;\n  const showCells = !isFullWidth && cellCtrlsMerged != null;\n  const reactFullWidthCellRendererStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = fullWidthCompDetails?.componentFromFramework && isComponentStateless(fullWidthCompDetails.componentClass);\n    return !!res;\n  }, [fullWidthCompDetails]);\n  const canRefreshFullWidthRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    canRefreshFullWidthRef.current = reactFullWidthCellRendererStateless && !!fullWidthCompDetails && !!gos.get(\"reactiveCustomComponents\");\n  }, [reactFullWidthCellRendererStateless, fullWidthCompDetails]);\n  const showCellsJsx = () => cellCtrlsMerged?.map((cellCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    cellComp_default,\n    {\n      cellCtrl,\n      editingCell: editSvc?.isEditing(cellCtrl, { withOpenEditor: true }) ?? false,\n      printLayout: rowCtrl.printLayout,\n      key: cellCtrl.instanceId\n    }\n  ));\n  const showFullWidthFrameworkJsx = () => {\n    const FullWidthComp = fullWidthCompDetails.componentClass;\n    return reactFullWidthCellRendererStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullWidthComp, { ...fullWidthCompDetails.params }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullWidthComp, { ...fullWidthCompDetails.params, ref: fullWidthCompRef });\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      role: \"row\",\n      style: rowStyles,\n      \"row-index\": rowIndex,\n      \"row-id\": rowId,\n      \"row-business-key\": rowBusinessKey\n    },\n    showCells ? showCellsJsx() : showFullWidthFramework ? showFullWidthFrameworkJsx() : null\n  );\n};\nvar rowComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(RowComp);\n\n// packages/ag-grid-react/src/reactUi/rows/rowContainerComp.tsx\nvar RowContainerComp = ({ name }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const containerOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowContainerOptions)(name), [name]);\n  const eViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eSpanContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const rowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [rowCtrlsOrdered, setRowCtrlsOrdered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => []);\n  const isSpanning = !!gos.get(\"enableCellSpan\") && !!containerOptions.getSpannedRowCtrls;\n  const spannedRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevSpannedRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [spannedRowCtrlsOrdered, setSpannedRowCtrlsOrdered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => []);\n  const domOrderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rowContainerCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const viewportClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-viewport\", (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowViewportClass)(name)), [name]);\n  const containerClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowContainerClass)(name)), [name]);\n  const spanClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-spanning-container\", (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowSpanContainerClass)(name)), [name]);\n  const shouldRenderViewport = containerOptions.type === \"center\" || isSpanning;\n  const topLevelRef = shouldRenderViewport ? eViewport : eContainer;\n  reactComment_default(\" AG Row Container \" + name + \" \", topLevelRef);\n  const areElementsReady = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const viewportReady = !shouldRenderViewport || eViewport.current != null;\n    const containerReady = eContainer.current != null;\n    const spanContainerReady = !isSpanning || eSpanContainer.current != null;\n    return viewportReady && containerReady && spanContainerReady;\n  }, []);\n  const areElementsRemoved = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    return eViewport.current == null && eContainer.current == null && eSpanContainer.current == null;\n  }, []);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (areElementsRemoved()) {\n      rowContainerCtrlRef.current = context.destroyBean(rowContainerCtrlRef.current);\n    }\n    if (areElementsReady()) {\n      const updateRowCtrlsOrdered = (useFlushSync) => {\n        const next = getNextValueIfDifferent(\n          prevRowCtrlsRef.current,\n          rowCtrlsRef.current,\n          domOrderRef.current\n        );\n        if (next !== prevRowCtrlsRef.current) {\n          prevRowCtrlsRef.current = next;\n          agFlushSync(useFlushSync, () => setRowCtrlsOrdered(next));\n        }\n      };\n      const updateSpannedRowCtrlsOrdered = (useFlushSync) => {\n        const next = getNextValueIfDifferent(\n          prevSpannedRowCtrlsRef.current,\n          spannedRowCtrlsRef.current,\n          domOrderRef.current\n        );\n        if (next !== prevSpannedRowCtrlsRef.current) {\n          prevSpannedRowCtrlsRef.current = next;\n          agFlushSync(useFlushSync, () => setSpannedRowCtrlsOrdered(next));\n        }\n      };\n      const compProxy = {\n        setHorizontalScroll: (offset) => {\n          if (eViewport.current) {\n            eViewport.current.scrollLeft = offset;\n          }\n        },\n        setViewportHeight: (height) => {\n          if (eViewport.current) {\n            eViewport.current.style.height = height;\n          }\n        },\n        setRowCtrls: ({ rowCtrls, useFlushSync }) => {\n          const useFlush = !!useFlushSync && rowCtrlsRef.current.length > 0 && rowCtrls.length > 0;\n          rowCtrlsRef.current = rowCtrls;\n          updateRowCtrlsOrdered(useFlush);\n        },\n        setSpannedRowCtrls: (rowCtrls, useFlushSync) => {\n          const useFlush = !!useFlushSync && spannedRowCtrlsRef.current.length > 0 && rowCtrls.length > 0;\n          spannedRowCtrlsRef.current = rowCtrls;\n          updateSpannedRowCtrlsOrdered(useFlush);\n        },\n        setDomOrder: (domOrder) => {\n          if (domOrderRef.current != domOrder) {\n            domOrderRef.current = domOrder;\n            updateRowCtrlsOrdered(false);\n          }\n        },\n        setContainerWidth: (width) => {\n          if (eContainer.current) {\n            eContainer.current.style.width = width;\n          }\n        },\n        setOffsetTop: (offset) => {\n          if (eContainer.current) {\n            eContainer.current.style.transform = `translateY(${offset})`;\n          }\n        }\n      };\n      rowContainerCtrlRef.current = context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.RowContainerCtrl(name));\n      rowContainerCtrlRef.current.setComp(\n        compProxy,\n        eContainer.current,\n        eSpanContainer.current ?? void 0,\n        eViewport.current\n      );\n    }\n  }, [areElementsReady, areElementsRemoved]);\n  const setContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eContainer.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const setSpanContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eSpanContainer.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const setViewportRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eViewport.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const buildContainer = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: containerClasses, ref: setContainerRef, role: \"rowgroup\" }, rowCtrlsOrdered.map((rowCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowComp_default, { rowCtrl, containerType: containerOptions.type, key: rowCtrl.instanceId })));\n  if (!shouldRenderViewport) {\n    return buildContainer();\n  }\n  const buildSpanContainer = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: spanClasses, ref: setSpanContainerRef, role: \"rowgroup\" }, spannedRowCtrlsOrdered.map((rowCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowComp_default, { rowCtrl, containerType: containerOptions.type, key: rowCtrl.instanceId })));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: viewportClasses, ref: setViewportRef, role: \"presentation\" }, buildContainer(), isSpanning ? buildSpanContainer() : null);\n};\nvar rowContainerComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(RowContainerComp);\n\n// packages/ag-grid-react/src/reactUi/gridBodyComp.tsx\nvar GridBodyComp = () => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context, overlays } = beans;\n  const [rowAnimationClass, setRowAnimationClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [topHeight, setTopHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [bottomHeight, setBottomHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [stickyTopHeight, setStickyTopHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyTopTop, setStickyTopTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyTopWidth, setStickyTopWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"100%\");\n  const [stickyBottomHeight, setStickyBottomHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyBottomBottom, setStickyBottomBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyBottomWidth, setStickyBottomWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"100%\");\n  const [topInvisible, setTopInvisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [bottomInvisible, setBottomInvisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [forceVerticalScrollClass, setForceVerticalScrollClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [topAndBottomOverflowY, setTopAndBottomOverflowY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [cellSelectableCss, setCellSelectableCss] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [layoutClass, setLayoutClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"ag-layout-normal\");\n  const cssManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssManager.current) {\n    cssManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eRoot.current);\n  }\n  const eRoot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eTop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eStickyTop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eStickyBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBody = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBodyViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const beansToDestroy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const destroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  reactComment_default(\" AG Grid Body \", eRoot);\n  reactComment_default(\" AG Pinned Top \", eTop);\n  reactComment_default(\" AG Sticky Top \", eStickyTop);\n  reactComment_default(\" AG Middle \", eBodyViewport);\n  reactComment_default(\" AG Pinned Bottom \", eBottom);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eRoot.current = eRef;\n    if (!eRef) {\n      beansToDestroy.current = context.destroyBeans(beansToDestroy.current);\n      destroyFuncs.current.forEach((f) => f());\n      destroyFuncs.current = [];\n      return;\n    }\n    if (!context) {\n      return;\n    }\n    const attachToDom = (eParent, eChild) => {\n      eParent.appendChild(eChild);\n      destroyFuncs.current.push(() => eParent.removeChild(eChild));\n    };\n    const newComp = (compClass) => {\n      const comp = context.createBean(new compClass());\n      beansToDestroy.current.push(comp);\n      return comp;\n    };\n    const addComp = (eParent, compClass, comment) => {\n      attachToDom(eParent, document.createComment(comment));\n      attachToDom(eParent, newComp(compClass).getGui());\n    };\n    addComp(eRef, ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.FakeHScrollComp, \" AG Fake Horizontal Scroll \");\n    const overlayComp = overlays?.getOverlayWrapperCompClass();\n    if (overlayComp) {\n      addComp(eRef, overlayComp, \" AG Overlay Wrapper \");\n    }\n    if (eBody.current) {\n      addComp(eBody.current, ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.FakeVScrollComp, \" AG Fake Vertical Scroll \");\n    }\n    const compProxy = {\n      setRowAnimationCssOnBodyViewport: setRowAnimationClass,\n      setColumnCount: (count) => {\n        if (eRoot.current) {\n          (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaColCount)(eRoot.current, count);\n        }\n      },\n      setRowCount: (count) => {\n        if (eRoot.current) {\n          (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaRowCount)(eRoot.current, count);\n        }\n      },\n      setTopHeight,\n      setBottomHeight,\n      setStickyTopHeight,\n      setStickyTopTop,\n      setStickyTopWidth,\n      setTopInvisible,\n      setBottomInvisible,\n      setColumnMovingCss: (cssClass, flag) => cssManager.current.toggleCss(cssClass, flag),\n      updateLayoutClasses: setLayoutClass,\n      setAlwaysVerticalScrollClass: setForceVerticalScrollClass,\n      setPinnedTopBottomOverflowY: setTopAndBottomOverflowY,\n      setCellSelectableCss: (cssClass, flag) => setCellSelectableCss(flag ? cssClass : null),\n      setBodyViewportWidth: (width) => {\n        if (eBodyViewport.current) {\n          eBodyViewport.current.style.width = width;\n        }\n      },\n      registerBodyViewportResizeListener: (listener) => {\n        if (eBodyViewport.current) {\n          const unsubscribeFromResize = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._observeResize)(beans, eBodyViewport.current, listener);\n          destroyFuncs.current.push(() => unsubscribeFromResize());\n        }\n      },\n      setStickyBottomHeight,\n      setStickyBottomBottom,\n      setStickyBottomWidth,\n      setGridRootRole: (role) => eRef.setAttribute(\"role\", role)\n    };\n    const ctrl = context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridBodyCtrl());\n    beansToDestroy.current.push(ctrl);\n    ctrl.setComp(\n      compProxy,\n      eRef,\n      eBodyViewport.current,\n      eTop.current,\n      eBottom.current,\n      eStickyTop.current,\n      eStickyBottom.current\n    );\n  }, []);\n  const rootClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-root\", \"ag-unselectable\", layoutClass), [layoutClass]);\n  const bodyViewportClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\n      \"ag-body-viewport\",\n      rowAnimationClass,\n      layoutClass,\n      forceVerticalScrollClass,\n      cellSelectableCss\n    ),\n    [rowAnimationClass, layoutClass, forceVerticalScrollClass, cellSelectableCss]\n  );\n  const bodyClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-body\", layoutClass), [layoutClass]);\n  const topClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-floating-top\", topInvisible ? \"ag-invisible\" : null, cellSelectableCss),\n    [cellSelectableCss, topInvisible]\n  );\n  const stickyTopClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-sticky-top\", cellSelectableCss), [cellSelectableCss]);\n  const stickyBottomClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-sticky-bottom\", stickyBottomHeight === \"0px\" ? \"ag-invisible\" : null, cellSelectableCss),\n    [cellSelectableCss, stickyBottomHeight]\n  );\n  const bottomClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-floating-bottom\", bottomInvisible ? \"ag-invisible\" : null, cellSelectableCss),\n    [cellSelectableCss, bottomInvisible]\n  );\n  const topStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: topHeight,\n      minHeight: topHeight,\n      overflowY: topAndBottomOverflowY\n    }),\n    [topHeight, topAndBottomOverflowY]\n  );\n  const stickyTopStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: stickyTopHeight,\n      top: stickyTopTop,\n      width: stickyTopWidth\n    }),\n    [stickyTopHeight, stickyTopTop, stickyTopWidth]\n  );\n  const stickyBottomStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: stickyBottomHeight,\n      bottom: stickyBottomBottom,\n      width: stickyBottomWidth\n    }),\n    [stickyBottomHeight, stickyBottomBottom, stickyBottomWidth]\n  );\n  const bottomStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: bottomHeight,\n      minHeight: bottomHeight,\n      overflowY: topAndBottomOverflowY\n    }),\n    [bottomHeight, topAndBottomOverflowY]\n  );\n  const createRowContainer = (container) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowContainerComp_default, { name: container, key: `${container}-container` });\n  const createSection = ({\n    section,\n    children,\n    className,\n    style\n  }) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: section, className, role: \"presentation\", style }, children.map(createRowContainer));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: rootClasses }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridHeaderComp_default, null), createSection({\n    section: eTop,\n    className: topClasses,\n    style: topStyle,\n    children: [\"topLeft\", \"topCenter\", \"topRight\", \"topFullWidth\"]\n  }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: bodyClasses, ref: eBody, role: \"presentation\" }, createSection({\n    section: eBodyViewport,\n    className: bodyViewportClasses,\n    children: [\"left\", \"center\", \"right\", \"fullWidth\"]\n  })), createSection({\n    section: eStickyTop,\n    className: stickyTopClasses,\n    style: stickyTopStyle,\n    children: [\"stickyTopLeft\", \"stickyTopCenter\", \"stickyTopRight\", \"stickyTopFullWidth\"]\n  }), createSection({\n    section: eStickyBottom,\n    className: stickyBottomClasses,\n    style: stickyBottomStyle,\n    children: [\"stickyBottomLeft\", \"stickyBottomCenter\", \"stickyBottomRight\", \"stickyBottomFullWidth\"]\n  }), createSection({\n    section: eBottom,\n    className: bottomClasses,\n    style: bottomStyle,\n    children: [\"bottomLeft\", \"bottomCenter\", \"bottomRight\", \"bottomFullWidth\"]\n  }));\n};\nvar gridBodyComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridBodyComp);\n\n// packages/ag-grid-react/src/reactUi/tabGuardComp.tsx\n\n\nvar TabGuardCompRef = (props, forwardRef4) => {\n  const { children, eFocusableElement, onTabKeyDown, gridCtrl, forceFocusOutWhenTabGuardsAreEmpty, isEmpty } = props;\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const topTabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const bottomTabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tabGuardCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setTabIndex = (value) => {\n    const processedValue = value == null ? void 0 : parseInt(value, 10).toString();\n    [topTabGuardRef, bottomTabGuardRef].forEach((tabGuard) => {\n      if (processedValue === void 0) {\n        tabGuard.current?.removeAttribute(\"tabindex\");\n      } else {\n        tabGuard.current?.setAttribute(\"tabindex\", processedValue);\n      }\n    });\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardRef4, () => ({\n    forceFocusOutOfContainer(up) {\n      tabGuardCtrlRef.current?.forceFocusOutOfContainer(up);\n    }\n  }));\n  const setupCtrl = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const topTabGuard = topTabGuardRef.current;\n    const bottomTabGuard = bottomTabGuardRef.current;\n    if (!topTabGuard && !bottomTabGuard) {\n      tabGuardCtrlRef.current = context.destroyBean(tabGuardCtrlRef.current);\n      return;\n    }\n    if (topTabGuard && bottomTabGuard) {\n      const compProxy = {\n        setTabIndex\n      };\n      tabGuardCtrlRef.current = context.createBean(\n        new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardCtrl({\n          comp: compProxy,\n          eTopGuard: topTabGuard,\n          eBottomGuard: bottomTabGuard,\n          eFocusableElement,\n          onTabKeyDown,\n          forceFocusOutWhenTabGuardsAreEmpty,\n          focusInnerElement: (fromBottom) => gridCtrl.focusInnerElement(fromBottom),\n          isEmpty\n        })\n      );\n    }\n  }, []);\n  const setTopRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      topTabGuardRef.current = e;\n      setupCtrl();\n    },\n    [setupCtrl]\n  );\n  const setBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      bottomTabGuardRef.current = e;\n      setupCtrl();\n    },\n    [setupCtrl]\n  );\n  const createTabGuard = (side) => {\n    const className = side === \"top\" ? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD_TOP : ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD_BOTTOM;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"div\",\n      {\n        className: `${ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD} ${className}`,\n        role: \"presentation\",\n        ref: side === \"top\" ? setTopRef : setBottomRef\n      }\n    );\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, createTabGuard(\"top\"), children, createTabGuard(\"bottom\"));\n};\nvar TabGuardComp = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(TabGuardCompRef);\nvar tabGuardComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(TabGuardComp);\n\n// packages/ag-grid-react/src/reactUi/gridComp.tsx\nvar GridComp = ({ context }) => {\n  const [rtlClass, setRtlClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [layoutClass, setLayoutClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [cursor, setCursor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [userSelect, setUserSelect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [initialised, setInitialised] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [tabGuardReady, setTabGuardReady] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const gridCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eRootWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [eGridBodyParent, setGridBodyParent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const focusInnerElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => void 0);\n  const paginationCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const focusableContainersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const onTabKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => void 0, []);\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (context.isDestroyed()) {\n      return null;\n    }\n    return context.getBeans();\n  }, [context]);\n  reactComment_default(\" AG Grid \", eRootWrapperRef);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eRootWrapperRef.current = eRef;\n    gridCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridCtrl()) : context.destroyBean(gridCtrlRef.current);\n    if (!eRef || context.isDestroyed()) {\n      return;\n    }\n    const gridCtrl = gridCtrlRef.current;\n    focusInnerElementRef.current = gridCtrl.focusInnerElement.bind(gridCtrl);\n    const compProxy = {\n      destroyGridUi: () => {\n      },\n      // do nothing, as framework users destroy grid by removing the comp\n      setRtlClass,\n      forceFocusOutOfContainer: (up) => {\n        if (!up && paginationCompRef.current?.isDisplayed()) {\n          paginationCompRef.current.forceFocusOutOfContainer(up);\n          return;\n        }\n        tabGuardRef.current?.forceFocusOutOfContainer(up);\n      },\n      updateLayoutClasses: setLayoutClass,\n      getFocusableContainers: () => {\n        const comps = [];\n        const gridBodyCompEl = eRootWrapperRef.current?.querySelector(\".ag-root\");\n        if (gridBodyCompEl) {\n          comps.push({ getGui: () => gridBodyCompEl });\n        }\n        focusableContainersRef.current.forEach((comp) => {\n          if (comp.isDisplayed()) {\n            comps.push(comp);\n          }\n        });\n        return comps;\n      },\n      setCursor,\n      setUserSelect\n    };\n    gridCtrl.setComp(compProxy, eRef, eRef);\n    setInitialised(true);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const gridCtrl = gridCtrlRef.current;\n    const eRootWrapper = eRootWrapperRef.current;\n    if (!tabGuardReady || !beans || !gridCtrl || !eGridBodyParent || !eRootWrapper || context.isDestroyed()) {\n      return;\n    }\n    const beansToDestroy = [];\n    const {\n      watermarkSelector,\n      paginationSelector,\n      sideBarSelector,\n      statusBarSelector,\n      gridHeaderDropZonesSelector\n    } = gridCtrl.getOptionalSelectors();\n    const additionalEls = [];\n    if (gridHeaderDropZonesSelector) {\n      const headerDropZonesComp = context.createBean(new gridHeaderDropZonesSelector.component());\n      const eGui = headerDropZonesComp.getGui();\n      eRootWrapper.insertAdjacentElement(\"afterbegin\", eGui);\n      additionalEls.push(eGui);\n      beansToDestroy.push(headerDropZonesComp);\n    }\n    if (sideBarSelector) {\n      const sideBarComp = context.createBean(new sideBarSelector.component());\n      const eGui = sideBarComp.getGui();\n      const bottomTabGuard = eGridBodyParent.querySelector(\".ag-tab-guard-bottom\");\n      if (bottomTabGuard) {\n        bottomTabGuard.insertAdjacentElement(\"beforebegin\", eGui);\n        additionalEls.push(eGui);\n      }\n      beansToDestroy.push(sideBarComp);\n      focusableContainersRef.current.push(sideBarComp);\n    }\n    const addComponentToDom = (component) => {\n      const comp = context.createBean(new component());\n      const eGui = comp.getGui();\n      eRootWrapper.insertAdjacentElement(\"beforeend\", eGui);\n      additionalEls.push(eGui);\n      beansToDestroy.push(comp);\n      return comp;\n    };\n    if (statusBarSelector) {\n      addComponentToDom(statusBarSelector.component);\n    }\n    if (paginationSelector) {\n      const paginationComp = addComponentToDom(paginationSelector.component);\n      paginationCompRef.current = paginationComp;\n      focusableContainersRef.current.push(paginationComp);\n    }\n    if (watermarkSelector) {\n      addComponentToDom(watermarkSelector.component);\n    }\n    return () => {\n      context.destroyBeans(beansToDestroy);\n      additionalEls.forEach((el) => {\n        el.parentElement?.removeChild(el);\n      });\n    };\n  }, [tabGuardReady, eGridBodyParent, beans]);\n  const rootWrapperClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-root-wrapper\", rtlClass, layoutClass),\n    [rtlClass, layoutClass]\n  );\n  const rootWrapperBodyClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-root-wrapper-body\", \"ag-focus-managed\", layoutClass),\n    [layoutClass]\n  );\n  const topStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      userSelect: userSelect != null ? userSelect : \"\",\n      WebkitUserSelect: userSelect != null ? userSelect : \"\",\n      cursor: cursor != null ? cursor : \"\"\n    }),\n    [userSelect, cursor]\n  );\n  const setTabGuardCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    tabGuardRef.current = ref;\n    setTabGuardReady(ref !== null);\n  }, []);\n  const isFocusable = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => !gridCtrlRef.current?.isFocusable(), []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: rootWrapperClasses, style: topStyle, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: rootWrapperBodyClasses, ref: setGridBodyParent, role: \"presentation\" }, initialised && eGridBodyParent && beans && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(BeansContext.Provider, { value: beans }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    tabGuardComp_default,\n    {\n      ref: setTabGuardCompRef,\n      eFocusableElement: eGridBodyParent,\n      onTabKeyDown,\n      gridCtrl: gridCtrlRef.current,\n      forceFocusOutWhenTabGuardsAreEmpty: true,\n      isEmpty: isFocusable\n    },\n    // we wait for initialised before rending the children, so GridComp has created and registered with it's\n    // GridCtrl before we create the child GridBodyComp. Otherwise the GridBodyComp would initialise first,\n    // before we have set the the Layout CSS classes, causing the GridBodyComp to render rows to a grid that\n    // doesn't have it's height specified, which would result if all the rows getting rendered (and if many rows,\n    // hangs the UI)\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridBodyComp_default, null)\n  ))));\n};\nvar gridComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridComp);\n\n// packages/ag-grid-react/src/reactUi/renderStatusService.tsx\n\nvar RenderStatusService = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.BeanStub {\n  wireBeans(beans) {\n    this.ctrlsSvc = beans.ctrlsSvc;\n  }\n  areHeaderCellsRendered() {\n    return this.ctrlsSvc.getHeaderRowContainerCtrls().every((container) => container.getAllCtrls().every((ctrl) => ctrl.areCellsRendered()));\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/agGridReactUi.tsx\nvar deprecatedProps = {\n  setGridApi: void 0,\n  maxComponentCreationTimeMs: void 0,\n  children: void 0\n};\nvar reactPropsNotGridOptions = {\n  gridOptions: void 0,\n  modules: void 0,\n  containerStyle: void 0,\n  className: void 0,\n  passGridApi: void 0,\n  componentWrappingElement: void 0,\n  ...deprecatedProps\n};\nvar excludeReactCompProps = new Set(Object.keys(reactPropsNotGridOptions));\nvar deprecatedReactCompProps = new Set(Object.keys(deprecatedProps));\nvar AgGridReactUi = (props) => {\n  const apiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const portalManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const destroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const whenReadyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n  const frameworkOverridesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const gridIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const ready = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [context, setContext] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [, setPortalRefresher] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    if (!eRef) {\n      destroyFuncs.current.forEach((f) => f());\n      destroyFuncs.current.length = 0;\n      return;\n    }\n    const modules = props.modules || [];\n    if (!portalManager.current) {\n      portalManager.current = new PortalManager(\n        () => setPortalRefresher((prev) => prev + 1),\n        props.componentWrappingElement,\n        props.maxComponentCreationTimeMs\n      );\n      destroyFuncs.current.push(() => {\n        portalManager.current?.destroy();\n        portalManager.current = null;\n      });\n    }\n    const mergedGridOps = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._combineAttributesAndGridOptions)(\n      props.gridOptions,\n      props,\n      Object.keys(props).filter((key) => !excludeReactCompProps.has(key))\n    );\n    const processQueuedUpdates = () => {\n      if (ready.current) {\n        const getFn = () => frameworkOverridesRef.current?.shouldQueueUpdates() ? void 0 : whenReadyFuncs.current.shift();\n        let fn = getFn();\n        while (fn) {\n          fn();\n          fn = getFn();\n        }\n      }\n    };\n    const frameworkOverrides = new ReactFrameworkOverrides(processQueuedUpdates);\n    frameworkOverridesRef.current = frameworkOverrides;\n    const renderStatus = new RenderStatusService();\n    const gridParams = {\n      providedBeanInstances: {\n        frameworkCompWrapper: new ReactFrameworkComponentWrapper(portalManager.current, mergedGridOps),\n        renderStatus\n      },\n      modules,\n      frameworkOverrides,\n      setThemeOnGridDiv: true\n    };\n    const createUiCallback = (context2) => {\n      setContext(context2);\n      context2.createBean(renderStatus);\n      destroyFuncs.current.push(() => {\n        context2.destroy();\n      });\n      context2.getBean(\"ctrlsSvc\").whenReady(\n        {\n          addDestroyFunc: (func) => {\n            destroyFuncs.current.push(func);\n          }\n        },\n        () => {\n          if (context2.isDestroyed()) {\n            return;\n          }\n          const api = apiRef.current;\n          if (api) {\n            props.passGridApi?.(api);\n          }\n        }\n      );\n    };\n    const acceptChangesCallback = (context2) => {\n      context2.getBean(\"ctrlsSvc\").whenReady(\n        {\n          addDestroyFunc: (func) => {\n            destroyFuncs.current.push(func);\n          }\n        },\n        () => {\n          whenReadyFuncs.current.forEach((f) => f());\n          whenReadyFuncs.current.length = 0;\n          ready.current = true;\n        }\n      );\n    };\n    const gridCoreCreator = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridCoreCreator();\n    mergedGridOps.gridId ?? (mergedGridOps.gridId = gridIdRef.current);\n    apiRef.current = gridCoreCreator.create(\n      eRef,\n      mergedGridOps,\n      createUiCallback,\n      acceptChangesCallback,\n      gridParams\n    );\n    destroyFuncs.current.push(() => {\n      apiRef.current = void 0;\n    });\n    if (apiRef.current) {\n      gridIdRef.current = apiRef.current.getGridId();\n    }\n  }, []);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return {\n      height: \"100%\",\n      ...props.containerStyle || {}\n    };\n  }, [props.containerStyle]);\n  const processWhenReady = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((func) => {\n    if (ready.current && !frameworkOverridesRef.current?.shouldQueueUpdates()) {\n      func();\n    } else {\n      whenReadyFuncs.current.push(func);\n    }\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const changes = extractGridPropertyChanges(prevProps.current, props);\n    prevProps.current = props;\n    processWhenReady(() => {\n      if (apiRef.current) {\n        (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._processOnChange)(changes, apiRef.current);\n      }\n    });\n  }, [props]);\n  const renderMode = !react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore || (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridOption)(props, \"renderingMode\") === \"legacy\" ? \"legacy\" : \"default\";\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style, className: props.className, ref: setRef2 }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(RenderModeContext.Provider, { value: renderMode }, context && !context.isDestroyed() ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridComp_default, { context }) : null, portalManager.current?.getPortals() ?? null));\n};\nfunction extractGridPropertyChanges(prevProps, nextProps) {\n  const changes = {};\n  Object.keys(nextProps).forEach((propKey) => {\n    if (excludeReactCompProps.has(propKey)) {\n      if (deprecatedReactCompProps.has(propKey)) {\n        (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._warn)(274, { prop: propKey });\n      }\n      return;\n    }\n    const propValue = nextProps[propKey];\n    if (prevProps[propKey] !== propValue) {\n      changes[propKey] = propValue;\n    }\n  });\n  return changes;\n}\nvar ReactFrameworkComponentWrapper = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.BaseComponentWrapper {\n  constructor(parent, gridOptions) {\n    super();\n    this.parent = parent;\n    this.gridOptions = gridOptions;\n  }\n  createWrapper(UserReactComponent, componentType) {\n    const gridOptions = this.gridOptions;\n    const reactiveCustomComponents = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridOption)(gridOptions, \"reactiveCustomComponents\");\n    if (reactiveCustomComponents) {\n      const getComponentClass = (propertyName) => {\n        switch (propertyName) {\n          case \"filter\":\n            return (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridOption)(gridOptions, \"enableFilterHandlers\") ? FilterDisplayComponentWrapper : FilterComponentWrapper;\n          case \"floatingFilterComponent\":\n            return (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridOption)(gridOptions, \"enableFilterHandlers\") ? FloatingFilterDisplayComponentWrapper : FloatingFilterComponentWrapper;\n          case \"dateComponent\":\n            return DateComponentWrapper;\n          case \"dragAndDropImageComponent\":\n            return DragAndDropImageComponentWrapper;\n          case \"loadingOverlayComponent\":\n            return LoadingOverlayComponentWrapper;\n          case \"noRowsOverlayComponent\":\n            return NoRowsOverlayComponentWrapper;\n          case \"statusPanel\":\n            return StatusPanelComponentWrapper;\n          case \"toolPanel\":\n            return ToolPanelComponentWrapper;\n          case \"menuItem\":\n            return MenuItemComponentWrapper;\n          case \"cellRenderer\":\n            return CellRendererComponentWrapper;\n          case \"innerHeaderComponent\":\n            return InnerHeaderComponentWrapper;\n        }\n      };\n      const ComponentClass = getComponentClass(componentType.name);\n      if (ComponentClass) {\n        return new ComponentClass(UserReactComponent, this.parent, componentType);\n      }\n    } else {\n      switch (componentType.name) {\n        case \"filter\":\n        case \"floatingFilterComponent\":\n        case \"dateComponent\":\n        case \"dragAndDropImageComponent\":\n        case \"loadingOverlayComponent\":\n        case \"noRowsOverlayComponent\":\n        case \"statusPanel\":\n        case \"toolPanel\":\n        case \"menuItem\":\n        case \"cellRenderer\":\n          warnReactiveCustomComponents();\n          break;\n      }\n    }\n    const suppressFallbackMethods = !componentType.cellRenderer && componentType.name !== \"toolPanel\";\n    return new ReactComponent(UserReactComponent, this.parent, componentType, suppressFallbackMethods);\n  }\n};\nvar DetailCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { registry, context, gos, rowModel } = beans;\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [gridCssClasses, setGridCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [detailGridOptions, setDetailGridOptions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [detailRowData, setDetailRowData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const ctrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGuiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const resizeObserverDestroyFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const parentModules = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridRegisteredModules)(props.api.getGridId(), detailGridOptions?.rowModelType ?? \"clientSide\"),\n    [props]\n  );\n  const topClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssClasses.toString() + \" ag-details-row\", [cssClasses]);\n  const gridClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gridCssClasses.toString() + \" ag-details-grid\", [gridCssClasses]);\n  if (ref) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => ({\n      refresh() {\n        return ctrlRef.current?.refresh() ?? false;\n      }\n    }));\n  }\n  if (props.template) {\n    (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._warn)(230);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGuiRef.current = eRef;\n    if (!eRef) {\n      ctrlRef.current = context.destroyBean(ctrlRef.current);\n      resizeObserverDestroyFunc.current?.();\n      return;\n    }\n    const compProxy = {\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      toggleDetailGridCss: (name, on) => setGridCssClasses((prev) => prev.setClass(name, on)),\n      setDetailGrid: (gridOptions) => setDetailGridOptions(gridOptions),\n      setRowData: (rowData) => setDetailRowData(rowData),\n      getGui: () => eGuiRef.current\n    };\n    const ctrl = registry.createDynamicBean(\"detailCellRendererCtrl\", true);\n    if (!ctrl) {\n      return;\n    }\n    context.createBean(ctrl);\n    ctrl.init(compProxy, props);\n    ctrlRef.current = ctrl;\n    if (gos.get(\"detailRowAutoHeight\")) {\n      const checkRowSizeFunc = () => {\n        if (eGuiRef.current == null) {\n          return;\n        }\n        const clientHeight = eGuiRef.current.clientHeight;\n        if (clientHeight != null && clientHeight > 0) {\n          const updateRowHeightFunc = () => {\n            props.node.setRowHeight(clientHeight);\n            if ((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._isClientSideRowModel)(gos, rowModel) || (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._isServerSideRowModel)(gos, rowModel)) {\n              rowModel.onRowHeightChanged();\n            }\n          };\n          setTimeout(updateRowHeightFunc, 0);\n        }\n      };\n      resizeObserverDestroyFunc.current = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._observeResize)(beans, eRef, checkRowSizeFunc);\n      checkRowSizeFunc();\n    }\n  }, []);\n  const registerGridApi = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((api) => {\n    ctrlRef.current?.registerDetailWithMaster(api);\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: topClassName, ref: setRef2 }, detailGridOptions && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    AgGridReactUi,\n    {\n      className: gridClassName,\n      ...detailGridOptions,\n      modules: parentModules,\n      rowData: detailRowData,\n      passGridApi: registerGridApi\n    }\n  ));\n});\nvar ReactFrameworkOverrides = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.VanillaFrameworkOverrides {\n  constructor(processQueuedUpdates) {\n    super(\"react\");\n    this.processQueuedUpdates = processQueuedUpdates;\n    this.queueUpdates = false;\n    this.renderingEngine = \"react\";\n    this.frameworkComponents = {\n      agGroupCellRenderer: groupCellRenderer_default,\n      agGroupRowRenderer: groupCellRenderer_default,\n      agDetailCellRenderer: DetailCellRenderer\n    };\n    this.wrapIncoming = (callback, source) => {\n      if (source === \"ensureVisible\") {\n        return runWithoutFlushSync(callback);\n      }\n      return callback();\n    };\n  }\n  frameworkComponent(name) {\n    return this.frameworkComponents[name];\n  }\n  isFrameworkComponent(comp) {\n    if (!comp) {\n      return false;\n    }\n    const prototype = comp.prototype;\n    const isJsComp = prototype && \"getGui\" in prototype;\n    return !isJsComp;\n  }\n  getLockOnRefresh() {\n    this.queueUpdates = true;\n  }\n  releaseLockOnRefresh() {\n    this.queueUpdates = false;\n    this.processQueuedUpdates();\n  }\n  shouldQueueUpdates() {\n    return this.queueUpdates;\n  }\n  runWhenReadyAsync() {\n    return isReact19();\n  }\n};\n\n// packages/ag-grid-react/src/agGridReact.tsx\nvar AgGridReact = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor() {\n    super(...arguments);\n    this.apiListeners = [];\n    this.setGridApi = (api) => {\n      this.api = api;\n      this.apiListeners.forEach((listener) => listener(api));\n    };\n  }\n  registerApiListener(listener) {\n    this.apiListeners.push(listener);\n  }\n  componentWillUnmount() {\n    this.apiListeners.length = 0;\n  }\n  render() {\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(AgGridReactUi, { ...this.props, passGridApi: this.setGridApi });\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/interfaces.ts\n\nfunction useGridCustomComponent(methods) {\n  const { setMethods } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(CustomContext);\n  setMethods(methods);\n}\nfunction useGridCellEditor(callbacks) {\n  useGridCustomComponent(callbacks);\n}\nfunction useGridDate(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFilter(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFilterDisplay(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFloatingFilter(callbacks) {\n  useGridCustomComponent(callbacks);\n}\nfunction useGridMenuItem(callbacks) {\n  useGridCustomComponent(callbacks);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ag-grid-react/dist/package/index.esm.mjs\n");

/***/ })

};
;