"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTicketsAnalyticsOverview = void 0;
const helpers_1 = require("../../../../utils/helpers");
const getTicketsAnalyticsOverview = async (req, res) => {
    try {
        const parseLocalDate = (s) => {
            const [y, m, d] = s.split("-").map((v) => parseInt(v, 10));
            return new Date(y, (m || 1) - 1, d || 1);
        };
        const toStartOfDay = (d) => {
            const x = new Date(d);
            x.setHours(0, 0, 0, 0);
            return x;
        };
        const toEndOfDay = (d) => {
            const x = new Date(d);
            x.setHours(23, 59, 59, 999);
            return x;
        };
        const { dateFrom, dateTo, tags, assignedTo, stageId, priority, dueDateFrom, dueDateTo, } = req.query;
        const whereClause = {
            deletedAt: null,
        };
        if ((dateFrom || dateTo) && !(dueDateFrom || dueDateTo)) {
            whereClause.createdAt = {};
            if (dateFrom) {
                whereClause.createdAt.gte = toStartOfDay(parseLocalDate(dateFrom));
            }
            if (dateTo) {
                whereClause.createdAt.lte = toEndOfDay(parseLocalDate(dateTo));
            }
        }
        if (tags) {
            const tagArray = tags.split(',').map((tag) => tag.trim());
            whereClause.tags = {
                hasSome: tagArray,
            };
        }
        if (priority) {
            whereClause.priority = priority;
        }
        const stageWhereClause = {};
        if (assignedTo) {
            stageWhereClause.assignedTo = assignedTo;
        }
        if (stageId) {
            stageWhereClause.pipelineStageId = stageId;
        }
        const ticketsWithStages = await prisma.ticket.findMany({
            where: {
                ...whereClause,
                ...(Object.keys(stageWhereClause).length > 0 && {
                    stages: {
                        some: stageWhereClause,
                    },
                }),
            },
            include: {
                pipeline: {
                    include: {
                        stages: {
                            orderBy: { order: 'asc' },
                        },
                    },
                },
                stages: {
                    include: {
                        pipelineStage: true,
                    },
                    orderBy: {
                        createdAt: 'asc',
                    },
                },
            },
        });
        let filteredTickets = ticketsWithStages;
        if (dueDateFrom || dueDateTo) {
            const fromDate = dueDateFrom ? toStartOfDay(parseLocalDate(dueDateFrom)) : null;
            const toDate = dueDateTo ? toEndOfDay(parseLocalDate(dueDateTo)) : null;
            filteredTickets = ticketsWithStages.filter((ticket) => {
                if (!ticket.currentStageId || !Array.isArray(ticket.stages) || ticket.stages.length === 0)
                    return false;
                const currentStage = ticket.stages.find((s) => String(s.pipelineStageId) === String(ticket.currentStageId));
                if (!currentStage)
                    return false;
                const dueAt = currentStage.dueAt ? new Date(currentStage.dueAt) : null;
                if (!dueAt)
                    return false;
                if (fromDate && dueAt < fromDate)
                    return false;
                if (toDate && dueAt > toDate)
                    return false;
                return true;
            });
        }
        const totalTickets = filteredTickets.length;
        let closedTickets = 0;
        let totalResolutionTime = 0;
        let resolvedTicketsCount = 0;
        for (const ticket of filteredTickets) {
            if (ticket.pipeline?.stages?.length > 0 &&
                ticket.stages?.length > 0 &&
                ticket.currentStageId) {
                const finalStage = ticket.pipeline.stages[ticket.pipeline.stages.length - 1];
                const currentStage = ticket.stages.find((stage) => String(stage.pipelineStageId) === String(ticket.currentStageId));
                if (currentStage && ticket.currentStageId === finalStage.id) {
                    closedTickets++;
                    const createdAt = new Date(ticket.createdAt);
                    const closedAt = new Date(currentStage.createdAt);
                    const resolutionTimeHours = (closedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
                    totalResolutionTime += resolutionTimeHours;
                    resolvedTicketsCount++;
                }
            }
        }
        const openTickets = totalTickets - closedTickets;
        const closureRate = totalTickets > 0 ? (closedTickets / totalTickets) * 100 : 0;
        const averageTimeToClose = resolvedTicketsCount > 0 ? totalResolutionTime / resolvedTicketsCount : 0;
        let ticketsCreatedInPeriod = totalTickets;
        if (dateFrom || dateTo) {
            const fromDate = dateFrom ? toStartOfDay(parseLocalDate(dateFrom)) : null;
            const toDate = dateTo ? toEndOfDay(parseLocalDate(dateTo)) : null;
            ticketsCreatedInPeriod = filteredTickets.filter((t) => {
                const createdAt = new Date(t.createdAt);
                if (fromDate && createdAt < fromDate)
                    return false;
                if (toDate && createdAt > toDate)
                    return false;
                return true;
            }).length;
        }
        let ticketsClosedInPeriod = 0;
        if (dateFrom || dateTo) {
            const allClosedTickets = filteredTickets.filter((ticket) => {
                if (ticket.pipeline?.stages?.length > 0 &&
                    ticket.stages?.length > 0 &&
                    ticket.currentStageId) {
                    const finalStage = ticket.pipeline.stages[ticket.pipeline.stages.length - 1];
                    const currentStage = ticket.stages.find((stage) => String(stage.pipelineStageId) === String(ticket.currentStageId));
                    if (currentStage && ticket.currentStageId === finalStage.id) {
                        const closedAt = new Date(currentStage.createdAt);
                        if (dateFrom && dateTo) {
                            return (closedAt >= toStartOfDay(parseLocalDate(dateFrom)) &&
                                closedAt <= toEndOfDay(parseLocalDate(dateTo)));
                        }
                        else if (dateFrom) {
                            return closedAt >= toStartOfDay(parseLocalDate(dateFrom));
                        }
                        else if (dateTo) {
                            return closedAt <= toEndOfDay(parseLocalDate(dateTo));
                        }
                        return true;
                    }
                }
                return false;
            });
            ticketsClosedInPeriod = allClosedTickets.length;
        }
        else {
            ticketsClosedInPeriod = closedTickets;
        }
        return res.status(200).json({
            success: true,
            data: {
                totalTickets,
                closedTickets,
                openTickets,
                averageTimeToClose: Math.round(averageTimeToClose * 100) / 100,
                averageTimeToCloseUnit: "hours",
                closureRate: Math.round(closureRate * 100) / 100,
                ticketsCreatedInPeriod,
                ticketsClosedInPeriod,
            },
        });
    }
    catch (error) {
        console.error("Error in getTicketsAnalyticsOverview:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getTicketsAnalyticsOverview = getTicketsAnalyticsOverview;
//# sourceMappingURL=overview.js.map