"use client";
import React from "react";
import {
  Dnd<PERSON>ontext,
  Pointer<PERSON>ensor,
  useSensor,
  useSensors,
  DragEndEvent,
  CollisionDetection,
  pointerWithin,
  rectIntersection,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  rectSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { TouchSensor } from "@dnd-kit/core";
import { CustomField } from "./page";

interface CustomFieldsArrangementProps {
  reorderedFields: CustomField[];
  setReorderedFields: React.Dispatch<React.SetStateAction<CustomField[]>>;
  originalFields: CustomField[];
  selectedFieldIds: Set<string>;
  setSelectedFieldIds: React.Dispatch<React.SetStateAction<Set<string>>>;
  hasReordered: boolean;
  setHasReordered: React.Dispatch<React.SetStateAction<boolean>>;
}

const SortableItem = ({
  id,
  displayName,
  fieldtype,
  isSelected,
  onSelect,
}: {
  id: string;
  displayName: string;
  fieldtype?: string;
  isSelected: boolean;
  onSelect: (id: string) => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
    data: {
      type: "custom-field",
      id,
    },
  });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 1,
  };

  const displayedFieldtype = (() => {
    if (!fieldtype) return "Text";

    if (fieldtype.startsWith("auto-")) {
      const autoOption = fieldtype.replace("auto-", "");
      switch (autoOption.toLowerCase()) {
        case "username":
          return "Auto - Username";
        case "date":
          return "Auto - Date";
        case "xyz":
          return "Auto - XYZ";
        default:
          return `Auto - ${
            autoOption.charAt(0).toUpperCase() +
            autoOption.slice(1).toLowerCase()
          }`;
      }
    }

    switch (fieldtype.toLowerCase()) {
      case "text":
        return "Text";
      case "number":
        return "Number";
      case "date":
        return "Date";
      case "auto":
        return "Auto";
      default:
        return (
          fieldtype.charAt(0).toUpperCase() + fieldtype.slice(1).toLowerCase()
        );
    }
  })();

  return (
    <div
      ref={setNodeRef}
      style={{ ...style, touchAction: "none" }}
      {...attributes}
      {...listeners}
      className={`
        flex flex-col cursor-grab active:cursor-grabbing select-none
        transition-all duration-200 group
        ${isDragging ? "scale-105" : "hover:scale-[1.02]"}
      `}
      onClick={() => onSelect(id)}
    >
      <div className="mb-1 px-1">
        <h3 className="text-gray-900 font-bold text-sm leading-tight truncate">
          {displayName}
        </h3>
      </div>

      <div
        className={`
          relative p-2 rounded-lg flex items-center justify-start
          min-h-[30px] max-w-[200px] border border-gray-300 shadow-sm
          transition-all duration-200
          ${
            isSelected
              ? "border-gray-500 shadow-md"
              : "group-hover:border-gray-400 group-hover:shadow-md"
          }
          ${isDragging ? "shadow-xl border-gray-500" : ""}
        `}
        style={{ backgroundColor: "#e5e7eb" }}
      >
        <span className="text-gray-600 text-sm font-medium text-left truncate">
          {displayedFieldtype}
        </span>

        {isSelected && (
          <div className="absolute top-2 right-2">
            <div className="w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center">
              <svg
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M20 6L9 17L4 12"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export const CustomFieldsArrangement: React.FC<CustomFieldsArrangementProps> = ({
  reorderedFields,
  setReorderedFields,
  originalFields,
  selectedFieldIds,
  setSelectedFieldIds,
  hasReordered,
  setHasReordered,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const customFieldsCollisionDetection: CollisionDetection = (args) => {
    const { active, droppableContainers } = args;
    const activeData = active.data.current;

    if (activeData?.type !== "custom-field") {
      return [];
    }

    const validDroppables = Array.from(droppableContainers.values()).filter((container) => {
      const containerData = container.data.current;
      return containerData?.type === "custom-field";
    });

    const pointerCollisions = pointerWithin({
      ...args,
      droppableContainers: validDroppables,
    });

    if (pointerCollisions.length > 0) {
      return pointerCollisions;
    }

    const rectCollisions = rectIntersection({
      ...args,
      droppableContainers: validDroppables,
    });

    return rectCollisions;
  };

  const handleCustomFieldsDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over) {
      const activeData = active.data.current;
      const overData = over.data.current;

      if (
        activeData?.type === "custom-field" &&
        overData?.type === "custom-field"
      ) {
        const oldIndex = reorderedFields.findIndex(
          (item) => item.id === active.id
        );
        const newIndex = reorderedFields.findIndex(
          (item) => item.id === over.id
        );

        if (oldIndex !== -1 && newIndex !== -1) {
          setReorderedFields((items) => arrayMove(items, oldIndex, newIndex));
          setHasReordered(true);
        }
      }
    } else {
      console.log("Custom field dragged outside valid drop area - ignoring");
    }
  };

  const toggleSelectField = (id: string) => {
    setSelectedFieldIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) newSet.delete(id);
      else newSet.add(id);
      return newSet;
    });
  };

  const handleReset = () => {
    setReorderedFields(originalFields);
    setHasReordered(false);
    setSelectedFieldIds(new Set());
  };

  return (
    <div className="space-y-4">
      <div className="text-center relative">
        <h2 className="text-xl font-bold text-gray-900 mb-2">
          Custom Fields
        </h2>
        <p className="text-sm text-gray-600">
          Drag and drop to reorder these client-specific fields
        </p>
      </div>

      {reorderedFields.length === 0 && (
        <div className="bg-gradient-to-br from-slate-50 to-gray-100 rounded-3xl p-8 border-2 border-dashed border-gray-300 text-center">
          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-gray-400"
            >
              <path
                d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L19.7071 9.70711C19.8946 9.89464 20 10.149 20 10.4142V19C20 20.1046 19.1046 21 18 21H17Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            No Custom Fields Found
          </h3>
          <p className="text-gray-600 text-sm">
            This client doesn't have any custom fields to arrange yet
          </p>
        </div>
      )}

      {reorderedFields.length > 0 && (
        <div>
          <DndContext
            sensors={sensors}
            collisionDetection={customFieldsCollisionDetection}
            onDragEnd={handleCustomFieldsDragEnd}
          >
            <SortableContext
              items={reorderedFields.map((item) => item.id)}
              strategy={rectSortingStrategy}
            >
              <div className="bg-gradient-to-br from-white via-slate-50 to-gray-50 rounded-3xl border-2 border-dashed border-gray-300 p-4 shadow-inner">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {reorderedFields.map((field, index) => (
                    <div
                      key={field.id}
                      className="transform transition-all duration-200"
                      style={{
                        animationDelay: `${index * 50}ms`,
                      }}
                    >
                      <SortableItem
                        id={field.id}
                        displayName={field.displayName}
                        fieldtype={field.fieldtype}
                        isSelected={selectedFieldIds.has(field.id)}
                        onSelect={toggleSelectField}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </SortableContext>
          </DndContext>
          {hasReordered && (
            <div className="flex justify-center mt-3">
              <button
                onClick={handleReset}
                disabled={!hasReordered}
                className="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200 hover:scale-105"
              >
                Reset Custom Fields
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
