"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTicket = void 0;
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const deleteTicket = async (req, res) => {
    const id = req.params.id;
    const { deletedBy } = req.body;
    // Use frontend username or fallback to 'system'
    const username = deletedBy || "system";
    try {
        const existingRecord = await prismaClient_1.default.ticket.findUnique({
            where: { id: id },
        });
        if (!existingRecord) {
            return res
                .status(400)
                .json({ success: false, message: "Ticket not found" });
        }
        // Soft delete by updating deletedAt and deletedBy
        await prismaClient_1.default.ticket.update({
            where: { id: id },
            data: {
                deletedAt: new Date(),
                deletedBy: username,
            },
        });
        await prismaClient_1.default.ticketStage.updateMany({
            where: { ticketId: id },
            data: {
                deletedAt: new Date(),
                deletedBy: username,
            },
        });
        return res.status(200).json({
            success: true,
            message: "Ticket deleted successfully"
        });
    }
    catch (error) {
        return res.status(500).json({ success: false, message: error.message });
    }
};
exports.deleteTicket = deleteTicket;
//# sourceMappingURL=delete.js.map