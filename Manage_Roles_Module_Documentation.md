# Manage Roles Module - Technical Documentation

## Overview

The Manage Roles Module is a comprehensive role-based access control (RBAC) system that provides role creation, permission assignment, and user access management. The module features an interactive roles matrix interface, granular permission management, and real-time role-permission mapping for enterprise-level access control.

## Architecture Overview

### Frontend Architecture (React/Next.js)

#### Component Structure
```
oi360-client/app/pms/manage-roles/
├── page.tsx                           # Main server component with data fetching
├── RolesMatrix.tsx                    # Interactive roles-permissions matrix
├── AddRoles.tsx                       # Role creation modal
├── UpdateRoles.tsx                    # Role editing modal
└── RoleSelected.tsx                   # Selected permissions preview component
```

#### Key Technologies
- **React 18** with Next.js App Router
- **React Hook Form** with Zod validation
- **React Select** for dropdown components
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **Sonner** for toast notifications
- **TypeScript** for type safety

#### State Management
- **Local State**: React useState hooks for component-level state
- **Form State**: React Hook Form for form management
- **Matrix State**: Complex state management for roles-permissions matrix
- **Permission State**: Real-time permission selection and validation
- **Search & Filter State**: Advanced filtering and search functionality

#### Roles Matrix Implementation
- **Interactive Grid**: Visual matrix showing roles vs permissions
- **Real-time Updates**: Live permission assignment and removal
- **Bulk Operations**: Multi-role and multi-permission operations
- **Advanced Filtering**: Search, module filtering, and modified-only view
- **Visual Indicators**: Color-coded permission states and change tracking

### Backend Architecture (Node.js/Express/Prisma)

#### API Endpoints
```
Base URL: /api/

Role Management:
├── POST   /add-roles                  # Create new role with permissions
├── GET    /get-all-roles              # Fetch all roles with permissions
├── PUT    /update-roles/:id           # Update role and permissions
├── DELETE /delete-roles/:id           # Delete role
├── GET    /get-all-permissions        # Fetch all available permissions
└── GET    /get-all-role-permissions   # Fetch role-permission mappings
```

#### Controller Structure
```
oi360-server/src/corporation/controllers/rolespermission/
├── create.ts                          # Role creation logic
├── view.ts                            # Role and permission retrieval
├── update.ts                          # Role update operations
├── delete.ts                          # Role deletion operations
├── utility.ts                         # Transaction utilities
└── find.ts                            # Query parameter utilities
```

#### Business Logic

##### Role Management
- **Role Creation**: Create roles with associated permissions
- **Permission Assignment**: Assign multiple permissions to roles
- **Role Updates**: Update role names and permission sets
- **Role Deletion**: Soft delete roles with cascade handling
- **Corporation Scoping**: Roles scoped to specific corporations

##### Permission System
- **Module-Based Permissions**: Permissions organized by functional modules
- **Action-Based Access**: Granular actions (create, read, update, delete)
- **Dynamic Permission Loading**: Runtime permission discovery
- **Permission Validation**: Comprehensive permission checking

##### Transaction Management
- **Atomic Operations**: Transactional role-permission updates
- **Rollback Support**: Error handling with transaction rollback
- **Audit Logging**: Complete audit trail for role changes
- **Consistency Checks**: Data integrity validation

### Database Schema

#### Core Models

##### Roles Model
```prisma
model Roles {
  id              Int              @id @default(autoincrement())
  name            String           @db.VarChar()
  corporation_id  Int
  client_id       Int?
  created_at      DateTime         @default(now()) @db.Timestamptz(6)
  updated_at      DateTime         @updatedAt @db.Timestamptz()
  
  corporation     Corporation      @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
  permissions     Permissions[]
  role_permission RolePermission[]
  User            User[]
}
```

##### Permissions Model
```prisma
model Permissions {
  id              Int              @id @default(autoincrement())
  module          String           @db.VarChar()
  action          String           @db.VarChar()
  created_at      DateTime         @default(now()) @db.Timestamptz(6)
  updated_at      DateTime         @updatedAt @db.Timestamptz()
  
  roles           Roles[]
  role_permission RolePermission[]
}
```

##### RolePermission Model (Junction Table)
```prisma
model RolePermission {
  id            Int         @id @default(autoincrement())
  role_id       Int
  permission_id Int
  created_at    DateTime    @default(now())
  updated_at    DateTime    @default(now()) @updatedAt
  
  role          Roles       @relation(fields: [role_id], references: [id], onDelete: Cascade)
  permission    Permissions @relation(fields: [permission_id], references: [id])
}
```

##### User Model (Related)
```prisma
model User {
  id             Int     @id @default(autoincrement())
  role_id        Int?
  corporation_id Int?
  // ... other user fields
  
  role           Roles?  @relation(fields: [role_id], references: [id], onDelete: Cascade)
  // ... other relationships
}
```

#### Relationships
- **Many-to-Many**: Roles ↔ Permissions (via RolePermission)
- **One-to-Many**: Corporation → Roles
- **One-to-Many**: Roles → Users
- **Many-to-One**: RolePermission → Roles
- **Many-to-One**: RolePermission → Permissions

#### Data Flow
1. **Role Creation**: Role created with corporation scoping
2. **Permission Assignment**: Multiple permissions assigned via junction table
3. **User Assignment**: Users assigned to roles for access control
4. **Permission Checking**: Runtime permission validation
5. **Audit Trail**: All changes logged for compliance

## Feature Specifications

### Roles Matrix Features
- **Interactive Grid**: Visual matrix showing all roles and permissions
- **Real-time Editing**: Live permission assignment and removal
- **Bulk Operations**: Multi-role permission management
- **Search & Filter**: Advanced filtering by role, module, or permission
- **Change Tracking**: Visual indicators for modified permissions
- **Undo/Redo**: Change management with save/cancel operations

### Role Management Features
- **Role Creation**: Create new roles with permission assignment
- **Role Editing**: Update role names and permission sets
- **Role Deletion**: Safe role deletion with dependency checking
- **Permission Preview**: Real-time preview of selected permissions
- **Module Organization**: Permissions organized by functional modules
- **Corporation Scoping**: Roles isolated by corporation

### Permission System Features
- **Module-Based Organization**: Permissions grouped by system modules
- **Action-Based Granularity**: Fine-grained action permissions
- **Dynamic Loading**: Runtime permission discovery and loading
- **Hierarchical Structure**: Organized permission hierarchy
- **Validation System**: Comprehensive permission validation

### Advanced Features
- **Search Functionality**: Global search across roles and permissions
- **Filter Options**: Multiple filtering criteria
- **Bulk Selection**: Multi-item selection and operations
- **Visual Feedback**: Color-coded states and change indicators
- **Responsive Design**: Mobile-friendly interface
- **Accessibility**: Screen reader and keyboard navigation support

## API Integration Patterns

#### Role Operations
```typescript
// Create role with permissions
const createRole = async (roleData: {
  name: string;
  permission: number[];
  client_id?: number;
}) => {
  return await formSubmit('/api/add-roles', 'POST', roleData);
};

// Update role permissions
const updateRole = async (roleId: string, updates: {
  name: string;
  permission: number[];
}) => {
  return await formSubmit(`/api/update-roles/${roleId}`, 'PUT', updates);
};

// Delete role
const deleteRole = async (roleId: string) => {
  return await formSubmit(`/api/delete-roles/${roleId}`, 'DELETE', {});
};
```

#### Permission Operations
```typescript
// Fetch all permissions
const getAllPermissions = async () => {
  return await getAllData('/api/get-all-permissions');
};

// Fetch roles with permissions
const getAllRoles = async () => {
  return await getAllData('/api/get-all-roles');
};
```

## Security & Permissions

### Access Control
- **Permission Required**: "create-role", "view-role", "update-role", "delete-role"
- **Role-Based Access**: Different permissions for different operations
- **Corporation Scoping**: Roles isolated by corporation boundaries
- **User Validation**: Comprehensive user permission checking

### Data Validation
- **Input Sanitization**: All inputs validated and sanitized
- **Permission Validation**: Ensures valid permission assignments
- **Role Name Validation**: Unique role names within corporation
- **Dependency Checking**: Validates role deletion dependencies

## Performance Considerations

### Frontend Optimizations
- **Component Memoization**: React.memo for expensive components
- **State Optimization**: Efficient state management for large matrices
- **Debounced Search**: Prevents excessive re-renders during search
- **Lazy Loading**: Dynamic imports for heavy components
- **Virtual Scrolling**: Efficient rendering for large permission sets

### Backend Optimizations
- **Database Indexing**: Proper indexes on foreign keys and search fields
- **Query Optimization**: Selective field loading with Prisma includes
- **Transaction Optimization**: Efficient bulk operations
- **Caching Strategy**: Potential for Redis caching of permissions

## Error Handling

### Frontend Error Handling
- **Toast Notifications**: User-friendly error messages via Sonner
- **Form Validation**: Client-side validation with Zod schemas
- **Loading States**: Visual feedback during operations
- **Graceful Degradation**: Fallback UI for failed operations
- **Confirmation Dialogs**: User confirmation for destructive operations

### Backend Error Handling
- **Structured Responses**: Consistent error response format
- **HTTP Status Codes**: Appropriate status codes for different scenarios
- **Input Validation**: Comprehensive request validation
- **Transaction Rollback**: Proper error handling with rollback
- **Permission Errors**: Specific error handling for permission issues

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with Jest/React Testing Library
- **Integration Tests**: Roles matrix functionality testing
- **E2E Tests**: Complete role management workflows
- **Accessibility Tests**: Screen reader and keyboard navigation
- **Permission Tests**: Role-based access control testing

### Backend Testing
- **Unit Tests**: Controller and utility function testing
- **Integration Tests**: Database operation testing
- **API Tests**: Endpoint testing with various scenarios
- **Permission Tests**: Access control validation testing
- **Transaction Tests**: Atomic operation testing

## Deployment & Monitoring

### Deployment Pipeline
- **Environment Variables**: API endpoints and configuration
- **Build Process**: Next.js optimization and bundling
- **Database Migrations**: Prisma migration management
- **Permission Seeding**: Initial permission data setup

### Monitoring & Logging
- **Error Tracking**: Frontend and backend error logging
- **Performance Monitoring**: API response times and matrix performance
- **Usage Analytics**: Role creation and permission usage tracking
- **Security Monitoring**: Permission access and role changes

## Future Enhancements

### Planned Features
- **Role Templates**: Pre-built role templates for common use cases
- **Permission Groups**: Logical grouping of related permissions
- **Role Inheritance**: Hierarchical role inheritance system
- **Advanced Analytics**: Role usage analytics and optimization
- **Bulk Import/Export**: CSV import/export for role management
- **Role Approval Workflow**: Approval process for role changes

### Technical Improvements
- **Real-time Collaboration**: Live updates for multi-user editing
- **Advanced Search**: Full-text search with indexing
- **Performance Optimization**: Advanced caching and optimization
- **Mobile App**: Native mobile role management interface
- **API Versioning**: Versioned APIs for backward compatibility

## Integration Points

### User Management System
- **User-Role Assignment**: Integration with user management
- **Permission Checking**: Runtime permission validation
- **Session Management**: Role-based session handling

### Authentication System
- **Login Integration**: Role-based login and access control
- **Token Management**: JWT token with role information
- **Middleware Integration**: Permission checking middleware

### Audit System
- **Change Logging**: Complete audit trail for role changes
- **Compliance Reporting**: Role and permission compliance reports
- **Activity Tracking**: User activity and role usage tracking

## Permission Modules

### Available Modules
- **USER MANAGEMENT**: User creation, editing, and management
- **ROLE MANAGEMENT**: Role and permission management
- **TICKET MANAGEMENT**: Ticket system permissions
- **INVOICE MANAGEMENT**: Invoice file management permissions
- **CARRIER MANAGEMENT**: Carrier data management
- **ASSOCIATE MANAGEMENT**: Associate management permissions
- **WORKTYPE MANAGEMENT**: Work type configuration
- **CATEGORY MANAGEMENT**: Category management permissions

### Permission Actions
- **create-**: Creation permissions for resources
- **view-**: Read permissions for resources
- **update-**: Update permissions for resources
- **delete-**: Deletion permissions for resources
- **manage-**: Full management permissions for resources
