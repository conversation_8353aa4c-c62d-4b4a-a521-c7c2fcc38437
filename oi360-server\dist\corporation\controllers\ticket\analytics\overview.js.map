{"version": 3, "file": "overview.js", "sourceRoot": "", "sources": ["../../../../../src/corporation/controllers/ticket/analytics/overview.ts"], "names": [], "mappings": ";;;AAAA,uDAAwD;AAEjD,MAAM,2BAA2B,GAAG,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACtE,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,CAAC,CAAS,EAAE,EAAE;YACnC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC3D,OAAO,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC;QACF,MAAM,YAAY,GAAG,CAAC,CAAO,EAAE,EAAE;YAC/B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QACF,MAAM,UAAU,GAAG,CAAC,CAAO,EAAE,EAAE;YAC7B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAC5B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QAEF,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,GACV,GAAG,GAAG,CAAC,KAAK,CAAC;QAEd,MAAM,WAAW,GAAQ;YACvB,SAAS,EAAE,IAAI;SAChB,CAAC;QACF,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,CAAC,EAAE,CAAC;YACxD,WAAW,CAAC,SAAS,GAAG,EAAE,CAAC;YAC3B,IAAI,QAAQ,EAAE,CAAC;gBACb,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrE,CAAC;YACD,IAAI,MAAM,EAAE,CAAC;gBACX,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAClE,WAAW,CAAC,IAAI,GAAG;gBACjB,OAAO,EAAE,QAAQ;aAClB,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAClC,CAAC;QAED,MAAM,gBAAgB,GAAQ,EAAE,CAAC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC;QAC3C,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC;QAC7C,CAAC;QAED,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YACrD,KAAK,EAAE;gBACL,GAAG,WAAW;gBACd,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI;oBAC9C,MAAM,EAAE;wBACN,IAAI,EAAE,gBAAgB;qBACvB;iBACF,CAAC;aACH;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;yBAC1B;qBACF;iBACF;gBACD,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,aAAa,EAAE,IAAI;qBACpB;oBACD,OAAO,EAAE;wBACP,SAAS,EAAE,KAAK;qBACjB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,eAAe,GAAG,iBAAiB,CAAC;QACxC,IAAI,WAAW,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAChF,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxE,eAAe,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;gBACpD,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;oBAAE,OAAO,KAAK,CAAC;gBACxG,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC5G,IAAI,CAAC,YAAY;oBAAE,OAAO,KAAK,CAAC;gBAChC,MAAM,KAAK,GAAgB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC3F,IAAI,CAAC,KAAK;oBAAE,OAAO,KAAK,CAAC;gBACzB,IAAI,QAAQ,IAAI,KAAK,GAAG,QAAQ;oBAAE,OAAO,KAAK,CAAC;gBAC/C,IAAI,MAAM,IAAI,KAAK,GAAG,MAAM;oBAAE,OAAO,KAAK,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC;QAE5C,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAC5B,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAE7B,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IACE,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;gBACnC,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;gBACzB,MAAM,CAAC,cAAc,EACrB,CAAC;gBACD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7E,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CACrC,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAC3E,CAAC;gBACF,IAAI,YAAY,IAAI,MAAM,CAAC,cAAc,KAAK,UAAU,CAAC,EAAE,EAAE,CAAC;oBAC5D,aAAa,EAAE,CAAC;oBAChB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAC7C,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oBAClD,MAAM,mBAAmB,GACvB,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;oBAChE,mBAAmB,IAAI,mBAAmB,CAAC;oBAC3C,oBAAoB,EAAE,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,YAAY,GAAG,aAAa,CAAC;QACjD,MAAM,WAAW,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,MAAM,kBAAkB,GACtB,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5E,IAAI,sBAAsB,GAAG,YAAY,CAAC;QAC1C,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1E,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAClE,sBAAsB,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;gBACpD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBACxC,IAAI,QAAQ,IAAI,SAAS,GAAG,QAAQ;oBAAE,OAAO,KAAK,CAAC;gBACnD,IAAI,MAAM,IAAI,SAAS,GAAG,MAAM;oBAAE,OAAO,KAAK,CAAC;gBAC/C,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC,MAAM,CAAC;QACZ,CAAC;QAED,IAAI,qBAAqB,GAAG,CAAC,CAAC;QAC9B,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;YACvB,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;gBACzD,IACE,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC;oBACnC,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;oBACzB,MAAM,CAAC,cAAc,EACrB,CAAC;oBACD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC7E,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CACrC,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAC3E,CAAC;oBACF,IAAI,YAAY,IAAI,MAAM,CAAC,cAAc,KAAK,UAAU,CAAC,EAAE,EAAE,CAAC;wBAC5D,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;wBAClD,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;4BACvB,OAAO,CACL,QAAQ,IAAI,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gCAClD,QAAQ,IAAI,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAC/C,CAAC;wBACJ,CAAC;6BAAM,IAAI,QAAQ,EAAE,CAAC;4BACpB,OAAO,QAAQ,IAAI,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;wBAC5D,CAAC;6BAAM,IAAI,MAAM,EAAE,CAAC;4BAClB,OAAO,QAAQ,IAAI,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;wBACxD,CAAC;wBACD,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;YACH,qBAAqB,GAAG,gBAAgB,CAAC,MAAM,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,qBAAqB,GAAG,aAAa,CAAC;QACxC,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,YAAY;gBACZ,aAAa;gBACb,WAAW;gBACX,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC9D,sBAAsB,EAAE,OAAO;gBAC/B,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;gBAChD,sBAAsB;gBACtB,qBAAqB;aACtB;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAxMW,QAAA,2BAA2B,+BAwMtC"}