"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/swr";
exports.ids = ["vendor-chunks/swr"];
exports.modules = {

/***/ "(ssr)/./node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ noop),\n/* harmony export */   B: () => (/* binding */ isPromiseLike),\n/* harmony export */   I: () => (/* binding */ IS_REACT_LEGACY),\n/* harmony export */   O: () => (/* binding */ OBJECT),\n/* harmony export */   S: () => (/* binding */ SWRConfigContext),\n/* harmony export */   U: () => (/* binding */ UNDEFINED),\n/* harmony export */   a: () => (/* binding */ isFunction),\n/* harmony export */   b: () => (/* binding */ SWRGlobalState),\n/* harmony export */   c: () => (/* binding */ cache),\n/* harmony export */   d: () => (/* binding */ defaultConfig),\n/* harmony export */   e: () => (/* binding */ isUndefined),\n/* harmony export */   f: () => (/* binding */ mergeConfigs),\n/* harmony export */   g: () => (/* binding */ SWRConfig),\n/* harmony export */   h: () => (/* binding */ initCache),\n/* harmony export */   i: () => (/* binding */ isWindowDefined),\n/* harmony export */   j: () => (/* binding */ mutate),\n/* harmony export */   k: () => (/* binding */ compare),\n/* harmony export */   l: () => (/* binding */ stableHash),\n/* harmony export */   m: () => (/* binding */ mergeObjects),\n/* harmony export */   n: () => (/* binding */ internalMutate),\n/* harmony export */   o: () => (/* binding */ getTimestamp),\n/* harmony export */   p: () => (/* binding */ preset),\n/* harmony export */   q: () => (/* binding */ defaultConfigOptions),\n/* harmony export */   r: () => (/* binding */ IS_SERVER),\n/* harmony export */   s: () => (/* binding */ serialize),\n/* harmony export */   t: () => (/* binding */ rAF),\n/* harmony export */   u: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   v: () => (/* binding */ slowConnection),\n/* harmony export */   w: () => (/* binding */ isDocumentDefined),\n/* harmony export */   x: () => (/* binding */ isLegacyDeno),\n/* harmony export */   y: () => (/* binding */ hasRequestAnimationFrame),\n/* harmony export */   z: () => (/* binding */ createCacheHelper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var dequal_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal/lite */ \"(ssr)/./node_modules/dequal/lite/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ A,B,I,O,S,U,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z auto */ \n\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == \"function\";\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = \"undefined\";\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = \"undefined\" != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && \"Deno\" in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window[\"requestAnimationFrame\"] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== \"hidden\";\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener(\"visibilitychange\", callback);\n    }\n    onWindowEvent(\"focus\", callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener(\"visibilitychange\", callback);\n        }\n        offWindowEvent(\"focus\", callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent(\"online\", onOnline);\n    onWindowEvent(\"offline\", onOffline);\n    return ()=>{\n        offWindowEvent(\"online\", onOnline);\n        offWindowEvent(\"offline\", onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\nconst IS_REACT_LEGACY = !react__WEBPACK_IMPORTED_MODULE_0__.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window[\"requestAnimationFrame\"](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== \"undefined\" && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    \"slow-2g\",\n    \"2g\"\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, \"Date\");\n    const isRegex = isObjectTypeName(typeName, \"RegExp\");\n    const isPlainObject = isObjectTypeName(typeName, \"Object\");\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + \"~\";\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = \"@\";\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + \",\";\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = \"#\";\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + \":\" + stableHash(arg[index]) + \",\";\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == \"symbol\" ? arg.toString() : type == \"string\" ? JSON.stringify(arg) : \"\" + arg;\n    }\n    return result;\n};\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = \"\";\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == \"string\" ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : \"\";\n    return [\n        key,\n        args\n    ];\n};\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === \"boolean\" ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === \"function\" ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (!/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](_events_mjs__WEBPACK_IMPORTED_MODULE_2__.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        let isError = false;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n                isError = true;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n                isError = true;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (isError) throw error;\n                return data;\n            } else if (isError && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!isError) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (isError) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal_lite__WEBPACK_IMPORTED_MODULE_1__.dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, preset);\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\nconst SWRConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/constants.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/swr/dist/_internal/constants.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* binding */ INFINITE_PREFIX)\n/* harmony export */ });\nconst INFINITE_PREFIX = '$inf$';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2NvbnN0YW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUUyQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NsaWVudC8uL25vZGVfbW9kdWxlcy9zd3IvZGlzdC9faW50ZXJuYWwvY29uc3RhbnRzLm1qcz9mYjM2Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IElORklOSVRFX1BSRUZJWCA9ICckaW5mJCc7XG5cbmV4cG9ydCB7IElORklOSVRFX1BSRUZJWCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/constants.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/events.mjs":
/*!****************************************************!*\
  !*** ./node_modules/swr/dist/_internal/events.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_REVALIDATE_EVENT: () => (/* binding */ ERROR_REVALIDATE_EVENT),\n/* harmony export */   FOCUS_EVENT: () => (/* binding */ FOCUS_EVENT),\n/* harmony export */   MUTATE_EVENT: () => (/* binding */ MUTATE_EVENT),\n/* harmony export */   RECONNECT_EVENT: () => (/* binding */ RECONNECT_EVENT)\n/* harmony export */ });\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2V2ZW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUU4RSIsInNvdXJjZXMiOlsid2VicGFjazovL2NsaWVudC8uL25vZGVfbW9kdWxlcy9zd3IvZGlzdC9faW50ZXJuYWwvZXZlbnRzLm1qcz8xNmYzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEZPQ1VTX0VWRU5UID0gMDtcbmNvbnN0IFJFQ09OTkVDVF9FVkVOVCA9IDE7XG5jb25zdCBNVVRBVEVfRVZFTlQgPSAyO1xuY29uc3QgRVJST1JfUkVWQUxJREFURV9FVkVOVCA9IDM7XG5cbmV4cG9ydCB7IEVSUk9SX1JFVkFMSURBVEVfRVZFTlQsIEZPQ1VTX0VWRU5ULCBNVVRBVEVfRVZFTlQsIFJFQ09OTkVDVF9FVkVOVCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/events.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/_internal/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/swr/dist/_internal/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* reexport safe */ _constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX),\n/* harmony export */   IS_REACT_LEGACY: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IS_SERVER: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   OBJECT: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.O),\n/* harmony export */   SWRConfig: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   SWRGlobalState: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   UNDEFINED: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.U),\n/* harmony export */   cache: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   compare: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   createCacheHelper: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   defaultConfig: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   defaultConfigOptions: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   getTimestamp: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   hasRequestAnimationFrame: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   initCache: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   internalMutate: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   isDocumentDefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   isFunction: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   isLegacyDeno: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   isPromiseLike: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.B),\n/* harmony export */   isUndefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   isWindowDefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   mergeConfigs: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   mergeObjects: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   mutate: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   noop: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   preload: () => (/* binding */ preload),\n/* harmony export */   preset: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   rAF: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   revalidateEvents: () => (/* reexport module object */ _events_mjs__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   serialize: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   slowConnection: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   stableHash: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   subscribeCallback: () => (/* binding */ subscribeCallback),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.u),\n/* harmony export */   useSWRConfig: () => (/* binding */ useSWRConfig),\n/* harmony export */   withArgs: () => (/* binding */ withArgs),\n/* harmony export */   withMiddleware: () => (/* binding */ withMiddleware)\n/* harmony export */ });\n/* harmony import */ var _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config-context-client-BoS53ST9.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n\n\n\n// @ts-expect-error\nconst enableDevtools = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.i && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = react__WEBPACK_IMPORTED_MODULE_3__;\n    }\n};\n\nconst normalize = (args)=>{\n    return (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.S);\n    const mergedConfig = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>(0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.d, parentConfig), [\n        parentConfig\n    ]);\n    return mergedConfig;\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n    const [, , , PRELOAD] = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n            const [, , , PRELOAD] = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n            if (key.startsWith(_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if ((0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/index/index.mjs":
/*!***********************************************!*\
  !*** ./node_modules/swr/dist/index/index.mjs ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   \"default\": () => (/* binding */ useSWR),\n/* harmony export */   mutate: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.j),\n/* harmony export */   preload: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.preload),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize),\n/* harmony export */   useSWRConfig: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.useSWRConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/swr/dist/_internal/index.mjs\");\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst resolvedUndef = Promise.resolve(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U);\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.b.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.s)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.z)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(fallbackData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(config.fallback) ? _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.m)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? fallback && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.B)(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.o)()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.n)(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            // Performance optimization: if a request is already in progress for this key,\n            // skip the revalidation to avoid redundant work\n            if (!FETCH[key]) {\n                if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n                    // Revalidate immediately.\n                    softRevalidate();\n                } else {\n                    // Delay the revalidate if we have data to return so we won't block\n                    // rendering.\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.t)(softRevalidate);\n                }\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense) {\n        const hasKeyButNoData = key && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data);\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I && _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r && hasKeyButNoData) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        if (hasKeyButNoData) {\n            fetcherRef.current = fetcher;\n            configRef.current = config;\n            unmountedRef.current = false;\n        }\n        const req = PRELOAD[key];\n        const mutateReq = !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(req) && hasKeyButNoData ? boundMutate(req) : resolvedUndef;\n        use(mutateReq);\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error) && hasKeyButNoData) {\n            throw error;\n        }\n        const revalidation = hasKeyButNoData ? revalidate(WITH_DEDUPE) : resolvedUndef;\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(returnedData) && hasKeyButNoData) {\n            // @ts-ignore modify react promise status\n            revalidation.status = 'fulfilled';\n            // @ts-ignore modify react promise value\n            revalidation.value = true;\n        }\n        use(revalidation);\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.O.defineProperty(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.g, 'defaultValue', {\n    value: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.d\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.withArgs)(useSWRHandler);\n\n// useSWR\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/index/index.mjs\n");

/***/ })

};
;