# Custom Fields Arrangement Module - Technical Documentation

## Overview

The Custom Fields Arrangement Module is a comprehensive system that allows administrators to manage and arrange both mandatory and custom fields for clients in the OI360 platform. The module provides drag-and-drop functionality for field arrangement, client-specific customization, and real-time preview capabilities.

## Architecture Overview

### Frontend Architecture (React/Next.js)

#### Component Structure
```
oi360-client/app/pms/arrange_custom_fields/
├── page.tsx                           # Main arrangement page (v1)
├── ArrangementButton.tsx              # Navigation button component
├── v1/
│   ├── page.tsx                       # Version 1 implementation
│   ├── MandatoryFieldsArrangement.tsx # Mandatory fields management
│   └── CustomFieldsArrangement.tsx   # Custom fields management
└── v2/
    ├── page.tsx                       # Version 2 implementation (enhanced)
    ├── MandatoryFieldsArrangement.tsx # Enhanced mandatory fields with grid layout
    ├── CustomFieldsArrangement.tsx   # Enhanced custom fields management
    └── MandatoryFieldsPreview.tsx    # Preview component for mandatory fields
```

#### Key Technologies
- **React 18** with Next.js App Router
- **@dnd-kit** for drag-and-drop functionality
- **React Select** for client selection
- **Tailwind CSS** for styling
- **Axios** for API communication
- **Sonner** for toast notifications

#### State Management
- **Local State**: React useState hooks for component-level state
- **Client Selection**: Managed via React Select component
- **Field Ordering**: Array manipulation with drag-and-drop events
- **Permission Management**: Role-based access control integration

#### Drag-and-Drop Implementation
- **Sensors**: PointerSensor and TouchSensor for multi-device support
- **Collision Detection**: Custom collision detection for mandatory fields grid
- **Sortable Context**: Manages reorderable custom fields
- **Grid Layout**: Complex grid system for mandatory fields with span support

### Backend Architecture (Node.js/Express/Prisma)

#### API Endpoints
```
Base URL: /api/

Custom Fields Management:
├── GET    /custom-fields                    # Fetch all custom fields
├── POST   /custom-fields                    # Create new custom field
├── GET    /custom-fields-with-clients       # Fetch fields with client associations
├── GET    /client-custom-fields/:clientId   # Fetch client-specific custom fields
├── POST   /client-custom-fields             # Create client custom field arrangement
└── POST   /client-custom-fields/order       # Update custom field order

Mandatory Fields:
└── GET    /mandatory-fields                 # Fetch mandatory fields from track_sheets schema
```

#### Controller Structure
```
oi360-server/src/corporation/controllers/
├── customfields/
│   ├── view.ts                           # General custom fields operations
│   ├── create.ts                         # Custom field creation
│   ├── clientcustomfields/
│   │   ├── view.ts                       # Client-specific field retrieval
│   │   ├── create.ts                     # Client field arrangement creation
│   │   └── update.ts                     # Field order updates
│   └── mandatoryfields/
│       └── view.ts                       # Mandatory fields from database schema
└── clientCustomFields/
    └── view.ts                           # Alternative client fields controller
```

#### Business Logic

##### Custom Fields Management
- **Field Creation**: Validates field types (TEXT, NUMBER, DATE, AUTO)
- **Auto Fields**: Special handling for auto-generated fields with options
- **Client Association**: Links custom fields to specific clients with ordering
- **Duplicate Prevention**: Checks for existing fields before creation

##### Mandatory Fields Management
- **Schema Introspection**: Dynamically fetches columns from track_sheets table
- **Field Filtering**: Excludes system fields (id, createdAt, updatedAt)
- **Display Name Generation**: Converts snake_case to Title Case
- **Alphabetical Sorting**: Orders fields for consistent display

##### Order Management
- **Transaction Safety**: Uses Prisma transactions for atomic operations
- **Order Preservation**: Maintains 1-based ordering system
- **Bulk Updates**: Handles multiple field reordering efficiently
- **Validation**: Ensures all referenced fields exist before updates

### Database Schema

#### Core Models

##### CustomField Model
```prisma
model CustomField {
  id         String    @id @default(uuid())
  type       FieldType # TEXT, NUMBER, DATE, AUTO
  autoOption AutoOption? # For AUTO type fields
  name       String   @unique
  createdAt  DateTime  @default(now())
  createdBy  String?
  updatedAt  DateTime  @updatedAt
  updatedBy  String?
  
  TrackSheetCustomFieldMapping TrackSheetCustomFieldMapping[]
  ClientCustomFieldArrangement ClientCustomFieldArrangement[]
}
```

##### ClientCustomFieldArrangement Model
```prisma
model ClientCustomFieldArrangement {
  id              String @id @default(uuid())
  client_id       Int
  custom_field_id String
  order           Int    # 1-based ordering
  
  Client      Client      @relation(fields: [client_id], references: [id], onDelete: Cascade)
  CustomField CustomField @relation(fields: [custom_field_id], references: [id], onDelete: Cascade)
}
```

##### TrackSheets Model (Mandatory Fields Source)
```prisma
model TrackSheets {
  id                 Int       @id @default(autoincrement())
  clientId           Int?
  company            String?
  division           String?
  masterInvoice      String?
  invoice            String?
  bol                String?
  invoiceDate        DateTime?
  receivedDate       DateTime?
  shipmentDate       DateTime?
  carrierId          Int?
  # ... 30+ additional fields that become mandatory fields
  
  TrackSheetCustomFieldMapping TrackSheetCustomFieldMapping[]
}
```

#### Relationships
- **One-to-Many**: Client → ClientCustomFieldArrangement
- **One-to-Many**: CustomField → ClientCustomFieldArrangement
- **Many-to-Many**: TrackSheets ↔ CustomField (via TrackSheetCustomFieldMapping)

#### Data Flow
1. **Mandatory Fields**: Extracted from TrackSheets table schema via SQL introspection
2. **Custom Fields**: Created and stored in CustomField table
3. **Client Arrangements**: Stored in ClientCustomFieldArrangement with ordering
4. **Field Values**: Stored in TrackSheetCustomFieldMapping for actual data

## Feature Specifications

### Version 1 (v1) Features
- Basic drag-and-drop custom field arrangement
- Client selection dropdown
- Field type display (Text, Number, Date, Auto)
- Reset functionality
- Save order functionality
- Permission-based access control

### Version 2 (v2) Features
- **Enhanced Mandatory Fields Management**:
  - Grid-based layout with configurable columns (3-10)
  - Field spanning (1-4 columns)
  - Drag-and-drop within grid cells
  - Field removal capability
  - Real-time preview modal
  - Responsive grid layout

- **Improved Custom Fields Management**:
  - Enhanced field type detection
  - Better auto-field handling
  - Improved UI/UX with animations
  - Field selection indicators

### Drag-and-Drop Specifications

#### Custom Fields (Both Versions)
- **Strategy**: Vertical list sorting
- **Collision Detection**: Closest center
- **Visual Feedback**: Opacity changes, scaling effects
- **Touch Support**: Mobile-friendly with activation constraints

#### Mandatory Fields (v2 Only)
- **Strategy**: Grid-based positioning
- **Collision Detection**: Custom pointer-within and rect-intersection
- **Spanning**: Fields can span 1-4 columns
- **Displacement**: Automatic field repositioning when inserting
- **Grid Management**: Dynamic row addition, column constraints

### API Integration Patterns

#### Data Fetching
```typescript
// Client selection
const clients = await getAllData(client_routes.GETALL_CLIENT);

// Custom fields for client
const response = await axios.get(`/api/client-custom-fields/${clientId}`);

// Mandatory fields
const response = await axios.get(`/api/mandatory-fields`);
```

#### Data Persistence
```typescript
// Save custom field order
await axios.post('/api/client-custom-fields/order', {
  client_id: selectedClient,
  custom_fields: orderedFieldIds
});
```

## Security & Permissions

### Access Control
- **Permission Required**: "arrange-custom-fields"
- **Role-Based**: Integrated with existing permission system
- **Corporation Override**: Corporation token allows full access

### Data Validation
- **Client ID Validation**: Ensures valid client selection
- **Field Existence**: Validates all field IDs before operations
- **Transaction Safety**: Database operations wrapped in transactions

## Performance Considerations

### Frontend Optimizations
- **Memoization**: useCallback for expensive operations
- **Lazy Loading**: Components loaded on demand
- **Debounced Operations**: Prevents excessive API calls
- **Virtual Scrolling**: For large field lists (future enhancement)

### Backend Optimizations
- **Database Indexing**: Proper indexes on foreign keys
- **Bulk Operations**: Transaction-based bulk updates
- **Query Optimization**: Selective field loading with Prisma
- **Caching Strategy**: Potential for Redis caching (future enhancement)

## Error Handling

### Frontend Error Handling
- **Toast Notifications**: User-friendly error messages
- **Loading States**: Visual feedback during operations
- **Validation**: Client-side validation before API calls
- **Fallback UI**: Graceful degradation for failed operations

### Backend Error Handling
- **Input Validation**: Comprehensive request validation
- **Database Errors**: Proper error catching and logging
- **Transaction Rollback**: Automatic rollback on failures
- **HTTP Status Codes**: Appropriate response codes

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with Jest/React Testing Library
- **Integration Tests**: API integration testing
- **E2E Tests**: Drag-and-drop functionality testing
- **Accessibility Tests**: Screen reader and keyboard navigation

### Backend Testing
- **Unit Tests**: Controller and service testing
- **Integration Tests**: Database operation testing
- **API Tests**: Endpoint testing with various scenarios
- **Performance Tests**: Load testing for bulk operations

## Deployment & Monitoring

### Deployment Pipeline
- **Environment Variables**: API base URLs, database connections
- **Build Process**: Next.js build optimization
- **Database Migrations**: Prisma migration management
- **Health Checks**: API endpoint monitoring

### Monitoring & Logging
- **Error Tracking**: Frontend and backend error logging
- **Performance Monitoring**: API response times
- **User Analytics**: Feature usage tracking
- **Database Monitoring**: Query performance and connection health

## Future Enhancements

### Planned Features
- **Field Templates**: Predefined field arrangements
- **Bulk Operations**: Multi-client field management
- **Field Validation Rules**: Custom validation for fields
- **Import/Export**: Field arrangement backup/restore
- **Audit Trail**: Change history tracking
- **Real-time Collaboration**: Multi-user editing support

### Technical Improvements
- **GraphQL Integration**: More efficient data fetching
- **WebSocket Support**: Real-time updates
- **Offline Support**: PWA capabilities
- **Advanced Caching**: Redis implementation
- **Microservices**: Service decomposition for scalability
