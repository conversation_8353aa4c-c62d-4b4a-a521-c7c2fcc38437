# Manage Invoice Files Module - Technical Documentation

## Overview

The Manage Invoice Files Module is a comprehensive file management system that handles invoice file processing, assignment, and tracking. The module provides file upload capabilities, bulk operations, advanced filtering, user assignment management, and seamless integration with track sheets and ticketing systems.

## Architecture Overview

### Frontend Architecture (React/Next.js)

#### Component Structure
```
oi360-client/app/pms/manage_invoice_files/
├── page.tsx                           # Main server component with data fetching
├── InvoiceFilesBoard.tsx              # Main dashboard and layout component
├── invoiceFilesContext.tsx            # React Context for state management
├── types/
│   └── index.ts                       # TypeScript type definitions
├── services/
│   └── invoiceFileService.ts          # API service functions
├── components/
│   ├── ViewInvoiceFiles.tsx           # AG Grid data table component
│   ├── AddInvoiceFile.tsx             # File upload and creation modal
│   ├── EditInvoiceFile.tsx            # File editing modal
│   ├── DeleteInvoiceFile.tsx          # File deletion confirmation
│   ├── InvoiceFileDetails.tsx         # File details modal
│   ├── InvoiceFileFilters.tsx         # Advanced filtering component
│   ├── BulkActions.tsx                # Bulk operations component
│   └── TicketIconCell.tsx             # Ticket integration cell renderer
└── view_tracksheets/
    ├── ViewTracksheetById.tsx         # Track sheet viewer
    └── [invoiceFileId]/
        └── page.tsx                   # Track sheet detail page
```

#### Key Technologies
- **React 18** with Next.js App Router
- **AG Grid React** for advanced data table functionality
- **React Hook Form** with Zod validation
- **PDF-lib** for PDF processing and page counting
- **React Select** for dropdown components
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **React Hot Toast** for notifications
- **TypeScript** for type safety

#### State Management
- **React Context**: Global state for reload triggers and data synchronization
- **Local State**: Component-level state for UI interactions
- **AG Grid State**: Advanced table state management with sorting, filtering, and pagination
- **Form State**: React Hook Form for form management
- **File Upload State**: PDF processing and validation state

#### File Upload Implementation
- **PDF Processing**: Automatic page counting using PDF-lib
- **Bulk Upload**: Multiple file processing with validation
- **Duplicate Detection**: Client-side and server-side duplicate checking
- **Progress Tracking**: Upload progress and status feedback
- **Error Handling**: Comprehensive error handling and user feedback

### Backend Architecture (Node.js/Express/Prisma)

#### API Endpoints
```
Base URL: /api/

Invoice File Management:
├── POST   /invoice-files                    # Create single invoice file
├── POST   /invoice-files/bulk               # Create multiple invoice files
├── GET    /invoice-files                    # Fetch all invoice files with pagination
├── GET    /invoice-files/:id                # Fetch specific invoice file
├── PUT    /invoice-files/:id                # Update invoice file
├── DELETE /invoice-files/:id                # Soft delete invoice file
├── GET    /invoice-files/user/:userId       # Fetch files by user
├── GET    /invoice-files/check-unique       # Check for duplicate files
├── POST   /invoice-files/bulk-assign        # Bulk assign files to users
├── POST   /invoice-files/bulk-delete        # Bulk delete files
└── GET    /invoice-files/:id/tracksheets    # Fetch track sheets by invoice file
```

#### Controller Structure
```
oi360-server/src/corporation/controllers/invoiceFile/
├── create.ts                          # File creation and bulk creation
├── viewAll.ts                         # File retrieval with filtering
├── view.ts                            # Single file retrieval
├── viewByUser.ts                      # User-specific file retrieval
├── update.ts                          # File update operations
├── delete.ts                          # File deletion operations
├── bulkAssign.ts                      # Bulk assignment operations
├── bulkDelete.ts                      # Bulk deletion operations
├── checkUniqueFiles.ts                # Duplicate detection
└── viewTracksheetsById.ts             # Track sheet integration
```

#### Business Logic

##### File Management
- **Single File Creation**: Individual file upload with validation
- **Bulk File Creation**: Multiple file processing with duplicate detection
- **File Updates**: Comprehensive field updates with validation
- **Soft Deletion**: Audit trail preservation with soft delete
- **Assignment Management**: User assignment with validation

##### Bulk Operations
- **Bulk Assignment**: Multi-file user assignment
- **Bulk Deletion**: Multi-file soft deletion
- **Duplicate Detection**: Server-side duplicate checking
- **Transaction Management**: Atomic operations for data consistency

##### Advanced Filtering
- **Text Search**: Full-text search across file names and metadata
- **Date Range Filtering**: Filter by creation date or file date
- **Status Filtering**: Filter by processing status (in progress, done)
- **User Filtering**: Filter by assigned user
- **Carrier Filtering**: Filter by carrier association
- **Pagination**: Efficient large dataset handling

### Database Schema

#### Core Models

##### InvoiceFile Model
```prisma
model InvoiceFile {
  id           String    @id @default(uuid())
  date         DateTime  @map("date") @db.Date
  fileName     String    @map("file_name") @db.VarChar(255)
  noOfPages    Int       @map("no_of_pages")
  assignedTo   Int?      @map("assigned_to")
  carrierId    Int       @map("carrier_id")
  status       InvoiceFileStatus @default(inProgress)
  
  carrier      Carrier?  @relation(fields: [carrierId], references: [id], onDelete: Cascade)
  assignedToUser User?   @relation("InvoiceFileAssignedTo", fields: [assignedTo], references: [id], onDelete: Cascade)
  TrackSheets  TrackSheets[]
  
  createdAt    DateTime  @map("created_at") @default(now())
  createdBy    Int?      @map("created_by")
  updatedAt    DateTime? @map("updated_at") @updatedAt
  updatedBy    Int?      @map("updated_by")
  deletedAt    DateTime? @map("deleted_at")
  deletedBy    Int?      @map("deleted_by")
  
  @@unique([carrierId, noOfPages, date, fileName, deletedAt])
  @@index([assignedTo])
  @@index([createdAt])
  @@index([deletedAt])
  @@index([date])
}
```

##### InvoiceFileStatus Enum
```prisma
enum InvoiceFileStatus {
  inProgress @map("in_progress")
  done
}
```

##### TrackSheets Model (Related)
```prisma
model TrackSheets {
  id            Int       @id @default(autoincrement())
  invoiceFileId String?   @map("invoice_file_id")
  invoiceFile   InvoiceFile? @relation(fields: [invoiceFileId], references: [id], onDelete: Cascade)
  // ... other track sheet fields
}
```

#### Relationships
- **Many-to-One**: InvoiceFile → Carrier
- **Many-to-One**: InvoiceFile → User (assignedTo)
- **One-to-Many**: InvoiceFile → TrackSheets
- **Integration**: InvoiceFile → Ticket (via workItemId)

#### Data Flow
1. **File Upload**: Files uploaded with metadata and PDF processing
2. **Duplicate Detection**: Server-side validation against existing files
3. **Assignment**: Files assigned to users for processing
4. **Track Sheet Creation**: Track sheets created from invoice files
5. **Status Management**: Files progress through processing stages
6. **Ticket Integration**: Files can be converted to tickets for workflow management

## Feature Specifications

### File Management Features
- **File Upload**: Single and bulk file upload with PDF processing
- **Metadata Management**: File name, date, carrier, page count tracking
- **Assignment System**: User assignment with validation
- **Status Tracking**: Processing status management (in progress, done)
- **Duplicate Detection**: Comprehensive duplicate file prevention

### Advanced Data Table Features
- **AG Grid Integration**: Professional data grid with advanced features
- **Sorting & Filtering**: Multi-column sorting and filtering
- **Pagination**: Efficient large dataset handling
- **Row Selection**: Multi-row selection for bulk operations
- **Custom Cell Renderers**: Specialized cell rendering for different data types
- **Export Capabilities**: Data export functionality

### Bulk Operations Features
- **Bulk Assignment**: Assign multiple files to users simultaneously
- **Bulk Deletion**: Delete multiple files with confirmation
- **Bulk Status Updates**: Update status for multiple files
- **Progress Tracking**: Real-time feedback for bulk operations
- **Error Handling**: Comprehensive error reporting for failed operations

### Integration Features
- **Track Sheet Integration**: Seamless integration with track sheet system
- **Ticket System Integration**: Convert files to tickets for workflow management
- **Carrier Management**: Integration with carrier data
- **User Management**: Integration with user assignment system
- **Permission System**: Role-based access control

### Filtering & Search Features
- **Text Search**: Search across file names and metadata
- **Date Range Filtering**: Filter by creation date or file date
- **Status Filtering**: Filter by processing status
- **User Filtering**: Filter by assigned user
- **Carrier Filtering**: Filter by carrier
- **Advanced Filters**: Combination of multiple filter criteria

## API Integration Patterns

#### File Operations
```typescript
// Create single invoice file
const createInvoiceFile = async (fileData: CreateInvoiceFilePayload) => {
  return await formSubmit('/api/invoice-files', 'POST', fileData);
};

// Create bulk invoice files
const createBulkInvoiceFiles = async (filesData: CreateBulkInvoiceFilePayload) => {
  return await formSubmit('/api/invoice-files/bulk', 'POST', filesData);
};

// Update invoice file
const updateInvoiceFile = async (id: string, updates: Partial<InvoiceFile>) => {
  return await formSubmit(`/api/invoice-files/${id}`, 'PUT', updates);
};
```

#### Bulk Operations
```typescript
// Bulk assign files
const bulkAssignInvoiceFiles = async (assignmentData: {
  fileIds: string[];
  assignedTo: number;
  updatedBy: number;
}) => {
  return await formSubmit('/api/invoice-files/bulk-assign', 'POST', assignmentData);
};

// Bulk delete files
const bulkDeleteInvoiceFiles = async (deletionData: {
  fileIds: string[];
  deletedBy: number;
}) => {
  return await formSubmit('/api/invoice-files/bulk-delete', 'POST', deletionData);
};
```

## Security & Permissions

### Access Control
- **Permission Required**: "manage-invoice-files" for file management
- **Role-Based Access**: Different permissions for different operations
- **User Scoping**: Users can see assigned files or all files based on role
- **Corporation Scoping**: Files scoped to user's corporation

### Data Validation
- **File Validation**: PDF file validation and page counting
- **Metadata Validation**: Comprehensive input validation
- **Duplicate Prevention**: Server-side duplicate detection
- **Assignment Validation**: User assignment validation
- **Permission Checks**: Operation-level permission validation

## Performance Considerations

### Frontend Optimizations
- **AG Grid Optimization**: Efficient data grid rendering and virtual scrolling
- **Component Memoization**: React.memo for expensive components
- **Lazy Loading**: Dynamic imports for heavy components
- **Debounced Search**: Prevents excessive API calls
- **Context Optimization**: Efficient context usage for state management

### Backend Optimizations
- **Database Indexing**: Proper indexes on foreign keys and search fields
- **Query Optimization**: Selective field loading with Prisma includes
- **Pagination**: Efficient large dataset handling
- **Bulk Operations**: Optimized bulk processing
- **Caching Strategy**: Potential for Redis caching

## Error Handling

### Frontend Error Handling
- **Toast Notifications**: User-friendly error messages
- **Loading States**: Visual feedback during operations
- **Form Validation**: Client-side validation before submission
- **Bulk Operation Feedback**: Detailed feedback for bulk operations
- **Graceful Degradation**: Fallback UI for failed operations

### Backend Error Handling
- **Structured Responses**: Consistent error response format
- **HTTP Status Codes**: Appropriate status codes for different scenarios
- **Input Validation**: Comprehensive request validation
- **Database Error Handling**: Proper error catching and logging
- **Duplicate Detection**: Specific error handling for duplicate files

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with Jest/React Testing Library
- **Integration Tests**: File upload and bulk operation testing
- **E2E Tests**: Complete file management workflows
- **AG Grid Testing**: Data table functionality testing

### Backend Testing
- **Unit Tests**: Controller and service testing
- **Integration Tests**: Database operation testing
- **API Tests**: Endpoint testing with various scenarios
- **Bulk Operation Tests**: Comprehensive bulk operation testing

## Future Enhancements

### Planned Features
- **File Preview**: PDF preview functionality
- **Advanced Analytics**: File processing analytics and reporting
- **Automated Processing**: Automated file processing workflows
- **Integration APIs**: Third-party system integrations
- **Mobile Support**: Mobile-friendly file management
- **Advanced Search**: Full-text search with indexing

### Technical Improvements
- **Performance Optimization**: Advanced caching and optimization
- **Scalability**: Microservices architecture for file processing
- **Real-time Updates**: WebSocket support for live updates
- **Advanced Filtering**: Machine learning-based filtering and categorization

## Integration Points

### Track Sheet System
- **Data Flow**: Invoice files create track sheets for processing
- **Metadata Transfer**: File metadata transferred to track sheets
- **Status Synchronization**: Status updates between systems

### Ticketing System
- **Workflow Integration**: Files can be converted to tickets
- **Assignment Integration**: User assignments synchronized
- **Status Tracking**: Processing status tracked across systems

### User Management
- **Assignment System**: Integration with user management
- **Permission System**: Role-based access control
- **Activity Tracking**: User activity and assignment tracking
