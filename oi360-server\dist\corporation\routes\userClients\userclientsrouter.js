"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/userClients/create");
const view_1 = require("../../controllers/userClients/view");
const router = (0, express_1.Router)();
router.post("/create-userclients", create_1.createuserClients);
router.get("/get-all-userclients", view_1.getUserClients);
// router.put(
//   "/update-userclients",
//   updateUserClients
// );
exports.default = router;
//# sourceMappingURL=userclientsrouter.js.map