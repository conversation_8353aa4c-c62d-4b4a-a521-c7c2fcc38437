"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteInvoiceFile = void 0;
const helpers_1 = require("../../../utils/helpers");
const deleteInvoiceFile = async (req, res) => {
    try {
        const { id } = req.params;
        const { deletedBy } = req.body;
        if (!id) {
            return res.status(400).json({
                success: false,
                message: "Invoice file ID is required",
            });
        }
        // Check if the record exists and is not already deleted
        const existingRecord = await prisma.invoiceFile.findUnique({
            where: { id: id },
        });
        if (!existingRecord) {
            return res.status(404).json({
                success: false,
                message: "Invoice file not found",
            });
        }
        if (existingRecord.deletedAt) {
            return res.status(400).json({
                success: false,
                message: "Invoice file is already deleted",
            });
        }
        // Perform soft delete by setting deletedAt and deletedBy
        const deletedInvoiceFile = await prisma.invoiceFile.update({
            where: { id: id },
            data: {
                deletedAt: new Date(),
                deletedBy: deletedBy,
            },
        });
        return res.status(200).json({
            success: true,
            message: "Invoice file deleted successfully",
            data: {
                id: deletedInvoiceFile.id,
                deletedAt: deletedInvoiceFile.deletedAt,
                deletedBy: deletedInvoiceFile.deletedBy,
            },
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.deleteInvoiceFile = deleteInvoiceFile;
//# sourceMappingURL=delete.js.map