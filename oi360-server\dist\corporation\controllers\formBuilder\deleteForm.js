"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteForm = void 0;
const client_1 = require("@prisma/client");
const helpers_1 = require("../../../utils/helpers");
const prisma = new client_1.PrismaClient();
const deleteForm = async (req, res) => {
    try {
        const { formId } = req.params;
        const deletedBy = (req.body && req.body.deletedBy) ?? req.user?.id;
        if (!formId) {
            return res.status(400).json({
                success: false,
                message: "Form ID is required",
            });
        }
        const form = await prisma.form.findUnique({
            where: { id: formId },
        });
        if (!form || form.deletedAt) {
            return res.status(404).json({
                success: false,
                message: `Form not found or already deleted`,
            });
        }
        await prisma.form.update({
            where: { id: formId },
            data: {
                deletedAt: new Date(),
                deletedBy: deletedBy,
            },
        });
        await prisma.formSchema.updateMany({
            where: {
                formId,
                deletedAt: null,
            },
            data: {
                deletedAt: new Date(),
                deletedBy: deletedBy,
            },
        });
        return res.status(200).json({
            success: true,
            message: "Form deleted successfully",
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error, "Failed to delete form");
    }
};
exports.deleteForm = deleteForm;
//# sourceMappingURL=deleteForm.js.map