"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateForm = void 0;
const client_1 = require("@prisma/client");
const helpers_1 = require("../../../utils/helpers");
const prisma = new client_1.PrismaClient();
executeWithRetry: async (operation, maxRetries = 3, delay = 100) => {
    let lastError;
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await operation();
        }
        catch (error) {
            lastError = error;
            if (i < maxRetries - 1) {
                await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
            }
        }
    }
    throw lastError;
};
const updateForm = async (req, res) => {
    try {
        const { formId } = req.params;
        const { name, description, updatedBy, fields } = req.body;
        if (!formId) {
            return res.status(400).json({
                success: false,
                message: "Form ID is required",
            });
        }
        return await prisma.$transaction(async (tx) => {
            const formExists = await tx.form.findFirst({
                where: { id: formId, deletedAt: null },
            });
            if (!formExists) {
                return res.status(404).json({
                    success: false,
                    message: `Form with ID ${formId} not found`,
                });
            }
            const updatedForm = await tx.form.update({
                where: { id: formId },
                data: {
                    ...(name !== undefined && { name }),
                    ...(description !== undefined && { description }),
                    ...(updatedBy !== undefined && { updatedBy: Number(updatedBy) }),
                },
                include: { fields: true },
            });
            if (Array.isArray(fields)) {
                const currentFieldIds = (await tx.formSchema.findMany({
                    where: { formId, deletedAt: null },
                    select: { id: true }
                })).map(f => f.id);
                const updatedFieldIds = [];
                for (const [index, field] of fields.entries()) {
                    try {
                        let fieldType = (field.type || 'text').toUpperCase();
                        if (!Object.values(client_1.FormFieldType).includes(fieldType)) {
                            console.warn(`Invalid field type '${field.type}' provided, defaulting to TEXT`);
                            fieldType = client_1.FormFieldType.TEXT;
                        }
                        const fieldData = {
                            formId,
                            label: field.label,
                            type: fieldType,
                            placeholder: field.placeholder ?? null,
                            required: field.required ?? false,
                            order: field.order ?? index,
                            options: field.options ?? null,
                            settings: field.settings ?? null,
                            createdBy: updatedBy ? Number(updatedBy) : null,
                            updatedBy: updatedBy ? Number(updatedBy) : null,
                        };
                        if (field.id) {
                            await tx.formSchema.update({
                                where: { id: field.id },
                                data: fieldData,
                            });
                            updatedFieldIds.push(field.id);
                        }
                        else {
                            const newField = await tx.formSchema.create({
                                data: fieldData,
                            });
                            updatedFieldIds.push(newField.id);
                        }
                    }
                    catch (fieldError) {
                        console.error('Error processing field:', {
                            field,
                            error: fieldError.message,
                            stack: fieldError.stack,
                            ...(fieldError.code && { code: fieldError.code }),
                            ...(fieldError.meta && { meta: fieldError.meta }),
                        });
                        throw new Error(`Failed to process field: ${field.label || 'Unknown'}. ${fieldError.message}`);
                    }
                }
                const fieldsToDelete = currentFieldIds.filter(id => !updatedFieldIds.includes(id));
                if (fieldsToDelete.length > 0) {
                    await tx.formSchema.updateMany({
                        where: { id: { in: fieldsToDelete } },
                        data: {
                            deletedAt: new Date(),
                            deletedBy: updatedBy ? Number(updatedBy) : null,
                        },
                    });
                }
            }
            const result = await tx.form.findUnique({
                where: { id: formId },
                include: {
                    fields: {
                        where: { deletedAt: null },
                        orderBy: { order: 'asc' }
                    }
                },
            });
            return res.status(200).json({
                success: true,
                message: "Form updated successfully",
                data: result,
            });
        });
    }
    catch (error) {
        console.error('Error in updateForm:', {
            message: error.message,
            stack: error.stack,
            ...(error.code && { code: error.code }),
            ...(error.meta && { meta: error.meta }),
        });
        return (0, helpers_1.handleError)(res, error, "Failed to update form");
    }
    finally {
        await prisma.$disconnect();
    }
};
exports.updateForm = updateForm;
//# sourceMappingURL=updateForm.js.map