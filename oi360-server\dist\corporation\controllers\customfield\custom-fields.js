"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCustomFieldOrder = exports.getClientCustomFieldsByClientId = exports.createClientCustomField = exports.getCustomFieldsWithClients = exports.getCustomFields = exports.createCustomField = exports.mandatoryFields = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
function mapFieldType(type) {
    switch (type.toUpperCase()) {
        case "DATE":
            return "DATE";
        case "NUMBER":
            return "NUMBER";
        case "TEXT":
            return "TEXT";
        case "AUTO":
            return "AUTO";
        default:
            return "TEXT";
    }
}
const mandatoryFields = async (req, res) => {
    try {
        const result = await prisma.$queryRawUnsafe(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'track_sheets'
        AND table_schema = 'public'
    `);
        const excludedFields = ["createdAt", "updatedAt", "id"];
        const fields = result
            .map((col) => col.column_name)
            .filter((field) => !excludedFields.includes(field))
            .map((field) => field
            .split("_")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" "))
            .sort((a, b) => a.localeCompare(b));
        res.json({ mandatoryFields: fields });
    }
    catch (err) {
        console.error("Error fetching TrackSheets columns:", err);
        res.status(500).json({ error: "Failed to get mandatory fields" });
    }
};
exports.mandatoryFields = mandatoryFields;
const createCustomField = async (req, res) => {
    try {
        const { fields } = req.body;
        if (!Array.isArray(fields) || fields.length === 0) {
            return res.status(400).json({ error: "No custom fields provided." });
        }
        const validFields = fields.filter((f) => f.name && f.type);
        if (validFields.length === 0) {
            return res.status(400).json({ error: "No valid custom fields found." });
        }
        const createdFields = [];
        const connectedFieldIds = [];
        let skipped = 0;
        for (const field of validFields) {
            const exists = await prisma.customField.findFirst({
                where: {
                    name: {
                        equals: field.name,
                        mode: "insensitive",
                    },
                },
            });
            if (!exists) {
                const mappedType = mapFieldType(field.type);
                const createData = {
                    name: field.name,
                    type: mappedType,
                    createdBy: field.created_by || "system",
                    updatedBy: field.updated_by || "system",
                };
                if (mappedType === "AUTO") {
                    createData.autoOption = field.autoOption || null;
                }
                const created = await prisma.customField.create({
                    data: createData,
                });
                createdFields.push(created);
                connectedFieldIds.push(created.id);
            }
            else {
                skipped++;
            }
        }
        return res.status(201).json({ created: createdFields, skipped });
    }
    catch (error) {
        console.error("Error creating custom fields:", error);
        return res.status(500).json({ error: "Server error" });
    }
};
exports.createCustomField = createCustomField;
const getCustomFields = async (_req, res) => {
    try {
        const fields = await prisma.customField.findMany({
            orderBy: { createdAt: "desc" },
            include: {},
        });
        res.status(200).json(fields);
    }
    catch (error) {
        console.error("Error fetching custom fields:", error);
        res.status(500).json({ error: "Server error" });
    }
};
exports.getCustomFields = getCustomFields;
const getCustomFieldsWithClients = async (req, res) => {
    try {
        const { page = "1", pageSize = "50", name, type, "Custom Field Name": customFieldName, "Field Type": fieldType, "Client List": clientList } = req.query;
        const pageNumber = parseInt(page);
        const pageSizeNumber = parseInt(pageSize);
        const whereConditions = {};
        if (name || customFieldName) {
            const searchTerm = name || customFieldName;
            whereConditions.name = {
                contains: searchTerm,
                mode: "insensitive"
            };
        }
        const allFields = await prisma.customField.findMany({
            where: whereConditions,
            orderBy: { createdAt: "asc" },
            include: {
                ClientCustomFieldArrangement: {
                    include: {
                        Client: {
                            select: {
                                id: true,
                                client_name: true,
                                ownership: {
                                    select: {
                                        username: true
                                    }
                                },
                                associate: {
                                    select: {
                                        name: true
                                    }
                                },
                                branch: {
                                    select: {
                                        branch_name: true
                                    }
                                }
                            }
                        }
                    }
                }
            },
        });
        let allFieldsWithClients = allFields.map(field => {
            let formattedType;
            if (field.type === "AUTO" && field.autoOption) {
                formattedType = `Auto - ${field.autoOption.charAt(0).toUpperCase() + field.autoOption.slice(1).toLowerCase()}`;
            }
            else if (field.type) {
                formattedType = field.type.charAt(0).toUpperCase() + field.type.slice(1).toLowerCase();
            }
            else {
                formattedType = "Text";
            }
            return {
                id: field.id,
                name: field.name,
                type: field.type,
                formattedType: formattedType,
                autoOption: field.autoOption,
                createdAt: field.createdAt,
                createdBy: field.createdBy,
                updatedAt: field.updatedAt,
                updatedBy: field.updatedBy,
                clients: field.ClientCustomFieldArrangement.map(arrangement => arrangement.Client),
                clientCount: field.ClientCustomFieldArrangement.length
            };
        });
        if (type || fieldType) {
            const searchTerm = (type || fieldType);
            allFieldsWithClients = allFieldsWithClients.filter(field => {
                return field.formattedType.toLowerCase().includes(searchTerm.toLowerCase());
            });
        }
        if (clientList) {
            const searchTerm = clientList.toLowerCase();
            allFieldsWithClients = allFieldsWithClients.filter(field => {
                const clientNames = field.clients.map(client => client.client_name.toLowerCase()).join(", ");
                return clientNames.includes(searchTerm);
            });
        }
        const totalFilteredCount = allFieldsWithClients.length;
        const totalPages = Math.ceil(totalFilteredCount / pageSizeNumber);
        const skip = (pageNumber - 1) * pageSizeNumber;
        const paginatedFields = allFieldsWithClients.slice(skip, skip + pageSizeNumber);
        res.status(200).json({
            data: paginatedFields,
            datalength: totalFilteredCount,
            page: pageNumber,
            pageSize: pageSizeNumber,
            totalPages: totalPages
        });
    }
    catch (error) {
        console.error("Error fetching custom fields with clients:", error);
        res.status(500).json({ error: "Server error" });
    }
};
exports.getCustomFieldsWithClients = getCustomFieldsWithClients;
const createClientCustomField = async (req, res) => {
    try {
        const { client_id, custom_fields } = req.body;
        if (!client_id || !Array.isArray(custom_fields)) {
            return res.status(400).json({ error: "Invalid input format." });
        }
        const foundFields = await prisma.customField.findMany({
            where: { id: { in: custom_fields } },
        });
        if (foundFields.length !== custom_fields.length) {
            return res.status(400).json({ error: "Some custom fields do not exist." });
        }
        const result = await prisma.$transaction(async (tx) => {
            await tx.clientCustomFieldArrangement.deleteMany({
                where: { client_id: Number(client_id) }
            });
            const createdArrangements = [];
            if (custom_fields.length > 0) {
                for (let i = 0; i < custom_fields.length; i++) {
                    const arrangement = await tx.clientCustomFieldArrangement.create({
                        data: {
                            client_id: Number(client_id),
                            custom_field_id: custom_fields[i],
                            order: i + 1,
                        },
                        include: {
                            CustomField: {
                                select: {
                                    id: true,
                                    name: true,
                                    type: true,
                                }
                            }
                        }
                    });
                    createdArrangements.push(arrangement);
                }
            }
            else {
            }
            return createdArrangements;
        });
        res.status(201).json({ success: true, arrangements: result });
    }
    catch (err) {
        console.error("Error saving client custom fields:", err.message || err);
        res.status(500).json({
            error: "Server error",
            details: err.message || err,
        });
    }
};
exports.createClientCustomField = createClientCustomField;
const getClientCustomFieldsByClientId = async (req, res) => {
    const client_id = parseInt(req.params.clientId);
    if (isNaN(client_id)) {
        return res.status(400).json({ error: "Invalid client ID" });
    }
    try {
        const arrangements = await prisma.clientCustomFieldArrangement.findMany({
            where: { client_id: client_id },
            include: {
                CustomField: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                    }
                },
            },
            orderBy: { order: 'asc' },
        });
        if (arrangements.length === 0) {
            return res.status(200).json({ props: [], custom_fields: [] });
        }
        const customFields = arrangements.map(arr => arr.CustomField);
        return res.status(200).json({
            props: [],
            custom_fields: customFields,
        });
    }
    catch (error) {
        console.error("Error fetching client custom fields:", error);
        return res.status(500).json({ error: "Server error" });
    }
};
exports.getClientCustomFieldsByClientId = getClientCustomFieldsByClientId;
const updateCustomFieldOrder = async (req, res) => {
    try {
        const { client_id, custom_fields } = req.body;
        if (!client_id || !Array.isArray(custom_fields)) {
            return res
                .status(400)
                .json({ error: "Missing client_id or invalid custom_fields" });
        }
        const allFields = await prisma.customField.findMany({
            where: {
                id: {
                    in: custom_fields
                }
            }
        });
        const fieldMap = new Map(allFields.map(field => [field.id, field]));
        const validFieldIds = custom_fields.filter(id => fieldMap.has(id));
        if (validFieldIds.length === 0) {
            return res.status(400).json({ error: "No valid custom fields provided" });
        }
        const updatedArrangements = await prisma.$transaction(async (tx) => {
            await tx.clientCustomFieldArrangement.deleteMany({
                where: { client_id: client_id }
            });
            const createdArrangements = [];
            for (let i = 0; i < validFieldIds.length; i++) {
                const arrangement = await tx.clientCustomFieldArrangement.create({
                    data: {
                        client_id: client_id,
                        custom_field_id: validFieldIds[i],
                        order: i + 1,
                    },
                    include: {
                        CustomField: {
                            select: {
                                id: true,
                                name: true,
                                type: true,
                            }
                        }
                    }
                });
                createdArrangements.push(arrangement);
            }
            return createdArrangements;
        });
        res.status(200).json({ success: true, arrangements: updatedArrangements });
    }
    catch (err) {
        console.error("Error updating custom field order:", err);
        res.status(500).json({ error: "Server error", details: err });
    }
};
exports.updateCustomFieldOrder = updateCustomFieldOrder;
//# sourceMappingURL=custom-fields.js.map