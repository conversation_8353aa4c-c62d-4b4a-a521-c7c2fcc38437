"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewInvoiceFilesByUser = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewInvoiceFilesByUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const skip = (page - 1) * limit;
        if (!userId || isNaN(parseInt(userId))) {
            return res.status(400).json({
                success: false,
                message: "Valid user ID is required",
            });
        }
        // Optional filters
        const { carrier, date, fileName } = req.query;
        const whereClause = {
            assignedTo: parseInt(userId),
            deletedAt: null, // Only return non-deleted records
        };
        // Add filters if provided
        if (carrier) {
            whereClause.carrierId = parseInt(carrier);
        }
        if (date) {
            whereClause.date = new Date(date);
        }
        if (fileName) {
            whereClause.fileName = {
                contains: fileName,
                mode: 'insensitive',
            };
        }
        const [invoiceFiles, total] = await Promise.all([
            prisma.invoiceFile.findMany({
                where: whereClause,
                include: {
                    carrier: {
                        select: {
                            name: true
                        }
                    },
                    assignedToUser: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
                skip: skip,
                take: limit,
            }),
            prisma.invoiceFile.count({
                where: whereClause,
            }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return res.status(200).json({
            success: true,
            data: invoiceFiles,
            pagination: {
                currentPage: page,
                totalPages: totalPages,
                totalRecords: total,
                limit: limit,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewInvoiceFilesByUser = viewInvoiceFilesByUser;
//# sourceMappingURL=viewByUser.js.map