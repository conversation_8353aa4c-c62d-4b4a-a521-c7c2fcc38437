{"version": 3, "file": "closureRate.js", "sourceRoot": "", "sources": ["../../../../../src/corporation/controllers/ticket/analytics/closureRate.ts"], "names": [], "mappings": ";;;AAAA,uDAAwD;AAEjD,MAAM,qBAAqB,GAAG,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IAChE,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,CAAC,CAAS,EAAE,EAAE;YACnC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC3D,OAAO,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC;QACF,MAAM,YAAY,GAAG,CAAC,CAAO,EAAE,EAAE;YAC/B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QACF,MAAM,UAAU,GAAG,CAAC,CAAO,EAAE,EAAE;YAC7B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAC5B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QAEF,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,GACV,GAAG,GAAG,CAAC,KAAK,CAAC;QAEd,MAAM,iBAAiB,GAAQ;YAC7B,SAAS,EAAE,IAAI;SAChB,CAAC;QAEF,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,CAAC,EAAE,CAAC;YACxD,iBAAiB,CAAC,SAAS,GAAG,EAAE,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBACb,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3E,CAAC;YACD,IAAI,MAAM,EAAE,CAAC;gBACX,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAClE,iBAAiB,CAAC,IAAI,GAAG;gBACvB,OAAO,EAAE,QAAQ;aAClB,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,iBAAiB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxC,CAAC;QAED,MAAM,gBAAgB,GAAQ,EAAE,CAAC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC;QAC3C,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC;QAC7C,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,KAAK,EAAE;gBACL,GAAG,iBAAiB;gBACpB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI;oBAC9C,MAAM,EAAE;wBACN,IAAI,EAAE,gBAAgB;qBACvB;iBACF,CAAC;aACH;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,aAAa,EAAE,IAAI;qBACpB;oBACD,OAAO,EAAE;wBACP,SAAS,EAAE,KAAK;qBACjB;iBACF;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;yBAC1B;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,eAAe,GAAG,OAAO,CAAC;QAC9B,IAAI,WAAW,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAChF,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1C,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM;oBAAE,OAAO,KAAK,CAAC;gBACnE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC5G,IAAI,CAAC,YAAY;oBAAE,OAAO,KAAK,CAAC;gBAChC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC9E,IAAI,CAAC,KAAK;oBAAE,OAAO,KAAK,CAAC;gBACzB,IAAI,QAAQ,IAAI,KAAK,GAAG,QAAQ;oBAAE,OAAO,KAAK,CAAC;gBAC/C,IAAI,MAAM,IAAI,KAAK,GAAG,MAAM;oBAAE,OAAO,KAAK,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,GAAG,EAKrB,CAAC;QAEL,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC/B,IAAI,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;gBAC5B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACrC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE;wBACtB,OAAO,EAAE,KAAK,CAAC,EAAE;wBACjB,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;wBACxC,UAAU,EAAE,KAAK,CAAC,KAAK;wBACvB,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,EAAE;qBACnC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACnE,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,IAAI,gBAAgB,GAAG,CAAC,CAAC;YACzB,IAAI,gBAAgB,GAAG,CAAC,CAAC;YACzB,IAAI,iBAAiB,GAAG,CAAC,CAAC;YAE1B,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC/B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM;oBAAE,OAAO;gBAErC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC/C,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAClE,CAAC;gBAEF,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;gBACrF,MAAM,eAAe,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;gBAE1F,IAAI,eAAe,EAAE,CAAC;oBACpB,cAAc,EAAE,CAAC;oBAEjB,MAAM,iBAAiB,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC5F,MAAM,cAAc,GAAG,UAAU,GAAG,CAAC,CAAC;oBAEtC,IAAI,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;wBACnD,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;wBAC9D,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,WAAW,CAAC,CAAC;wBAEnF,IAAI,cAAc,EAAE,CAAC;4BACnB,gBAAgB,EAAE,CAAC;4BAEnB,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;4BACrF,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,WAAW,CAAC,CAAC;4BAEnF,IAAI,UAAU,IAAI,cAAc,EAAE,CAAC;gCACjC,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;gCAC5G,gBAAgB,IAAI,WAAW,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gCACnD,iBAAiB,EAAE,CAAC;4BACtB,CAAC;wBACH,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBAC3D,IAAI,YAAY,CAAC,eAAe,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;4BACvD,gBAAgB,EAAE,CAAC;4BAEnB,IAAI,iBAAiB,EAAE,CAAC;gCACtB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;gCACvB,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;gCACpF,gBAAgB,IAAI,WAAW,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gCACnD,iBAAiB,EAAE,CAAC;4BACtB,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,MAAM,kBAAkB,GAAG,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5F,OAAO;gBACL,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,cAAc;gBACd,gBAAgB;gBAChB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;gBAChD,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC9D,QAAQ,EAAE,OAAO;aAClB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAE1D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,aAAa;SACpB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AA5MW,QAAA,qBAAqB,yBA4MhC"}