{"version": 3, "file": "distribution.js", "sourceRoot": "", "sources": ["../../../../../src/corporation/controllers/ticket/analytics/distribution.ts"], "names": [], "mappings": ";;;AAAA,uDAAwD;AAEjD,MAAM,4BAA4B,GAAG,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACvE,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,CAAC,CAAS,EAAE,EAAE;YACnC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC3D,OAAO,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC;QACF,MAAM,YAAY,GAAG,CAAC,CAAO,EAAE,EAAE;YAC/B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QACF,MAAM,UAAU,GAAG,CAAC,CAAO,EAAE,EAAE;YAC7B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAC5B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QAEF,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,GACV,GAAG,GAAG,CAAC,KAAK,CAAC;QAEd,MAAM,iBAAiB,GAAQ;YAC7B,SAAS,EAAE,IAAI;SAChB,CAAC;QAEF,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,CAAC,EAAE,CAAC;YACxD,iBAAiB,CAAC,SAAS,GAAG,EAAE,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBACb,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3E,CAAC;YACD,IAAI,MAAM,EAAE,CAAC;gBACX,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAClE,iBAAiB,CAAC,IAAI,GAAG;gBACvB,OAAO,EAAE,QAAQ;aAClB,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,iBAAiB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxC,CAAC;QAED,MAAM,gBAAgB,GAAQ,EAAE,CAAC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC;QAC3C,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC;QAC7C,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,KAAK,EAAE;gBACL,GAAG,iBAAiB;gBACpB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI;oBAC9C,MAAM,EAAE;wBACN,IAAI,EAAE,gBAAgB;qBACvB;iBACF,CAAC;aACH;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,aAAa,EAAE,IAAI;qBACpB;oBACD,OAAO,EAAE;wBACP,SAAS,EAAE,KAAK;qBACjB;iBACF;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;yBAC1B;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,eAAe,GAAG,OAAO,CAAC;QAC9B,IAAI,WAAW,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAChF,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1C,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM;oBAAE,OAAO,KAAK,CAAC;gBACnE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC5G,IAAI,CAAC,YAAY;oBAAE,OAAO,KAAK,CAAC;gBAChC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC9E,IAAI,CAAC,KAAK;oBAAE,OAAO,KAAK,CAAC;gBACzB,IAAI,QAAQ,IAAI,KAAK,GAAG,QAAQ;oBAAE,OAAO,KAAK,CAAC;gBAC/C,IAAI,MAAM,IAAI,KAAK,GAAG,MAAM;oBAAE,OAAO,KAAK,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEnG,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACpD,KAAK,EAAE;gBACL,UAAU,EAAE;oBACV,EAAE,EAAE,WAAW;iBAChB;gBACD,SAAS,EAAE,IAAI;aAChB;YACD,OAAO,EAAE;gBACP,EAAE,UAAU,EAAE,KAAK,EAAE;gBACrB,EAAE,KAAK,EAAE,KAAK,EAAE;aACjB;SACF,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAM7B,CAAC;QAEL,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;gBACtC,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;gBACxC,UAAU,EAAE,KAAK,CAAC,KAAK;gBACvB,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,EAAE;gBAClC,WAAW,EAAE,CAAC;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC/B,MAAM,GAAG,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACjF,IAAI,GAAG,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtC,MAAM,SAAS,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;gBAC9C,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC;QAC5C,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC7E,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,UAAU,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SAChG,CAAC,CAAC,CAAC;QAEJ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAE9D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC/D,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AArKW,QAAA,4BAA4B,gCAqKvC"}