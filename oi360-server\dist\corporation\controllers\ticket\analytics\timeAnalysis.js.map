{"version": 3, "file": "timeAnalysis.js", "sourceRoot": "", "sources": ["../../../../../src/corporation/controllers/ticket/analytics/timeAnalysis.ts"], "names": [], "mappings": ";;;AAAA,uDAAwD;AAEjD,MAAM,qBAAqB,GAAG,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IAChE,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,CAAC,CAAS,EAAE,EAAE;YACnC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC3D,OAAO,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC;QACF,MAAM,YAAY,GAAG,CAAC,CAAO,EAAE,EAAE;YAC/B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QACF,MAAM,UAAU,GAAG,CAAC,CAAO,EAAE,EAAE;YAC7B,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAC5B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QACF,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,GACV,GAAG,GAAG,CAAC,KAAK,CAAC;QAEd,MAAM,iBAAiB,GAAQ;YAC7B,SAAS,EAAE,IAAI;SAChB,CAAC;QAEF,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,CAAC,EAAE,CAAC;YACxD,iBAAiB,CAAC,SAAS,GAAG,EAAE,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBACb,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3E,CAAC;YACD,IAAI,MAAM,EAAE,CAAC;gBACX,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAClE,iBAAiB,CAAC,IAAI,GAAG;gBACvB,OAAO,EAAE,QAAQ;aAClB,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,iBAAiB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxC,CAAC;QAED,MAAM,gBAAgB,GAAQ,EAAE,CAAC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC;QAC3C,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,KAAK,EAAE;gBACL,GAAG,iBAAiB;gBACpB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI;oBAC9C,MAAM,EAAE;wBACN,IAAI,EAAE,gBAAgB;qBACvB;iBACF,CAAC;aACH;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,aAAa,EAAE,IAAI;qBACpB;oBACD,OAAO,EAAE;wBACP,SAAS,EAAE,KAAK;qBACjB;iBACF;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;yBAC1B;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,eAAe,GAAG,OAAO,CAAC;QAC9B,IAAI,WAAW,IAAI,SAAS,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAChF,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1C,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM;oBAAE,OAAO,KAAK,CAAC;gBACnE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC5G,IAAI,CAAC,YAAY;oBAAE,OAAO,KAAK,CAAC;gBAChC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC9E,IAAI,CAAC,KAAK;oBAAE,OAAO,KAAK,CAAC;gBACzB,IAAI,QAAQ,IAAI,KAAK,GAAG,QAAQ;oBAAE,OAAO,KAAK,CAAC;gBAC/C,IAAI,MAAM,IAAI,KAAK,GAAG,MAAM;oBAAE,OAAO,KAAK,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,eAAe,GAAU,EAAE,CAAC;QAEhC,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;gBAC1D,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;aACvB,CAAC,CAAC;YACH,IAAI,aAAa,EAAE,CAAC;gBAClB,eAAe,GAAG,CAAC,aAAa,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,SAAS,GAAG,IAAI,GAAG,EAAe,CAAC;YACzC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC/B,IAAI,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;oBAC5B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBACrC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBACjC,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YACH,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACpD,MAAM,YAAY,GAAa,EAAE,CAAC;YAElC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC/B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM;oBAAE,OAAO;gBAErC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC/C,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAClE,CAAC;gBAEF,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC5E,IAAI,CAAC,UAAU;oBAAE,OAAO;gBAExB,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC5E,MAAM,cAAc,GAAG,UAAU,GAAG,CAAC,CAAC;gBAEtC,IAAI,OAAa,CAAC;gBAElB,IAAI,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnD,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;oBAC9D,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,WAAW,CAAC,CAAC;oBAEnF,IAAI,cAAc,EAAE,CAAC;wBACnB,OAAO,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;oBAC/C,CAAC;yBAAM,CAAC;wBACN,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;oBACvB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,CAAC;gBAED,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;gBACnF,MAAM,gBAAgB,GAAG,aAAa,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBAE1D,IAAI,gBAAgB,IAAI,CAAC,EAAE,CAAC;oBAC1B,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC;YACvC,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,GAAG,CAAC,CAAC;YAEhB,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;gBACnB,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC;gBAE7E,MAAM,WAAW,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjC,UAAU,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;gBACvE,CAAC;qBAAM,CAAC;oBACN,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACrC,CAAC;gBAED,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;gBACpC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;YACtC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;gBACxC,UAAU,EAAE,KAAK,CAAC,KAAK;gBACvB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;gBAChD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC9C,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG;gBACxC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG;gBACxC,QAAQ,EAAE,OAAO;gBACjB,UAAU;aACX,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAE9D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AA7MW,QAAA,qBAAqB,yBA6MhC"}