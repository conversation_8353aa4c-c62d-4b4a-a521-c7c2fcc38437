"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewInvoiceFile = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewInvoiceFile = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({
                success: false,
                message: "Invoice file ID is required",
            });
        }
        const invoiceFile = await prisma.invoiceFile.findUnique({
            where: {
                id: id,
                deletedAt: null, // Only return non-deleted records
            },
            include: {
                carrier: {
                    select: {
                        name: true
                    }
                },
                assignedToUser: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
            },
        });
        if (!invoiceFile) {
            return res.status(404).json({
                success: false,
                message: "Invoice file not found",
            });
        }
        // Manually fetch created by user if needed
        let createdByUser = null;
        if (invoiceFile.createdBy) {
            createdByUser = await prisma.user.findUnique({
                where: { id: invoiceFile.createdBy },
                select: { id: true, firstName: true, lastName: true, email: true },
            });
        }
        return res.status(200).json({
            success: true,
            data: {
                ...invoiceFile,
                createdByUser,
            },
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewInvoiceFile = viewInvoiceFile;
//# sourceMappingURL=view.js.map