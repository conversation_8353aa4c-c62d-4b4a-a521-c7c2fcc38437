"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllFormsWithFields = void 0;
const client_1 = require("@prisma/client");
const helpers_1 = require("../../../utils/helpers");
const prisma = new client_1.PrismaClient();
const getAllFormsWithFields = async (req, res) => {
    try {
        const forms = await prisma.form.findMany({
            where: {
                deletedAt: null
            },
            include: {
                fields: {
                    where: { deletedAt: null },
                    orderBy: { order: "asc" }
                }
            },
            orderBy: { createdAt: "desc" }
        });
        return res.status(200).json({
            success: true,
            message: "Forms retrieved successfully",
            data: forms
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error, "Failed to retrieve forms");
    }
};
exports.getAllFormsWithFields = getAllFormsWithFields;
//# sourceMappingURL=getAllForms.js.map