"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFormWithFields = void 0;
const client_1 = require("@prisma/client");
const helpers_1 = require("../../../utils/helpers");
const prisma = new client_1.PrismaClient();
const getFormWithFields = async (req, res) => {
    try {
        const { formId } = req.params;
        if (!formId) {
            return res.status(400).json({
                success: false,
                message: "Form ID is required",
            });
        }
        try {
            const form = await prisma.form.findFirst({
                where: {
                    id: formId,
                    deletedAt: null,
                },
                include: {
                    fields: {
                        where: { deletedAt: null },
                        orderBy: { order: "asc" },
                    },
                },
            });
            if (!form) {
                return res.status(404).json({
                    success: false,
                    message: `Form with ID ${formId} not found`,
                });
            }
            return res.status(200).json({
                success: true,
                message: "Form retrieved successfully",
                data: form,
            });
        }
        catch (dbError) {
            console.error('Database error when fetching form:', {
                error: dbError.message,
                stack: dbError.stack,
                code: dbError.code,
                meta: dbError.meta,
            });
            throw dbError; // Re-throw to be caught by outer catch
        }
    }
    catch (error) {
        console.error('Error in getFormWithFields:', {
            message: error.message,
            stack: error.stack,
            ...(error.code && { code: error.code }),
            ...(error.meta && { meta: error.meta }),
        });
        return (0, helpers_1.handleError)(res, error, "Failed to retrieve form");
    }
};
exports.getFormWithFields = getFormWithFields;
//# sourceMappingURL=getForm.js.map