"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAverageTimeByStage = void 0;
const helpers_1 = require("../../../../utils/helpers");
const getAverageTimeByStage = async (req, res) => {
    try {
        const parseLocalDate = (s) => {
            const [y, m, d] = s.split("-").map((v) => parseInt(v, 10));
            return new Date(y, (m || 1) - 1, d || 1);
        };
        const toStartOfDay = (d) => {
            const x = new Date(d);
            x.setHours(0, 0, 0, 0);
            return x;
        };
        const toEndOfDay = (d) => {
            const x = new Date(d);
            x.setHours(23, 59, 59, 999);
            return x;
        };
        const { dateFrom, dateTo, tags, assignedTo, stageId, priority, dueDateFrom, dueDateTo, } = req.query;
        const ticketWhereClause = {
            deletedAt: null,
        };
        if ((dateFrom || dateTo) && !(dueDateFrom || dueDateTo)) {
            ticketWhereClause.createdAt = {};
            if (dateFrom) {
                ticketWhereClause.createdAt.gte = toStartOfDay(parseLocalDate(dateFrom));
            }
            if (dateTo) {
                ticketWhereClause.createdAt.lte = toEndOfDay(parseLocalDate(dateTo));
            }
        }
        if (tags) {
            const tagArray = tags.split(',').map((tag) => tag.trim());
            ticketWhereClause.tags = {
                hasSome: tagArray,
            };
        }
        if (priority) {
            ticketWhereClause.priority = priority;
        }
        const stageWhereClause = {};
        if (assignedTo) {
            stageWhereClause.assignedTo = assignedTo;
        }
        const tickets = await prisma.ticket.findMany({
            where: {
                ...ticketWhereClause,
                ...(Object.keys(stageWhereClause).length > 0 && {
                    stages: {
                        some: stageWhereClause,
                    },
                }),
            },
            include: {
                stages: {
                    include: {
                        pipelineStage: true,
                    },
                    orderBy: {
                        createdAt: 'asc',
                    },
                },
                pipeline: {
                    include: {
                        stages: {
                            orderBy: { order: 'asc' },
                        },
                    },
                },
            },
        });
        let filteredTickets = tickets;
        if (dueDateFrom || dueDateTo) {
            const fromDate = dueDateFrom ? toStartOfDay(parseLocalDate(dueDateFrom)) : null;
            const toDate = dueDateTo ? toEndOfDay(parseLocalDate(dueDateTo)) : null;
            filteredTickets = tickets.filter((ticket) => {
                if (!ticket.currentStageId || !ticket.stages?.length)
                    return false;
                const currentStage = ticket.stages.find((s) => String(s.pipelineStageId) === String(ticket.currentStageId));
                if (!currentStage)
                    return false;
                const dueAt = currentStage.dueAt ? new Date(currentStage.dueAt) : null;
                if (!dueAt)
                    return false;
                if (fromDate && dueAt < fromDate)
                    return false;
                if (toDate && dueAt > toDate)
                    return false;
                return true;
            });
        }
        let stagesToAnalyze = [];
        if (stageId) {
            const specificStage = await prisma.pipelineStage.findUnique({
                where: { id: stageId },
            });
            if (specificStage) {
                stagesToAnalyze = [specificStage];
            }
        }
        else {
            const allStages = new Map();
            filteredTickets.forEach(ticket => {
                if (ticket.pipeline?.stages) {
                    ticket.pipeline.stages.forEach(stage => {
                        allStages.set(stage.id, stage);
                    });
                }
            });
            stagesToAnalyze = Array.from(allStages.values());
        }
        const stageTimeAnalysis = stagesToAnalyze.map(stage => {
            const timesInStage = [];
            filteredTickets.forEach(ticket => {
                if (!ticket.pipeline?.stages)
                    return;
                const ticketStages = ticket.stages.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
                const stageEntry = ticketStages.find(ts => ts.pipelineStageId === stage.id);
                if (!stageEntry)
                    return;
                const stageIndex = ticket.pipeline.stages.findIndex(s => s.id === stage.id);
                const nextStageIndex = stageIndex + 1;
                let endTime;
                if (nextStageIndex < ticket.pipeline.stages.length) {
                    const nextStageId = ticket.pipeline.stages[nextStageIndex].id;
                    const nextStageEntry = ticketStages.find(ts => ts.pipelineStageId === nextStageId);
                    if (nextStageEntry) {
                        endTime = new Date(nextStageEntry.createdAt);
                    }
                    else {
                        endTime = new Date();
                    }
                }
                else {
                    endTime = new Date();
                }
                const timeInStageMs = endTime.getTime() - new Date(stageEntry.createdAt).getTime();
                const timeInStageHours = timeInStageMs / (1000 * 60 * 60);
                if (timeInStageHours >= 0) {
                    timesInStage.push(timeInStageHours);
                }
            });
            const sampleSize = timesInStage.length;
            let averageTime = 0;
            let medianTime = 0;
            let minTime = 0;
            let maxTime = 0;
            if (sampleSize > 0) {
                averageTime = timesInStage.reduce((sum, time) => sum + time, 0) / sampleSize;
                const sortedTimes = [...timesInStage].sort((a, b) => a - b);
                const midIndex = Math.floor(sortedTimes.length / 2);
                if (sortedTimes.length % 2 === 0) {
                    medianTime = (sortedTimes[midIndex - 1] + sortedTimes[midIndex]) / 2;
                }
                else {
                    medianTime = sortedTimes[midIndex];
                }
                minTime = Math.min(...timesInStage);
                maxTime = Math.max(...timesInStage);
            }
            return {
                stageId: stage.id,
                stageName: stage.name || 'Unnamed Stage',
                stageOrder: stage.order,
                averageTime: Math.round(averageTime * 100) / 100,
                medianTime: Math.round(medianTime * 100) / 100,
                minTime: Math.round(minTime * 100) / 100,
                maxTime: Math.round(maxTime * 100) / 100,
                timeUnit: "hours",
                sampleSize,
            };
        });
        stageTimeAnalysis.sort((a, b) => a.stageOrder - b.stageOrder);
        return res.status(200).json({
            success: true,
            data: stageTimeAnalysis,
        });
    }
    catch (error) {
        console.error("Error in getAverageTimeByStage:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getAverageTimeByStage = getAverageTimeByStage;
//# sourceMappingURL=timeAnalysis.js.map