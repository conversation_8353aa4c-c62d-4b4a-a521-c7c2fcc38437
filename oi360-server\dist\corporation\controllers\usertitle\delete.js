"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteUser = void 0;
const operation_1 = require("../../../utils/operation");
const deleteUser = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "userTitle",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "User title has been deleted",
    });
};
exports.deleteUser = deleteUser;
//# sourceMappingURL=delete.js.map