/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffer-indexof-polyfill";
exports.ids = ["vendor-chunks/buffer-indexof-polyfill"];
exports.modules = {

/***/ "(ssr)/./node_modules/buffer-indexof-polyfill/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/buffer-indexof-polyfill/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nvar initBuffer = __webpack_require__(/*! ./init-buffer */ \"(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js\");\n\nif (!Buffer.prototype.indexOf) {\n    Buffer.prototype.indexOf = function (value, offset) {\n        offset = offset || 0;\n\n        // Always wrap the input as a Buffer so that this method will support any\n        // data type such as array octet, string or buffer.\n        if (typeof value === \"string\" || value instanceof String) {\n            value = initBuffer(value);\n        } else if (typeof value === \"number\" || value instanceof Number) {\n            value = initBuffer([ value ]);\n        }\n\n        var len = value.length;\n\n        for (var i = offset; i <= this.length - len; i++) {\n            var mismatch = false;\n            for (var j = 0; j < len; j++) {\n                if (this[i + j] != value[j]) {\n                    mismatch = true;\n                    break;\n                }\n            }\n\n            if (!mismatch) {\n                return i;\n            }\n        }\n\n        return -1;\n    };\n}\n\nfunction bufferLastIndexOf (value, offset) {\n\n    // Always wrap the input as a Buffer so that this method will support any\n    // data type such as array octet, string or buffer.\n    if (typeof value === \"string\" || value instanceof String) {\n        value = initBuffer(value);\n    } else if (typeof value === \"number\" || value instanceof Number) {\n        value = initBuffer([ value ]);\n    }\n\n    var len = value.length;\n    offset = offset || this.length - len;\n\n    for (var i = offset; i >= 0; i--) {\n        var mismatch = false;\n        for (var j = 0; j < len; j++) {\n            if (this[i + j] != value[j]) {\n                mismatch = true;\n                break;\n            }\n        }\n\n        if (!mismatch) {\n            return i;\n        }\n    }\n\n    return -1;\n}\n\n\nif (Buffer.prototype.lastIndexOf) {\n    // check Buffer#lastIndexOf is usable: https://github.com/nodejs/node/issues/4604\n    if (initBuffer(\"ABC\").lastIndexOf (\"ABC\") === -1)\n        Buffer.prototype.lastIndexOf = bufferLastIndexOf;\n} else {\n    Buffer.prototype.lastIndexOf = bufferLastIndexOf;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-indexof-polyfill/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js":
/*!*************************************************************!*\
  !*** ./node_modules/buffer-indexof-polyfill/init-buffer.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("module.exports = function initBuffer(val) {\n  // assume old version\n    var nodeVersion = process && process.version ? process.version : \"v5.0.0\";\n    var major = nodeVersion.split(\".\")[0].replace(\"v\", \"\");\n    return major < 6\n      ? new Buffer(val)\n      : Buffer.from(val);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWluZGV4b2YtcG9seWZpbGwvaW5pdC1idWZmZXIuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NsaWVudC8uL25vZGVfbW9kdWxlcy9idWZmZXItaW5kZXhvZi1wb2x5ZmlsbC9pbml0LWJ1ZmZlci5qcz9kYTEyIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gaW5pdEJ1ZmZlcih2YWwpIHtcbiAgLy8gYXNzdW1lIG9sZCB2ZXJzaW9uXG4gICAgdmFyIG5vZGVWZXJzaW9uID0gcHJvY2VzcyAmJiBwcm9jZXNzLnZlcnNpb24gPyBwcm9jZXNzLnZlcnNpb24gOiBcInY1LjAuMFwiO1xuICAgIHZhciBtYWpvciA9IG5vZGVWZXJzaW9uLnNwbGl0KFwiLlwiKVswXS5yZXBsYWNlKFwidlwiLCBcIlwiKTtcbiAgICByZXR1cm4gbWFqb3IgPCA2XG4gICAgICA/IG5ldyBCdWZmZXIodmFsKVxuICAgICAgOiBCdWZmZXIuZnJvbSh2YWwpO1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js\n");

/***/ })

};
;