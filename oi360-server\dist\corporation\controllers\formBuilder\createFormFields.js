"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFormField = void 0;
const client_1 = require("@prisma/client");
const helpers_1 = require("../../../utils/helpers");
const prisma = new client_1.PrismaClient();
const createFormField = async (req, res) => {
    try {
        const { formId } = req.params;
        const { label, type, placeholder, required, order, options, settings, createdBy, } = req.body;
        if (!formId) {
            return res.status(400).json({
                success: false,
                message: "Form ID is required",
            });
        }
        if (!label || !type) {
            return res.status(400).json({
                success: false,
                message: "Field label and type are required",
            });
        }
        const formExists = await prisma.form.findFirst({
            where: { id: formId, deletedAt: null },
        });
        if (!formExists) {
            return res.status(404).json({
                success: false,
                message: `Form with ID ${formId} not found`,
            });
        }
        const duplicateField = await prisma.formSchema.findFirst({
            where: {
                formId,
                label,
                type: type,
                placeholder: placeholder || null,
                required: !!required,
                order: order ?? 0,
                createdBy: createdBy ? Number(createdBy) : null,
                deletedAt: null,
            },
        });
        if (duplicateField) {
            return res.status(400).json({
                success: false,
                message: "A field with the same details already exists",
            });
        }
        const newField = await prisma.formSchema.create({
            data: {
                formId,
                label,
                type: type,
                placeholder: placeholder || null,
                required: !!required,
                order: order ?? 0,
                options: options ?? null,
                settings: settings ?? null,
                createdBy: createdBy ? Number(createdBy) : null,
            },
        });
        return res.status(201).json({
            success: true,
            message: "Field created successfully",
            data: newField,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error, "Failed to create form field");
    }
};
exports.createFormField = createFormField;
//# sourceMappingURL=createFormFields.js.map