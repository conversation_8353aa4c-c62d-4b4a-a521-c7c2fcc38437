"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deletePipelineStage = void 0;
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const helpers_1 = require("../../../utils/helpers");
const deletePipelineStage = async (req, res) => {
    const id = req.params.id;
    const { deletedBy } = req.body;
    try {
        const existingRecord = await prismaClient_1.default.pipelineStage.findUnique({
            where: { id: id },
        });
        if (!existingRecord) {
            return res
                .status(400)
                .json({ success: false, message: "Pipeline stage not found" });
        }
        // Soft delete by updating deletedAt and deletedBy
        await prismaClient_1.default.pipelineStage.update({
            where: { id: id },
            data: {
                deletedAt: new Date(),
                deletedBy: deletedBy,
            },
        });
        return res.status(200).json({
            success: true,
            message: "Pipeline stage deleted successfully"
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.deletePipelineStage = deletePipelineStage;
//# sourceMappingURL=delete.js.map