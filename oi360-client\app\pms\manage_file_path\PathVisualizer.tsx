"use client";

import React from "react";

type Props = { path: string };

const isPlaceholder = (seg: string) => {
  const hasLower = /[a-z]/.test(seg);
  return !hasLower && /[A-Z]/.test(seg);
};

const isFile = (seg: string) => /\.[a-zA-Z0-9]+$/.test(seg);

const TOKEN_COLORS: Record<string, string> = {
  "ASSOCIATE": "bg-blue-100 text-blue-800 border border-blue-200",
  "CLIENT": "bg-teal-100 text-teal-800 border border-teal-200",
  "CARRIER": "bg-cyan-100 text-cyan-800 border border-cyan-200",
  "INVOICES": "bg-lime-100 text-lime-800 border border-lime-200",
  "YEAR": "bg-purple-100 text-purple-800 border border-purple-200",
  "MONTH": "bg-pink-100 text-pink-800 border border-pink-200",
  "DATE": "bg-orange-100 text-orange-800 border border-orange-200",
  "RECEIVE DATE": "bg-orange-100 text-orange-800 border border-orange-200",
  "FTP FILE NAME": "bg-rose-100 text-rose-800 border border-rose-200",

  "CURRENT_MONTH": "bg-pink-100 text-pink-800 border border-pink-200",
  "CURRENT_YEAR": "bg-purple-100 text-purple-800 border border-purple-200",
  "CURRENT_DATE": "bg-orange-100 text-orange-800 border border-orange-200",
};

const DEFAULT_PLACEHOLDER = "bg-amber-100 text-amber-800 border border-amber-200";
const DEFAULT_DIR = "bg-indigo-100 text-indigo-800 border border-indigo-200";
const FILE_BASE = "bg-emerald-100 text-emerald-800 border border-emerald-200";
const STATIC_TEXT = "bg-gray-100 text-gray-800 border border-gray-200";

const EXT_COLORS: Record<string, string> = {
  ".pdf": "bg-red-100 text-red-800 border border-red-200",
  ".docx": "bg-blue-100 text-blue-800 border border-blue-200",
  ".xlsx": "bg-green-100 text-green-800 border border-green-200",
  ".txt": "bg-slate-100 text-slate-800 border border-slate-200",
};

const chipClass = (
  seg: string,
  kind: "placeholder" | "dir" | "file" | "static",
  ext?: string
): string => {
  if (kind === "file" && ext) {
    return EXT_COLORS[ext.toLowerCase()] || FILE_BASE;
  }

  if (kind === "static") return STATIC_TEXT;

  const key = seg.trim().toUpperCase();
  if (TOKEN_COLORS[key]) return TOKEN_COLORS[key];

  switch (kind) {
    case "placeholder":
      return DEFAULT_PLACEHOLDER;
    case "file":
      return FILE_BASE;
    default:
      return DEFAULT_DIR;
  }
};

export default function PathVisualizer({ path }: Props) {
  const parts = (path || "")
    .split("/")
    .filter(Boolean);

  return (
    <div className="flex flex-wrap items-center gap-1 leading-6">
      {parts.map((seg, idx) => {
        const baseKind: "placeholder" | "dir" | "file" | "static" = isFile(seg)
          ? "file"
          : isPlaceholder(seg)
          ? "placeholder"
          : /[a-z]/.test(seg)
          ? "static" 
          : "dir";

        if (baseKind === "file") {
          const match = seg.match(/^(.*?)(\.[a-zA-Z0-9]+)$/);
          const base = match ? match[1] : seg;
          const ext = match ? match[2] : "";
          return (
            <React.Fragment key={idx}>
              <span
                title={seg}
                className={
                  "px-2 py-0.5 rounded-l-md text-xs font-medium whitespace-nowrap " +
                  chipClass(base, "file")
                }
              >
                {base}
              </span>
              {ext && (
                <span
                  title={ext}
                  className={
                    "px-1.5 py-0.5 rounded-r-md text-xs font-semibold whitespace-nowrap border-l-0 " +
                    chipClass(ext, "file", ext)
                  }
                >
                  {ext}
                </span>
              )}
              {idx < parts.length - 1 && (
                <span className="text-gray-500 select-none px-1 text-lg leading-none font-semibold">/</span>
              )}
            </React.Fragment>
          );
        }

        return (
          <React.Fragment key={idx}>
            <span
              title={seg}
              className={
                "px-2 py-0.5 rounded-md text-xs font-medium whitespace-nowrap " +
                chipClass(seg, baseKind)
              }
            >
              {seg}
            </span>
            {idx < parts.length - 1 && (
              <span className="text-gray-500 select-none px-1 text-lg leading-none font-semibold">/</span>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}
