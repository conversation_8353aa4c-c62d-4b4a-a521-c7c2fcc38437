"use client";
import React, { useState, useEffect, useCallback } from "react";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import Select from "react-select";
import axios from "axios";
import { getAllData } from "@/lib/helpers";
import { client_routes } from "@/lib/routePath";
import { toast } from "sonner";
import { MandatoryFieldsArrangement } from "./MandatoryFieldsArrangement";
import { CustomFieldsArrangement } from "./CustomFieldsArrangement";

type ClientOption = { value: string; label: string };
export type CustomField = { id: string; displayName: string; fieldtype?: string };
export type MandatoryField = {
  fieldtype: any; name: string; displayName: string; span?: number 
};

const ArrangementPage = () => {
  const [clients, setClients] = useState<ClientOption[]>([]);
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [reorderedFields, setReorderedFields] = useState<CustomField[]>([]);
  const [originalFields, setOriginalFields] = useState<CustomField[]>([]);
  const [selectedFieldIds, setSelectedFieldIds] = useState<Set<string>>(
    new Set()
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasReordered, setHasReordered] = useState(false);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        const res = await getAllData(client_routes.GETALL_CLIENT);
        const clientOptions = res?.data.map((client: any) => ({
          value: client.id,
          label: client.client_name,
        }));
        setClients(clientOptions);
      } catch (err) {
        console.error("Failed to fetch clients", err);
      }
    };
    fetchClients();
  }, []);

  const fetchCustomFields = useCallback(async () => {
    if (!selectedClient) return;
    try {
      const res = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/client-custom-fields/${selectedClient}`
      );
      const customFieldsArray = res.data.custom_fields || [];
      console.log("Fetched custom fields:", customFieldsArray);
      console.log("Sample field structure:", customFieldsArray[0]);
      const customFieldObjects: CustomField[] = customFieldsArray.map(
        (field: any) => {
          const name =
            typeof field === "string"
              ? field
              : field.name || field.displayName || field.label || "";

          let fieldtype = "";
          if (typeof field !== "string") {
            if (field.type === "AUTO" && field.autoOption) {
              fieldtype = `auto-${field.autoOption.toLowerCase()}`;
            } else {
              fieldtype = field.type || field.fieldtype || "";
            }
          }

          return {
            id: typeof field === "string" ? field : field.id,
            displayName: name,
            fieldtype,
          };
        }
      );

      setReorderedFields(customFieldObjects);
      setOriginalFields(customFieldObjects);
      setSelectedFieldIds(new Set());
      setHasReordered(false);
    } catch (err) {
      console.error("Failed to fetch custom fields", err);
    }
  }, [selectedClient]);

  useEffect(() => {
    if (selectedClient) {
      fetchCustomFields();
    } else {
      setReorderedFields([]);
      setOriginalFields([]);
      setSelectedFieldIds(new Set());
      setHasReordered(false);
    }
  }, [selectedClient, fetchCustomFields]);

  const handleSubmit = async () => {
    if (!selectedClient) return;
    setIsSubmitting(true);

    const orderedIds = reorderedFields.map((field) => field.id);
    console.log("Saving order:", orderedIds);

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/client-custom-fields/order`,
        {
          client_id: selectedClient,
          custom_fields: orderedIds,
        }
      );
      console.log("Save response:", response.data);
      toast.success("Custom field order updated successfully!");
    } catch (err) {
      console.error("Failed to update custom field order", err);
      toast.error("Failed to save order.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full px-6 pb-12 min-h-screen bg-gradient-to-br from-slate-100 via-gray-50 to-blue-50">
      <div className="h-12 flex items-center">
        <AdminNavBar
          link="/pms/arrange_custom_fields/v1"
          name="Arrange Mandatory/Custom Fields"
        />
      </div>

      <div className="space-y-3 mt-4 max-w-6xl mx-auto text-center relative">
        <div className="relative">
          <h1 className="text-3xl font-extrabold text-gray-900 tracking-tight">
            Arrange Client's Fields
          </h1>

          {selectedClient && (
            <div className="group absolute top-0 right-0">
              <div className="w-9 h-9 bg-white border border-gray-300 hover:border-gray-400 rounded-full flex items-center justify-center cursor-help transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md">
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-gray-500 hover:text-gray-700"
                >
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                  <path
                    d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 17h.01"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>

              <div className="absolute top-0 right-12 w-56 bg-white border border-gray-200 rounded-lg shadow-xl p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-[9999]">
                <div className="absolute top-3 -right-1 w-3 h-3 bg-white border-r border-b border-gray-200 rotate-45"></div>

                <div className="space-y-2">
                  <h4 className="font-semibold text-gray-800 text-sm text-left">
                    Field Arrangement
                  </h4>

                  <div className="text-xs text-gray-600 space-y-1 text-left">
                    <div><strong>Mandatory:</strong> Drag to reposition, hover for span controls</div>
                    <div><strong>Custom:</strong> Drag to reorder, click to select</div>
                    <div><strong>Save:</strong> Click "Save Order" when done</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <p className="text-base text-gray-600 max-w-lg mx-auto">
          Select a client to view and arrange their fields.
        </p>
      </div>

      <div className="h-10 max-w-sm mx-auto mt-6 relative">
        <div className="absolute inset-0 rounded-[1.75rem] bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-75 blur-sm"></div>

        <div className="relative h-full bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-[1.75rem] p-[1px]">
          <div className="h-full rounded-[1.75rem] bg-white shadow-lg">
            <Select
              options={clients}
              onChange={(option) => setSelectedClient(option?.value || null)}
              placeholder="Select a client..."
              isSearchable
              menuPortalTarget={
                typeof document !== "undefined" ? document.body : undefined
              }
              menuPosition="fixed"
              styles={{
                control: (provided) => ({
                  ...provided,
                  backgroundColor: "transparent",
                  border: "none",
                  boxShadow: "none",
                  minHeight: "40px",
                  cursor: "pointer",
                  borderRadius: "1.75rem",
                }),
                container: (provided) => ({
                  ...provided,
                  width: "100%",
                }),
                menu: (provided) => ({
                  ...provided,
                  zIndex: 9999,
                }),
              }}
            />
          </div>
        </div>
      </div>

      {selectedClient && (
        <div className="max-w-7xl mx-auto mt-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 relative">
          <MandatoryFieldsArrangement />

          <CustomFieldsArrangement
            reorderedFields={reorderedFields}
            setReorderedFields={setReorderedFields}
            originalFields={originalFields}
            selectedFieldIds={selectedFieldIds}
            setSelectedFieldIds={setSelectedFieldIds}
            hasReordered={hasReordered}
            setHasReordered={setHasReordered}
          />
        </div>

        {selectedClient && reorderedFields.length > 0 && (
          <div className="mt-8 flex justify-center gap-4">
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className={`
                px-8 py-3 rounded-xl font-semibold transition-all duration-200
                ${
                  isSubmitting
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 hover:scale-105 shadow-lg hover:shadow-xl"
                }
                text-white transform
              `}
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Saving...
                </div>
              ) : (
                "Save Order"
              )}
            </button>
          </div>
        )}
        </div>
      )}
    </div>
  );
};

export default ArrangementPage;
