"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createForm = void 0;
const client_1 = require("@prisma/client");
const helpers_1 = require("../../../utils/helpers");
const prisma = new client_1.PrismaClient();
const createForm = async (req, res) => {
    try {
        const { name, description, createdBy, fields } = req.body;
        if (!name) {
            return res.status(400).json({
                success: false,
                message: "Form name is required",
            });
        }
        if (fields && !Array.isArray(fields)) {
            return res.status(400).json({
                success: false,
                message: "Fields must be an array",
            });
        }
        const existingForm = await prisma.form.findFirst({
            where: {
                name,
                description: description || null,
                createdBy: createdBy ? Number(createdBy) : null
            }
        });
        if (existingForm) {
            return res.status(409).json({
                success: false,
                message: "A form with the same details already exists"
            });
        }
        const newForm = await prisma.form.create({
            data: {
                name,
                description: description || null,
                createdBy: createdBy ? Number(createdBy) : null,
                deletedAt: null,
                fields: fields?.length
                    ? {
                        create: fields.map((f) => ({
                            label: f.label,
                            type: f.type,
                            placeholder: f.placeholder || null,
                            required: !!f.required,
                            order: f.order ?? 0,
                            options: f.options || null,
                            settings: f.settings || null,
                            createdBy: createdBy ? Number(createdBy) : null
                        }))
                    }
                    : undefined
            },
            include: { fields: true }
        });
        return res.status(201).json({
            success: true,
            message: "Form created successfully",
            data: newForm
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error, "Failed to create form");
    }
};
exports.createForm = createForm;
//# sourceMappingURL=createForm.js.map