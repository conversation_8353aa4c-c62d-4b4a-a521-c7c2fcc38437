"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.exportTicketService = void 0;
const helpers_1 = require("../../../utils/helpers");
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const xlsx_1 = __importDefault(require("xlsx"));
const workTypeToTableMapping = {
    trackSheets: "track_sheets",
    invoiceFiles: "invoice_files",
};
const exportTicketService = async (req, res) => {
    try {
        const data = await prismaClient_1.default.ticket.findMany({
            where: { deletedAt: null },
            include: {
                pipeline: { include: { stages: true } },
                stages: { include: { pipelineStage: true } },
                comments: {
                    where: { deletedAt: null },
                    orderBy: { createdAt: "desc" },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        // Collect all unique assignedTo values from ticket.stages only
        const allAssignedTo = new Set();
        data.forEach((ticket) => {
            if (ticket.stages) {
                ticket.stages.forEach((stage) => {
                    if (stage.assignedTo)
                        allAssignedTo.add(stage.assignedTo);
                });
            }
        });
        // Fetch user details for all assignedTo values (by username or id)
        const assignedToArr = Array.from(allAssignedTo);
        const users = assignedToArr.length > 0
            ? await prismaClient_1.default.user.findMany({
                where: {
                    OR: [
                        { username: { in: assignedToArr } },
                        {
                            id: {
                                in: assignedToArr
                                    .filter((v) => !isNaN(Number(v)))
                                    .map((v) => Number(v)),
                            },
                        },
                    ],
                },
                select: {
                    id: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                },
            })
            : [];
        const getUser = (assignedTo) => users.find((u) => u.username === assignedTo || String(u.id) === String(assignedTo));
        let exportRows = [];
        for (const ticket of data) {
            // Enrich pipeline (always present)
            const pipeline = ticket.pipeline;
            // Enrich stages with assignedUser
            const stages = ticket.stages.map((stage) => ({
                ...stage,
                assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
            }));
            // Find current stage: ticket stage where stage.pipelineStageId === ticket.currentStageId
            let currentStage = null;
            if (stages.length > 0) {
                if (ticket.currentStageId) {
                    currentStage = stages.find((s) => s.pipelineStageId === ticket.currentStageId);
                }
                if (!currentStage) {
                    // Fallback: use the last stage in the array
                    currentStage = stages[stages.length - 1];
                }
            }
            // Get TicketTracksheet (always available; ticket cannot exist without tracksheet)
            const table = workTypeToTableMapping[pipeline.workType];
            const query = `select * from ${table} where id = ${ticket.workItemId}`;
            const fetchedRecord = await prismaClient_1.default.$queryRawUnsafe(query);
            const tracksheet = fetchedRecord[0];
            // Enrich tracksheet with client and carrier names
            if (tracksheet.client_id) {
                const client = await prismaClient_1.default.client.findUnique({
                    where: { id: tracksheet.client_id },
                    select: { client_name: true },
                });
                tracksheet.client = client ? client.client_name : tracksheet.client_id;
            }
            if (tracksheet.carrier_id) {
                const carrier = await prismaClient_1.default.carrier.findUnique({
                    where: { id: tracksheet.carrier_id },
                    select: { name: true },
                });
                tracksheet.carrier = carrier ? carrier.name : tracksheet.carrier_id;
            }
            // Only one row per ticket, with details and current stage info
            // Helper to format date as yyyy-mm-dd
            const formatDate = (dateValue) => {
                if (!dateValue)
                    return "";
                const date = new Date(dateValue);
                if (isNaN(date.getTime()))
                    return "";
                const yyyy = date.getFullYear();
                const mm = String(date.getMonth() + 1).padStart(2, "0");
                const dd = String(date.getDate()).padStart(2, "0");
                return `${yyyy}-${mm}-${dd}`;
            };
            // Helper to format date as yyyy-mm-dd hh:mm:ss AM/PM (12-hour format)
            const formatDateTime = (dateValue) => {
                if (!dateValue)
                    return "";
                const date = new Date(dateValue);
                if (isNaN(date.getTime()))
                    return "";
                const yyyy = date.getFullYear();
                const mm = String(date.getMonth() + 1).padStart(2, "0");
                const dd = String(date.getDate()).padStart(2, "0");
                let hh = date.getHours();
                const min = String(date.getMinutes()).padStart(2, "0");
                const ss = String(date.getSeconds()).padStart(2, "0");
                const ampm = hh >= 12 ? "PM" : "AM";
                hh = hh % 12;
                if (hh === 0)
                    hh = 12;
                const hhStr = String(hh).padStart(2, "0");
                return `${yyyy}-${mm}-${dd} ${hhStr}:${min}:${ss} ${ampm}`;
            };
            exportRows.push({
                // Ticket fields
                Title: ticket.title,
                Description: ticket.description,
                Owner: ticket.owner,
                Priority: ticket.priority,
                WorkItemId: ticket.workItemId,
                CreatedAt: formatDateTime(ticket.createdAt),
                CreatedBy: ticket.createdBy,
                UpdatedAt: formatDateTime(ticket.updatedAt),
                UpdatedBy: ticket.updatedBy,
                // Pipeline fields
                PipelineName: pipeline.name,
                PipelineDescription: pipeline.description,
                WorkType: pipeline.workType,
                // Current Stage fields
                CurrentStageName: pipeline.stages.find((s) => s.id === currentStage.pipelineStageId)?.name || currentStage.pipelineStage?.name || "",
                CurrentStageDescription: currentStage.pipelineStage?.description || "",
                AssignedTo: currentStage.assignedUser ? `${currentStage.assignedUser.firstName || ""} ${currentStage.assignedUser.lastName || ""}`.trim() : "",
                DueAt: formatDate(currentStage.dueAt),
                // TicketTracksheet fields (tracksheet is always available)
                Client: tracksheet.client,
                Company: tracksheet.company,
                Division: tracksheet.division,
                Carrier: tracksheet.carrier,
                FTPFileName: tracksheet.ftp_file_name,
                FTPPage: tracksheet.ftp_page,
                FilePath: tracksheet.file_path,
                MasterInvoice: tracksheet.master_invoice,
                Invoice: tracksheet.invoice,
                Bol: tracksheet.bol,
                ReceivedDate: formatDate(tracksheet.received_date),
                InvoiceDate: formatDate(tracksheet.invoice_date),
                ShipmentDate: formatDate(tracksheet.shipment_date),
                InvoiceTotal: tracksheet.invoice_total,
                Currency: tracksheet.currency,
                QtyShipped: tracksheet.qty_shipped,
                QuantityBilledText: tracksheet.quantity_billed_text,
                InvoiceStatus: tracksheet.invoice_status,
                ManualMatching: tracksheet.manual_matching,
                FreightClass: tracksheet.freight_class,
                InvoiceType: tracksheet.invoice_type,
                WeightUnitName: tracksheet.weight_unit_name,
                Savings: tracksheet.savings,
                BillToClient: String(tracksheet.bill_to_client),
                DOCAvailable: tracksheet.doc_available,
                Notes: tracksheet.notes,
                EnteredBy: tracksheet.entered_by,
            });
        }
        const headers = [
            "Title",
            "Description",
            "Owner",
            "Priority",
            "WorkItemId",
            "CreatedAt",
            "CreatedBy",
            "UpdatedAt",
            "UpdatedBy",
            "PipelineName",
            "PipelineDescription",
            "WorkType",
            "CurrentStageName",
            "CurrentStageDescription",
            "AssignedTo",
            "DueAt",
            "Client",
            "Company",
            "Division",
            "Carrier",
            "FTPFileName",
            "FTPPage",
            "FilePath",
            "MasterInvoice",
            "Invoice",
            "Bol",
            "ReceivedDate",
            "InvoiceDate",
            "ShipmentDate",
            "InvoiceTotal",
            "Currency",
            "QtyShipped",
            "QuantityBilledText",
            "InvoiceStatus",
            "ManualMatching",
            "FreightClass",
            "InvoiceType",
            "WeightUnitName",
            "Savings",
            "BillToClient",
            "DOCAvailable",
            "Notes",
            "EnteredBy",
        ];
        const worksheetData = [
            headers,
            ...exportRows.map((row) => headers.map((h) => row[h] ?? "")),
        ];
        const ws = xlsx_1.default.utils.aoa_to_sheet(worksheetData);
        const wb = xlsx_1.default.utils.book_new();
        xlsx_1.default.utils.book_append_sheet(wb, ws, "tickets");
        const excelBuffer = xlsx_1.default.write(wb, { bookType: "xlsx", type: "buffer" });
        if (exportRows.length > 0) {
            res.setHeader("Content-Disposition", "attachment; filename=tickets.xlsx");
            res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            return res.send(excelBuffer);
        }
        return res.status(200).json({ data: [], datalength: 0 });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.exportTicketService = exportTicketService;
//# sourceMappingURL=exportTicketService.js.map