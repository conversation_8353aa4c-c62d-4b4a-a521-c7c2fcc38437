"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateInvoiceFile = void 0;
const helpers_1 = require("../../../utils/helpers");
const updateInvoiceFile = async (req, res) => {
    try {
        const { id } = req.params;
        const { carrier, date, fileName, noOfPages, assignedTo, updatedBy } = req.body;
        if (!id) {
            return res.status(400).json({
                success: false,
                message: "Invoice file ID is required",
            });
        }
        // Check if the record exists and is not deleted
        const existingRecord = await prisma.invoiceFile.findUnique({
            where: { id: id },
        });
        if (!existingRecord || existingRecord.deletedAt) {
            return res.status(404).json({
                success: false,
                message: "Invoice file not found",
            });
        }
        // Prepare update fields (only include provided fields)
        const updateFields = {
            updatedBy: updatedBy,
            updatedAt: new Date(),
        };
        if (carrier !== undefined) {
            updateFields.carrierId = Number(carrier);
        }
        if (date !== undefined) {
            updateFields.date = new Date(date);
        }
        if (fileName !== undefined) {
            updateFields.fileName = fileName.trim();
        }
        if (noOfPages !== undefined) {
            if (typeof noOfPages !== "number" || noOfPages <= 0) {
                return res.status(400).json({
                    success: false,
                    message: "Number of pages must be a positive number",
                });
            }
            updateFields.noOfPages = Number(noOfPages);
        }
        if (assignedTo !== undefined) {
            updateFields.assignedTo = assignedTo;
        }
        // Check for unique constraint if date or fileName is being updated
        if (updateFields.carrier || updateFields.date || updateFields.fileName) {
            const checkCarrier = updateFields.carrier || existingRecord.carrierId;
            const checkDate = updateFields.date || existingRecord.date;
            const checkFileName = updateFields.fileName || existingRecord.fileName;
            const duplicateRecord = await prisma.invoiceFile.findFirst({
                where: {
                    AND: [
                        { id: { not: id } },
                        { carrierId: checkCarrier },
                        { date: checkDate },
                        { fileName: checkFileName },
                        { deletedAt: null },
                    ],
                },
            });
            if (duplicateRecord) {
                return res.status(400).json({
                    success: false,
                    message: "Invoice file with this carrier,date and filename already exists",
                });
            }
        }
        const updatedInvoiceFile = await prisma.invoiceFile.update({
            where: { id: id },
            data: updateFields,
            include: {
                assignedToUser: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
            },
        });
        // Manually fetch updated by user if needed
        let updatedByUser = null;
        if (updatedInvoiceFile.updatedBy) {
            updatedByUser = await prisma.user.findUnique({
                where: { id: updatedInvoiceFile.updatedBy },
                select: { id: true, firstName: true, lastName: true, email: true },
            });
        }
        return res.status(200).json({
            success: true,
            message: "Invoice file updated successfully",
            data: {
                ...updatedInvoiceFile,
                updatedByUser,
            },
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.updateInvoiceFile = updateInvoiceFile;
//# sourceMappingURL=update.js.map